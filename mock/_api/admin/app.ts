import { Request, Response } from 'express';

const waitTime = (time: number = 100) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(true);
        }, time);
    });
};

export default {
    'GET /mock_api/admin/app/list': async (req: Request, res: Response) => {
        await waitTime(1000);
        res.send({
            page: 0,
            size: 10,
            totalPage: 2,
            total: 13,
            data: [
                {
                    createdDate: '2021-05-11T07:47:12.000+00:00',
                    updatedDate: '2021-05-11T07:48:07.000+00:00',
                    id: 19,
                    appCode: 'lsdyna',
                    appName: 'LSDYNA',
                    appType: 'JOB',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://job.app',
                    appWebUrl: 'para-cloud-desktop://job.app',
                    appIconUrl: '/images/app_icons/icon-lsdyna.png',
                    appDesc: 'LSDYNA',
                    appPriority: 100,
                },
                {
                    createdDate: '2021-04-15T08:51:13.000+00:00',
                    updatedDate: '2021-04-26T06:17:42.000+00:00',
                    id: 16,
                    appCode: 'fluent',
                    appName: 'Fluent',
                    appType: 'JOB',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://job.app',
                    appWebUrl: 'para-cloud-desktop://job.app',
                    appIconUrl: '/images/app_icons/icon-fluent.png',
                    appDesc: 'Flunet',
                    appPriority: 100,
                },
                {
                    createdDate: '2021-03-31T07:34:20.000+00:00',
                    updatedDate: '2021-04-23T08:10:34.000+00:00',
                    id: 14,
                    appCode: 'DashBoard',
                    appName: 'DashBoard',
                    appType: 'DEFAULT',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://dashboard',
                    appWebUrl: 'para-cloud-desktop://dashboard',
                    appIconUrl: '/images/app_icons/icon-dashboard.png',
                    appDesc: 'DashBoard',
                    appPriority: 6,
                },
                {
                    createdDate: '2021-03-09T08:59:48.000+00:00',
                    updatedDate: '2021-04-26T06:17:33.000+00:00',
                    id: 12,
                    appCode: 'abaqus',
                    appName: 'Abaqus',
                    appType: 'JOB',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://job.app',
                    appWebUrl: 'para-cloud-desktop://job.app',
                    appIconUrl: '/images/app_icons/icon-abaqus.png',
                    appDesc: 'Abaqus',
                    appPriority: 100,
                },
                {
                    createdDate: '2021-03-04T07:10:44.000+00:00',
                    updatedDate: '2021-04-26T06:17:54.000+00:00',
                    id: 11,
                    appCode: 'admin_console_old',
                    appName: '管理员控制台',
                    appType: 'MANAGE',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://admin.console',
                    appWebUrl: 'para-cloud-desktop://admin.console',
                    appIconUrl: '/images/app_icons/icon-account.png',
                    appDesc: '管理员控制台',
                    appPriority: 1,
                },
                {
                    createdDate: '2021-03-01T06:59:54.000+00:00',
                    updatedDate: '2021-04-26T06:17:26.000+00:00',
                    id: 9,
                    appCode: 'AltairAccess',
                    appName: 'Altair Access',
                    appType: 'DEFAULT',
                    appRenderType: 'new-window',
                    appUrl: 'https://120.253.67.175:4443/pbsworks/login',
                    appWebUrl: 'https://120.253.67.175:4443/pbsworks/login',
                    appIconUrl: '/images/app_icons/icon-altair.png',
                    appDesc: 'Altair Access',
                    appPriority: 100,
                },
                {
                    createdDate: '2021-02-26T08:23:11.000+00:00',
                    updatedDate: '2021-04-26T06:17:23.000+00:00',
                    id: 6,
                    appCode: 'starccm',
                    appName: 'STAR-CCM',
                    appType: 'JOB',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://job.app',
                    appWebUrl: 'para-cloud-desktop://job.app',
                    appIconUrl: '/images/app_icons/icon-starccm.png',
                    appDesc: 'starccm',
                    appPriority: 100,
                },
                {
                    createdDate: '2021-02-24T04:35:03.000+00:00',
                    updatedDate: '2021-04-26T06:17:18.000+00:00',
                    id: 5,
                    appCode: 'CitrixStoreFront',
                    appName: 'Win前后处理',
                    appType: 'DEFAULT',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://storefront.citrix',
                    appWebUrl: 'para-cloud-desktop://storefront.citrix',
                    appIconUrl: '/images/app_icons/icon-pc-windows.png',
                    appDesc: 'Win前后处理',
                    appPriority: 100,
                },
                {
                    createdDate: '2020-09-18T06:35:50.000+00:00',
                    updatedDate: '2021-04-26T06:17:37.000+00:00',
                    id: 13,
                    appCode: '__account_manager__',
                    appName: '账号管理器',
                    appType: 'DEFAULT',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://account_manager',
                    appWebUrl: 'para-cloud-desktop://account_manager',
                    appIconUrl: '/images/app_icons/icon-account.png',
                    appDesc: '账号管理器',
                    appPriority: 2,
                },
                {
                    createdDate: '2020-09-18T06:31:44.000+00:00',
                    updatedDate: '2021-04-26T06:17:11.000+00:00',
                    id: 3,
                    appCode: 'win_scp',
                    appName: 'WinSCP',
                    appType: 'DEFAULT',
                    appRenderType: 'inline-component',
                    appUrl: 'para-cloud-desktop://winscp',
                    appWebUrl: 'para-cloud-desktop://winscp',
                    appIconUrl: '/images/app_icons/icon-winscp.png',
                    appDesc: 'WinSCP',
                    appPriority: 6,
                },
            ],
        });
    },
    'GET /mock_api/admin/app/group/assigned': async (req: Request, res: Response) => {
        await waitTime(1000);
        res.send([]);
    },
    'GET /mock_api/admin/app/user/assigned': async (req: Request, res: Response) => {
        await waitTime(1000);
        res.send([
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 120,
                uid: 'pcadmin',
                appId: 1,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 121,
                uid: 'pcadmin',
                appId: 2,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 122,
                uid: 'pcadmin',
                appId: 3,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 123,
                uid: 'pcadmin',
                appId: 4,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 124,
                uid: 'pcadmin',
                appId: 5,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 125,
                uid: 'pcadmin',
                appId: 6,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 126,
                uid: 'pcadmin',
                appId: 9,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 127,
                uid: 'pcadmin',
                appId: 12,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 128,
                uid: 'pcadmin',
                appId: 13,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 129,
                uid: 'pcadmin',
                appId: 14,
                status: true,
            },
            {
                createdDate: '2021-05-06T06:49:10.000+00:00',
                updatedDate: '2021-05-06T06:49:10.000+00:00',
                id: 130,
                uid: 'pcadmin',
                appId: 16,
                status: true,
            },
        ]);
    },
};
