{"page": 1, "size": 20, "totalPage": 1, "total": 4, "data": [{"createdDate": "2025-07-11T16:04:00.000+00:00", "updatedDate": "2025-07-11T16:04:00.000+00:00", "id": 631, "billTime": "2025-06-30T16:00:00.000+00:00", "startTime": "2025-03-31T16:00:00.000+00:00", "endTime": "2025-08-31T15:59:59.000+00:00", "billingAccountId": 2, "billingAccountName": null, "zone": "LOCAL", "cluster": "LOCAL", "queue": "demoq", "queueType": "CPU", "orderNo": "********-002", "orderId": 132, "orderType": null, "resourceId": 217, "sku": "demoq(按需)", "resourcePricingType": "COMPUTING", "unit": "CORE_HOURS", "expenseType": "NEED", "listPrice": 22.0, "discount": 90.0, "productDiscount": null, "discountPrice": 19.8, "quantity": 0, "listAmount": null, "amount": 1038.972, "cpuTime": *********, "gpuTime": null, "billQuantity": 52.4733, "billListAmount": null, "billAmount": null, "remark": null}, {"createdDate": "2025-07-01T03:15:35.000+00:00", "updatedDate": "2025-07-01T03:15:35.000+00:00", "id": 627, "billTime": "2025-05-31T16:00:00.000+00:00", "startTime": "2025-04-30T16:00:00.000+00:00", "endTime": "2025-07-31T15:59:59.000+00:00", "billingAccountId": 2, "billingAccountName": null, "zone": "Z1", "cluster": "Z1", "queue": "eqae2m-1x", "queueType": "CPU", "orderNo": "********-001", "orderId": 131, "orderType": null, "resourceId": 216, "sku": "eqae2m-1x（按月）", "resourcePricingType": "COMPUTING", "unit": "CORE_HOURS", "expenseType": "MONTH", "listPrice": 80.0, "discount": 80.0, "productDiscount": null, "discountPrice": 64.0, "quantity": 1000, "listAmount": null, "amount": ********.0, "cpuTime": *************, "gpuTime": null, "billQuantity": 720000.0, "billListAmount": null, "billAmount": null, "remark": null}, {"createdDate": "2025-07-01T03:15:35.000+00:00", "updatedDate": "2025-07-01T03:15:35.000+00:00", "id": 630, "billTime": "2025-05-31T16:00:00.000+00:00", "startTime": "2024-12-31T16:00:00.000+00:00", "endTime": "2025-10-31T15:59:59.000+00:00", "billingAccountId": 2, "billingAccountName": null, "zone": null, "cluster": null, "queue": null, "queueType": null, "orderNo": "********-002", "orderId": 137, "orderType": null, "resourceId": 218, "sku": "stro-nashb-m（存储包年）", "resourcePricingType": "STORAGE", "unit": "TERABYTE", "expenseType": "YEAR", "listPrice": 20000.0, "discount": 100.0, "productDiscount": null, "discountPrice": 20000.0, "quantity": 20, "listAmount": null, "amount": 33333.333, "cpuTime": null, "gpuTime": null, "billQuantity": 20.0, "billListAmount": null, "billAmount": null, "remark": null}, {"createdDate": "2025-06-23T16:02:00.000+00:00", "updatedDate": "2025-07-01T03:15:34.000+00:00", "id": 621, "billTime": "2025-05-31T16:00:00.000+00:00", "startTime": "2025-03-31T16:00:00.000+00:00", "endTime": "2025-08-31T15:59:59.000+00:00", "billingAccountId": 2, "billingAccountName": null, "zone": "LOCAL", "cluster": "LOCAL", "queue": "demoq", "queueType": "CPU", "orderNo": "********-002", "orderId": 132, "orderType": null, "resourceId": 217, "sku": "demoq(按需)", "resourcePricingType": "COMPUTING", "unit": "CORE_HOURS", "expenseType": "NEED", "listPrice": 22.0, "discount": 90.0, "productDiscount": null, "discountPrice": 19.8, "quantity": 0, "listAmount": null, "amount": 14958.262, "cpuTime": **********, "gpuTime": null, "billQuantity": 755.4678, "billListAmount": null, "billAmount": null, "remark": null}]}