# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development

- `npm run start:dev` - Start development server with dev environment
- `npm run dev` - Alias for start:dev
- `npm run start` - Start with dev UMI environment
- `npm run start:no-mock` - Start without mock data
- `npm run start:pre` - Start with pre environment
- `npm run start:test` - Start with test environment

### Build and Deploy

- `npm run build` - Build the application using custom build script
- `npm run maxbuild` - Build using UmiJS max build
- `./config-patch/common/make-patch.sh` - Create deployment patch after build

### Code Quality

- `npm run lint:js` - Run ESLint on src directory
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run lint:prettier` - Check Prettier formatting
- `npm run tsc` - TypeScript type checking (no emit)

### Testing

- `npm run test` - Run UmiJS tests
- `npm run test:all` - Run all tests using custom script
- `npm run test:component` - Run component tests only

## Architecture Overview

### Framework and Stack

- **Framework**: UmiJS 4 with Ant Design Pro Components
- **UI Library**: Ant Design 5.19.3
- **State Management**: UmiJS built-in model + React Redux 9.2.0
- **Language**: TypeScript with React 17.0.2
- **Build Tool**: Node.js 20+ required

### Project Structure

- `src/components/` - Reusable UI components organized by feature
- `src/pages/` - Route-based page components
- `src/services/api/` - API service definitions and types
- `src/utils/` - Utility functions and helpers
- `src/locales/` - Internationalization (zh-CN/en-US)
- `config/` - UmiJS configuration files
- `mock/` - Mock data for development

### Key Architectural Patterns

#### Dynamic Menu System

The application uses a dynamic menu system that fetches menu configuration from the server. Menu items are transformed to support tab-based navigation with three levels:

1. Main menu categories
2. Sub-menu pages
3. Tab-based content within pages

#### Internationalization

Built-in i18n support with Chinese (zh-CN) as default and English (en-US) available. Language preference is stored in localStorage.

#### API Architecture

- Uses UmiJS request with custom interceptors
- Base URL and timeout configured via global config
- Automatic empty string to undefined conversion
- Comprehensive error handling with user notifications

#### Authentication Flow

- Login redirect handling
- Password change enforcement
- Role-based access control
- Session timeout management

### Component Organization

Components are feature-grouped (e.g., `admin_user_manage`, `billing_center`, `resource_queue_manage`) rather than by type, making it easier to understand related functionality.

### Development Environment

- Uses hash-based routing for deployment flexibility
- Proxy configuration for multiple API endpoints (ports 10443, 17443, 18443)
- Hot reload with fastRefresh enabled
- Mock data exclusion pattern: `mock/_*/**/*.*`

### Build and Deployment

The project uses a custom build script (`build_script/build.mjs`) alongside UmiJS's built-in build system. Deployment involves creating patch files for incremental updates.
