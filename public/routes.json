[{"menuName": "原计费中心", "path": "/OldBilling", "name": "OldBilling", "routes": [{"menuName": "核时统计", "path": "", "name": "nuclearTimeStatistics", "type": "TABLE"}, {"menuName": "核心统计", "path": "", "name": "coreStatistics", "type": "TABLE"}, {"menuName": "账号统计", "path": "", "name": "accountStatistics", "type": "TABLE"}, {"menuName": "应用统计", "path": "", "name": "applicationStatistics", "type": "TABLE"}, {"menuName": "活跃用户统计", "path": "", "name": "activeUserUsage", "type": "TABLE"}, {"menuName": "作业统计", "path": "", "name": "jobUsage", "type": "TABLE"}, {"menuName": "部门统计", "path": "", "name": "departmentalStatistics", "type": "TABLE"}, {"menuName": "部门统计(按事业部)", "path": "", "name": "departmentalStatisticsBusinessUnit", "type": "TABLE"}, {"menuName": "作业清单", "path": "", "name": "jobList", "type": "TABLE"}, {"menuName": "3DVNC利用率", "path": "", "name": "3DVNC", "type": "TABLE"}, {"menuName": "作业时长分析", "path": "", "name": "jobDuration", "type": "TABLE"}, {"menuName": "充值记录", "path": "", "name": "chargeRecord", "type": "TABLE"}]}, {"menuName": "计费中心", "path": "/Billing", "name": "Billing", "routes": [{"menuName": "产品列表", "path": "/Billing/ProductList", "name": "ProductList"}, {"menuName": "订单列表", "path": "/Billing/OrderList", "name": "OrderList"}, {"menuName": "计费账单", "name": "DeptAccounting", "path": "/Billing/DeptAccounting", "routes": [{"menuName": "本地账单", "name": "LocalBill", "type": "TABLE"}, {"menuName": "云端账单", "name": "CloudBill", "type": "TABLE"}, {"menuName": "计费组管理", "name": "BillingAccount", "type": "TABLE"}]}, {"menuName": "消费明细", "name": "ConsumptionDetail", "path": "/Billing/ConsumptionDetail", "routes": [{"menuName": "消费明细", "name": "Detail", "type": "TABLE"}, {"menuName": "作业清单", "name": "JobList", "type": "TABLE"}, {"menuName": "核时统计", "name": "CpuTimeStatistics", "type": "TABLE"}, {"menuName": "应用统计", "name": "ApplicationStatistics", "type": "TABLE"}]}, {"menuName": "消费总览", "path": "/Billing/BillOverview", "name": "BillOverview"}]}, {"menuName": "同步管理", "path": "/Synchronized", "name": "Synchronized", "routes": [{"menuName": "传输优先级", "path": "/TransferPriority", "name": "TransferPriority", "type": "TABLE"}, {"menuName": "并发配置", "path": "/TransferConcurrent", "name": "TransferConcurrent", "type": "TABLE"}]}, {"menuName": "组织机构", "path": "/Organization", "name": "organization", "routes": [{"menuName": "平台账号", "path": "/Organization/Account", "name": "account"}, {"menuName": "用户账号", "path": "/Organization/User", "name": "user"}, {"menuName": "角色管理", "path": "/Organization/Permission", "name": "Permission", "routes": [{"menuName": "角色管理", "path": "", "name": "Role", "type": "TABLE"}, {"menuName": "权限项", "path": "", "name": "PermissionItem", "type": "TABLE"}, {"menuName": "菜单管理", "path": "", "name": "MenuManage", "type": "TABLE"}]}, {"menuName": "用户部门", "path": "/Organization/Group", "name": "group"}, {"menuName": "自定义组", "path": "/Organization/CustomPayGroup", "name": "CustomPayGroup"}]}, {"menuName": "资源中心", "path": "/Resource", "name": "resource", "routes": [{"menuName": "区域", "path": "/Resource/Zone", "name": "zone"}, {"menuName": "集群", "path": "/Resource/Cluster", "name": "cluster"}, {"menuName": "队列", "path": "/Resource/Queue", "name": "queue"}, {"menuName": "节点", "path": "/Resource/Node", "name": "node"}, {"menuName": "队列资源", "path": "/Resource/QueueResource", "name": "queue_resource"}, {"menuName": "队列账号", "path": "/Resource/QueueAccount", "name": "<PERSON><PERSON><PERSON><PERSON>unt"}, {"menuName": "平台管理", "path": "/Resource/Platform", "name": "platform"}, {"menuName": "许可管理", "path": "/Resource/License", "name": "license"}]}, {"menuName": "应用中心", "path": "/App", "name": "app", "routes": [{"menuName": "应用", "path": "/App/Icon", "name": "icon"}, {"menuName": "版本模板", "path": "/App/Version", "name": "version"}, {"menuName": "应用脚本", "path": "/App/<PERSON><PERSON>t", "name": "script"}, {"menuName": "应用标签", "path": "/App/Tag", "name": "tag"}, {"menuName": "应用集成", "path": "/App/Guide", "name": "guide"}, {"menuName": "回传策略", "path": "/App/PostBackStrategy", "name": "PostBackStrategy"}, {"menuName": "应用队列弹性策略", "path": "/App/QueueElasticityStrategy", "name": "QueueElasticityStrategy"}]}, {"menuName": "任务中心", "path": "/JobTask", "name": "JobTask", "routes": [{"menuName": "作业管理", "path": "/JobTask/Job", "name": "Job", "routes": [{"menuName": "历史记录", "path": "", "name": "history", "type": "TABLE"}, {"menuName": "作业状态", "path": "", "name": "jobStatus", "type": "TABLE"}, {"menuName": "历史作业", "path": "", "name": "history<PERSON>ob", "type": "TABLE"}, {"menuName": "分类监控", "path": "", "name": "classMonitor", "type": "TABLE"}, {"menuName": "排队监控", "path": "", "name": "queueStatistics", "type": "TABLE"}, {"menuName": "配置管理", "path": "", "name": "config", "type": "TABLE"}]}, {"menuName": "会话管理", "path": "/JobTask/ViewJob", "name": "<PERSON><PERSON>ob"}, {"menuName": "申诉管理", "path": "/JobTask/Appeal", "name": "Appeal"}]}, {"menuName": "统计中心", "path": "/Monitors", "name": "monitors", "routes": [{"menuName": "作业统计", "path": "/Monitors/Jobs", "name": "jobs", "routes": [{"menuName": "作业统计('按队列')", "path": "", "name": "queue", "type": "TABLE"}, {"menuName": "作业统计('按平台')", "path": "", "name": "platform", "type": "TABLE"}, {"menuName": "作业统计分析", "path": "", "name": "job_analysis", "type": "TABLE"}, {"menuName": "时刻统计", "path": "", "name": "days", "type": "TABLE"}, {"menuName": "作业数量统计", "path": "", "name": "number_jobs", "type": "TABLE"}, {"menuName": "应用统计", "path": "", "name": "app", "type": "TABLE"}, {"menuName": "集群节点利用率", "path": "", "name": "jobnode", "type": "TABLE"}, {"menuName": "平台节点利用率", "path": "", "name": "platform_percent", "type": "TABLE"}, {"menuName": "时间核心利用率", "path": "", "name": "platform_time_percent", "type": "TABLE"}, {"menuName": "作业状态统计", "path": "", "name": "job_status", "type": "TABLE"}]}, {"menuName": "会话统计", "path": "/Monitors/Session", "name": "session", "routes": [{"menuName": "会话活跃用户", "path": "", "name": "active_user_list", "type": "TABLE"}, {"menuName": "会话统计", "path": "", "name": "session_chart", "type": "TABLE"}, {"menuName": "节点统计", "path": "", "name": "session_node_chart", "type": "TABLE"}, {"menuName": "会话连接次数", "path": "", "name": "session_time_chart", "type": "TABLE"}, {"menuName": "图站统计", "path": "", "name": "node_chart", "type": "TABLE"}, {"menuName": "图站组统计", "path": "", "name": "node_group_chart", "type": "TABLE"}]}, {"menuName": "用户统计", "path": "/Monitors/User", "name": "user", "routes": [{"menuName": "活跃用户列表", "path": "", "name": "ActiveUserList", "type": "TABLE"}, {"menuName": "活跃作业列表", "path": "", "name": "ActiveJobUserList", "type": "TABLE"}, {"menuName": "活跃用户曲线(按天)", "path": "", "name": "ActiveUserTimeChart", "type": "TABLE"}, {"menuName": "活跃用户曲线(按月)", "path": "", "name": "ActiveUserTimeChartMonth", "type": "TABLE"}, {"menuName": "活跃作业用户曲线（按天）", "path": "", "name": "ActiveJobUserTimeChart", "type": "TABLE"}, {"menuName": "活跃作业用户曲线（按部门）", "path": "", "name": "ActiveUserGroupChart", "type": "TABLE"}, {"menuName": "活跃作业用户曲线（按自定义组）", "path": "", "name": "activeUserCurveByCustomGroup", "type": "TABLE"}]}, {"menuName": "月度统计", "path": "/Monitors/Month", "name": "month", "routes": [{"menuName": "核时统计", "path": "", "name": "NuclearTimeStats", "type": "TABLE"}, {"menuName": "存储容量统计", "path": "", "name": "StorageStats", "type": "TABLE"}]}, {"menuName": "多维度统计", "path": "/Monitors/MachineHours", "name": "machineHours", "routes": [{"menuName": "项目/项目节点统计", "path": "", "name": "project-projectNode-Statistics", "type": "TABLE"}, {"menuName": "部门/项目节点统计", "path": "", "name": "group-projectNode-Statistics", "type": "TABLE"}, {"menuName": "用户/项目节点统计", "path": "", "name": "user-projectNode-Statistics", "type": "TABLE"}, {"menuName": "仿真项/项目节点统计", "path": "", "name": "simulationItem-projectNode-Statistics", "type": "TABLE"}, {"menuName": "作业统计", "path": "", "name": "job-statistics", "type": "TABLE"}, {"menuName": "项目统计", "path": "", "name": "ProjectUsage", "type": "TABLE"}]}, {"menuName": "软件许可统计", "path": "/Monitors/SoftwareLicense", "name": "softwareLicense", "routes": [{"menuName": "许可总揽", "path": "", "name": "license_overview", "type": "TABLE"}, {"menuName": "许可利用率", "path": "", "name": "license_utilization", "type": "TABLE"}, {"menuName": "许可使用机时", "path": "", "name": "license_time", "type": "TABLE"}, {"menuName": "许可峰值", "path": "", "name": "license_peak", "type": "TABLE"}]}, {"menuName": "Windows图形节点数据统计", "path": "/Monitors/Windows", "name": "windows", "routes": [{"menuName": "CPU/内存/磁盘监控", "path": "", "name": "queue", "type": "TABLE"}, {"menuName": "IO监控", "path": "", "name": "IO", "type": "TABLE"}]}]}, {"menuName": "运营中心", "name": "Operation", "path": "/Operation", "routes": [{"menuName": "审计", "path": "/Operation/AuditLog", "name": "AuditLog", "routes": [{"menuName": "登陆日志", "path": "", "name": "LoginLog", "type": "TABLE"}, {"menuName": "作业日志", "path": "", "name": "JobLog", "type": "TABLE"}, {"menuName": "操作日志", "path": "", "name": "OperationLog", "type": "TABLE"}]}, {"menuName": "公告管理", "path": "/Operation/Announcement", "name": "Announcement"}]}, {"menuName": "作业标签", "path": "/JobTag", "name": "jobTag", "routes": [{"menuName": "标签组", "path": "/JobTag/Group", "name": "group"}, {"menuName": "统计类型", "path": "/JobTag/Type", "name": "type"}]}, {"menuName": "项目管理", "path": "/Project", "name": "project"}, {"menuName": "计费管理", "name": "Price", "path": "/Price", "routes": [{"menuName": "队列定价", "path": "/Price/Queue", "name": "Queue"}, {"menuName": "阶梯队列定价", "path": "/Price/QueueTiered", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"menuName": "存储定价", "path": "/Price/Storage", "name": "Storage"}, {"menuName": "存储阶梯定价", "path": "/Price/StorageTiered", "name": "StorageTiered"}, {"menuName": "账户类型管理", "path": "/Price/Account", "name": "Account"}, {"menuName": "充值记录", "path": "/Price/ChargeRecord", "name": "ChargeRecord"}, {"menuName": "存储计费", "path": "/Price/StorageBilling", "name": "StorageBilling"}]}, {"menuName": "账单管理", "name": "Bill", "path": "/Bill", "routes": [{"menuName": "用户账单", "name": "Account", "path": "/Bill/Account"}, {"menuName": "用户组账单", "name": "Group", "path": "/Bill/Group"}, {"menuName": "自定义组", "name": "CustomPayGroup", "path": "/Bill/CustomPayGroup"}, {"menuName": "项目账单", "name": "Project", "path": "/Bill/Project"}, {"menuName": "存储账单", "name": "Storage", "path": "/Bill/Storage"}, {"menuName": "作业存储账单", "name": "JobStorage", "path": "/Bill/JobStorage"}]}, {"menuName": "系统管理", "name": "system", "path": "/System", "routes": [{"menuName": "管理员控制台配置", "path": "/System/Admin", "name": "system.admin", "routes": [{"menuName": "图片管理", "path": "", "name": "1", "type": "TABLE"}, {"menuName": "配置管理", "path": "", "name": "2", "type": "TABLE"}]}, {"menuName": "云桌面配置", "path": "/System/Desktop", "name": "desktop", "routes": [{"menuName": "图片管理", "path": "", "name": "1", "type": "TABLE"}, {"menuName": "Web配置", "path": "", "name": "2", "type": "TABLE"}, {"menuName": "客户端配置", "path": "", "name": "3", "type": "TABLE"}]}, {"menuName": "服务端配置", "path": "/System/Service", "name": "system.service", "routes": [{"menuName": "配置管理", "path": "", "name": "1", "type": "TABLE"}]}, {"menuName": "提醒配置", "path": "/System/ReminderManage", "name": "reminderManage"}, {"menuName": "其他配置", "path": "/System/Other", "name": "system.other", "routes": [{"menuName": "日期管理", "path": "", "name": "dateManagement", "type": "TABLE"}, {"menuName": "传输管理", "path": "", "name": "transferManagement", "type": "TABLE"}, {"menuName": "文件应用关联", "path": "", "name": "fileApplicationAssociation", "type": "TABLE"}, {"menuName": "应用路径映射", "path": "", "name": "applicationPathMapping", "type": "TABLE"}]}]}, {"menuName": "监控中心", "name": "MonitorCenter", "path": "/MonitorCenter", "routes": [{"menuName": "节点监控", "path": "/MonitorCenter/Paramon", "name": "<PERSON><PERSON>"}]}, {"menuName": "容器管理", "name": "ContainerManagement", "path": "/ContainerManagement", "routes": [{"menuName": "容器统计", "path": "/ContainerManagement/ContainerStatistics", "name": "ContainerStatistics"}]}, {"menuName": "大屏监控", "name": "bigScreenMonitoring", "path": "/BigScreenMonitoring"}, {"menuName": "关于", "name": "about", "path": "/About", "routes": [{"menuName": "关于", "path": "/About/Versions", "name": "versions"}, {"menuName": "许可证", "path": "/About/License", "name": "License"}]}]