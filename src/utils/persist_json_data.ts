export function loadUserData<T>(
    persistenceType: 'localStorage' | 'sessionStorage',
    persistenceKey: string,
) {
    try {
        const dataStr =
            persistenceType === 'localStorage'
                ? localStorage.getItem(persistenceKey)
                : sessionStorage.getItem(persistenceKey);
        if (dataStr) {
            return JSON.parse(dataStr) as T;
        } else {
            return null;
        }
    } catch (err) {
        console.error('persist_json_data loadUserData fail', err);
        return null;
    }
}

export function saveUserData(
    persistenceType: 'localStorage' | 'sessionStorage',
    persistenceKey: string,
    data: any,
) {
    const dataStr = JSON.stringify(data);
    if (persistenceType === 'localStorage') {
        localStorage.setItem(persistenceKey, dataStr);
    } else {
        sessionStorage.setItem(persistenceKey, dataStr);
    }
}
