import { splitNum } from './time_split_num';

type IChartYAxis = (maxNumber: number) => {
    interval: number;
    baseMax: number;
};

/**
 * 获取图表的y轴分割
 * @param maxNumber 最大值 单位核时｜ 核
 * @returns
 * interval: 间隔
 * baseMax: 基础最大值
 */
export const getChartYAxis: IChartYAxis = (maxNumber: number) => {
    const baseInterval = splitNum(maxNumber / 256) * 256;
    const interval = Math.ceil(maxNumber / baseInterval);
    return {
        interval: baseInterval,
        baseMax: interval * baseInterval,
    };
};
