declare namespace API {
    export enum Lang {
        'zh-CN' = 'zh-cn',
        'en-US' = 'en',
    }

    export interface IApiError {
        code: number | string;
        msg?: string;
    }

    export interface IApiReqDataUserLogin {
        username: string;
        password: string;
        verifyCode?: string;
    }

    export interface IApiReqDataUserSshLogin {
        username: string;
        password?: string;
        prvKey?: string;
        passphrase?: string;
    }

    export interface IApiResUserModel {
        createdDate: string;
        description: string;
        disable: number;
        homeDirectory: string;
        changePassword: boolean;
        id: number;
        role?: IResRole;
        groupRole: 'GROUP_MANAGER' | 'DEFAULT';
        uid: string;
        updatedDate: string;
        username: string;
        permissionList: string[];
        group: IApiResAdminCreateGroupModel | null;
    }

    export interface IApiResAppModel {
        id?: number;
        appCode: string;
        appDesc: string;
        appIconUrl: string;
        appName: string;
        appPriority: number;
        appRenderType: 'inline-component' | null;
        appType: string;
        appUrl: string;
    }

    export interface IApiResFileModel {
        // linux 文件权限字符串 "rw-------"
        chmod: string;
        isDir: boolean;
        lastModified: number;
        name: string;
        // example: "prn:lpcs:sftp::::file:/home/<USER>"
        path: string;
        size: number;
        type: string;
    }

    export interface IApiResFilesUpload {
        md5: string;
    }

    export type IApiResTaskStatus =
        | 'PREP'
        | 'INIT'
        | 'UPLOAD_PEND'
        | 'UPLOADING'
        | 'UPLOAD_SUSPEND'
        | 'SUBMIT_PEND'
        | 'RUN'
        | 'JOB_END'
        | 'DOWNLOAD_INIT'
        | 'DOWNLOAD_PEND'
        | 'DOWNLOADING'
        | 'DOWNLOAD_SUSPEND'
        | 'END'
        | 'ERROR'
        | 'CANCEL'
        | 'UPLOAD_ERROR'
        | 'DOWNLOAD_ERROR';

    export type IApiJobStatus =
        | 'PREP'
        | 'PENDING'
        | 'RUN'
        | 'DONE'
        | 'FAIL'
        | 'CANCELLING'
        | 'CANCEL'
        | 'UNKNOWN';

    export type IApiJobTransferPriority = 'NORMAL' | 'URGENT';

    /**
     * 作业状态
     * @param PREP 准备
     * @param DONE 完成
     * @param PREPARE 准备
     * @param CANCEL 错误
     * @param FAIL 失败
     * @param PENDING 等待
     * @param SUSPENDED 挂起
     * @param RUN 运行
     * @param UNKNOWN 未知
     * @param CANCELLING 取消中
     * @param WAITING 等待
     */
    export type IJobStatus =
        | 'PREP'
        | 'DONE'
        | 'PREPARE'
        | 'CANCEL'
        | 'FAIL'
        | 'PENDING'
        | 'SUSPENDED'
        | 'RUN'
        | 'UNKNOWN'
        | 'CANCELLING'
        | 'WAITING';

    export interface IApiResJobTaskModel {
        activeManualTransfer: boolean;
        accountResourceType: 'GENERATED' | 'LOCAL' | 'PUBLIC' | 'SELF';
        appSubmitType: 'CALCULATE' | 'VIEW';
        appCode: string;
        billingType: IApiResQueueBillingType;
        calculationAccuracy: string;
        changeQueue: boolean;
        cloud: boolean;
        cloudJobId: string;
        cluster: string;
        code: number;
        comment: string;
        cpuTimeRaw: number;
        cputime: number;
        createdDate: string;
        delete: boolean;
        departmentTag: string;
        done: boolean;
        downloadEndDate: string;
        downloadStartDate: string;
        downloadTime: number;
        downloadTransferPriority: IApiJobTransferPriority;
        elasticityQueue: boolean;
        endTime: number;
        exitCode: string;
        fileDelete: number;
        gid: string;
        higher: number;
        jobDesc: string;
        jobId: string;
        jobName: string;
        jobStatus: IJobStatus;
        jobType: 'CMD' | 'GUI';
        jobUser: 'CMD' | 'GUI';
        lazyDeleteFileTime: number;
        localLogPath: string;
        localOutputCluster: string;
        localOutputZone: string;
        localPath: string;
        localPathBase: string;
        localStartTime: number;
        logPath: string;
        mpi: string;
        msg: string;
        nodes: string;
        outPutPath: string;
        outPutPathTmp: string;
        outputFileDoneMatch: string;
        outputFileExcludeDoneMatch: string;
        outputFileExcludeRunMatch: string;
        outputFileRunMatch: string;
        paraUid: string;
        payGid: number;
        payUid: string;
        pbsPlatform: string;
        pendReason: string;
        pendingChangeQueueTime: number;
        pendingTime: number;
        platform: string;
        preStatus: IApiResTaskStatus;
        priority: number;
        priorityOrder: number;
        projectId: number;
        projectName: string;
        queue: string;
        rawJobId: number;
        showSyncStatus: string;
        slots: number;
        slotsRequested: number;
        software: string;
        softwareModule: string;
        startTime: number;
        status: IApiResTaskStatus;
        submitTime: number;
        updateTime?: number;
        totalInputFileSize: number;
        totalOutputFileCount: number;
        totalOutputFileSize: number;
        totalSyncFileCount: number;
        totalSyncFileSize: number;
        uid: string;
        updatedDate: string;
        uploadBasePath: string;
        uploadEndDate: string;
        uploadStartDate: string;
        uploadTime: number;
        uploadTransferPriority: IApiJobTransferPriority;
        version: string;
        walltime: number;
        workDir: string;
        zone: string;
        zoneAccessType: string;
        pendingOrder: number;
        batchId: string;
        batchName: string;
        batchNo: string;
        isBatch: boolean;
        executionTime: number;
        cpuTime: number;
        pendingSlots: number;
        totalSpeed: number;
        percentage: number;
        averageUploadSpeed: number;
        averageDownlondSpeed: number;
        uploadPendingTime: number;
        downlondPendingTime: number;
        jobSecurityLevel: string;
        jobAppealStatusType: string;
        reason: string;
        systemTime: number;
    }

    export interface IApiReqPageParams {
        page?: number;
        size?: number;
        level1s?: string;
        order?: 'ASC' | 'DESC';
        orderBy?: string;
    }

    export interface IApiResPagedDataModel<T> {
        data: T[];
        page: number;
        size: number;
        total: number;
        totalPage: number;
    }

    export interface IApiReqDataJobList {
        appCode?: string[];
        jobStatus?: string;
        order?: string;
        orderBy?: string;
        status?: string;
        showStatus?: string[];
        version?: string;
        page?: number;
        size?: number;
    }

    export interface IApiReqDataFilesLs {
        path: string;
        recursive?: boolean;
    }

    export interface IApiReqDataFilesTail {
        path: string;
        lines?: number;
        start?: number;
    }

    export interface IApiReqDataFilesSearch {
        path: string;
        match: string;
        hide?: boolean;
        likePrefix?: boolean;
        likeSuffix?: boolean;
        link?: boolean;
        recursive?: boolean;
    }

    export interface IApiResStorage {
        cluster: string;
        paths: string[];
        zone: string;
        zoneUser: string;
    }

    export interface IApiResFilesTaskIdEntity {
        taskId: string;
    }

    export interface IApiResFilesTaskStatusInfo {
        current: number;
        msg: string[];
        progress: number;
        status: number;
        taskId: string;
        total: number;
        value: string;
    }

    export interface IApiReqDataJobLog {
        jobId: string;
        path: string;
        lines?: number;
        start?: number;
    }

    export interface IApiResJobFilesSyncStatus {
        /**
         * SYNC_NO: 不同布
         * SYNC_UN: 未同步
         */
        fileSyncStauts:
            | 'SYNC_NO'
            | 'SYNC_UN'
            | 'SYNC_ING'
            | 'SYNC_DONE'
            | 'SYNC_CANCEL'
            | 'SYNC_ERROR'
            | null
            | undefined;
        cloud?: IApiResFileModel;
        local?: IApiResFileModel;
    }

    export interface IApiResVoAppTemplateVersionModel {
        templateVersion: {
            version: string;
            resourceType: string;
        }[];
    }

    export interface IApiResVoAppTemplateModel {
        app: {
            paragraphApp: {
                desc: string;
                fold: boolean;
                params: IApiParagraphAppParams[];
            };
            paragraphCommon: {
                desc: string;
                fold: boolean;
                cmd: IApiParagraphAppParams[];
            };
        };
        appCode: string;
    }

    interface IApiParagraphAppParams {
        check: string;
        defaultValue: string;
        fileTypes: string[];
        hint: string;
        id: string;
        label: string;
        order: string;
        required: boolean;
        type: string;
    }

    interface IApiParagraphCommonCmd {
        defaultValue: string;
        hint: string;
        id: string;
        isHigh: boolean;
        label: string;
        required: boolean;
        type: string;
        userDefault: boolean;
        valueList: string;
    }

    export type IApiResVoAppResourceAccountType = 'LOCAL' | 'PUBLIC' | 'SELF' | 'GENERATED';

    export interface IApiResVoAppResourceModel {
        appCode: string;
        appResourceId: number;
        version: string;
        resourceOrder: number;
        zone: string;
        cluster: string | null;
        queueName: string;
        queue: string;
        name: string;
        cpus: number;
        zoneUser: string | null;
        resourceType: 'paratera' | 'starlight';
        uid: string;
        enableLimit: boolean;
        accountResourceType: IApiResVoAppResourceAccountType;
    }

    export interface IApiFileRenameRequest {
        newname: string;
        path: string;
    }

    export interface IApiResVoAppVersionLimitModel {
        userCores: number;
    }

    export interface IApiReqDataGetAdminGroup {
        searchName?: string;
        gid?: number;
        recursive?: boolean;
    }

    export interface IApiReqDataAdminCreateGroup {
        groupName: string;
        description?: string;
    }

    export interface IApiResAdminCreateGroupModel {
        groupMonthGpuTimeQuota: number;
        balance: number;
        createdDate?: string;
        description?: string;
        groupName: string;
        id: number;
        updatedDate?: string;
        pgid: number;
        def: boolean | null;
        assignable: boolean;
        groupMonthCpuTimeQuota: number;
        allowDownload?: boolean | null;
        balanceType: 'IN_ARREARS' | 'NORMAL';
        groupMangerUid: string;
        totalAmount: number;
        disabled?: boolean;
    }

    export interface IApiReqDataAdminUser extends IApiReqPageParams {
        userScope?: 'ALL' | 'GROUP' | 'NO_GROUP';
        gids?: number[];
        usernameSearch?: string;
        uidSearch?: string;
    }

    export interface IApiReqDataAdminCreateUser {
        username: string;
        gid?: number;
        password: string;
        uid: string;
    }

    export type IApiReqGetAdminAppVersionUserAssignable = IApiReqPageParams &
        IUserSearchParams & {
            appVersionId: number;
        };

    export type IApiReqGetAdminQueueUserAssignable = IApiReqPageParams &
        IUserSearchParams & {
            queueResourceId: number;
        };

    export interface IApiResAdminCreateUserModel {
        groupAdmin: any;
        allowDownload?: boolean | null;
        balance: number;
        balanceType: 'IN_ARREARS' | 'NORMAL';
        consume: number;
        createdDate: string;
        description: string;
        disable: number;
        homeDirectory: string;
        id: number;
        uid: string;
        gid: number | null;
        updatedDate: string;
        username: string;
        bindGroup: boolean;
        role: string;
        roleId: number | null;
        total: number;
        totalAmount: number;
        userId: string;
        userMonthCpuTimeQuota: number;
        userMonthGpuTimeQuota: number;
        userSecurityLevel: string;
    }

    export type IApiResAdminUserList = IApiResPagedDataModel<IApiResAdminCreateUserModel>;

    export interface IApiReqDataAdminUpdateUser {
        username: string;
        gid?: number;
        uid: string;
        email?: string;
    }

    export interface IApiReqDataUpdatePassword {
        uid?: string;
        password?: string;
    }

    export interface IApiReqDataGroupUser {
        gid: number;
        uid: string;
    }

    export interface IApiReqDataRechargeCoretime {
        uid: string;
        coreTime: number;
    }

    export interface IApiReqDataGetRechargeCoretime {
        uid: string;
        size: number;
        page: number;
    }

    export interface IApiResVoUserAppVersionLimitModel {
        appCode: string;
        appName: string;
        appVersion: string;
        appVersionsId: number;
        defultUserCores: number;
        id: number;
        status: boolean;
        totalCores: number;
        uid: string;
        userCores: number;
    }

    export interface IApiReqUserAppVersionLimitParam {
        appVersionsId: number;
        status?: boolean;
        uid: string;
        userCores?: number;
    }

    export interface IApiResPostAdminBillingRechargeModel {
        balance: number;
        consume: number;
    }

    export type IApiResGetAdminBillingRechargeModel =
        IApiResPagedDataModel<IApiResGetAdminBillingRechargeDataModel>;

    export interface IApiResGetAdminBillingRechargeDataModel {
        coreTime: number;
        createdDate: string;
        id: number;
        operator: string;
        uid: string;
        updatedDate: string;
    }

    export interface IApiResAdminUserAppResourceModel {
        appCode: string;
        cluster: string;
        cpus: number;
        createdDate: string;
        id: number;
        name: string;
        queue: string;
        queueName: string;
        queueResourceId: number;
        resourceOrder: number;
        resourceType: 'starlight' | 'paratera';
        status: boolean;
        updatedDate: string;
        version: string;
        zone: string;
        zoneUser: string;
    }

    export interface IApiResVoUserCloudStorage {
        id: number;
        name: string;
        path: string;
        uid: string;
        createdDate: string;
        updatedDate: string;
        zone: string;
    }

    export interface IApiCloudStorage extends IApiResVoUserCloudStorage {
        _path: [string, string];
    }

    export interface IApiCloudAccount {
        account: string;
        accountResourceType: IApiResVoAppResourceAccountType;
        createdDate: string;
        id: number;
        paraUid: string;
        status: boolean;
        uid: string;
        updatedDate: string;
    }

    export interface IApiResVoGroupAppVersionLimitModel {
        appCode: string;
        appName: string;
        appVersion: string;
        appVersionsId: number;
        defultUserCores: number;
        id: number;
        status: boolean;
        totalCores: number;
        gid: string;
        groupCores: number;
    }

    export interface IApiReqGroupAppVersionLimitParam {
        appVersionsId: number;
        status?: boolean;
        gid: number;
        groupCores?: number;
    }

    export interface IAppVersionLimit {
        appCode: string;
        appName: string;
        appVersion: string;
        appVersionsId: number;
        defultUserCores: number;
        id: number;
        status: boolean;
        totalCores: number;
        validId: string;
        cores: number;
    }

    export interface IApiReqDataSshAccount {
        clusterCode: string;
        clusterLoginName: string;
        clusterLoginPass?: string;
        clusterLoginCert?: File;
        clusterCertPass?: string;
        accountLabel?: string;
    }

    export interface IApiResSshAccountVo {
        accountResourceType: IApiResVoAppResourceAccountType;
        accountLabel: string;
        clusterCode: string;
        clusterLoginName: string;
        id: number;
        paraUid: string;
        userId: string;
    }

    export type IApiSshTool = 'WEB_SSH' | 'Putty' | 'WinSCP' | 'XSHELL';

    export interface IApiResSshAccount {
        accountLabel: string;
        clusterCertPass: string;
        clusterCode: string;
        clusterLoginCert: string;
        clusterLoginName: string;
        clusterLoginPass: string;
        createdDate: string;
        id: number;
        tunnelLoginName: string;
        tunnelLoginPass: string;
        updatedDate: string;
        userId: string;
    }

    export interface IApiResSshAccountConnect {
        clusterCode: string;
        clusterLoginCert: string;
        clusterLoginCertPass: string;
        clusterLoginName: string;
        clusterLoginPass: string;
        clusterLoginType: 'PASS' | 'CERT';
        name: string;
        host: string;
        port: number;
    }

    export interface IApiResSshAccountNode {
        id: number;
        name: string;
        host: string;
        port: number;
        pingPort: number;
        clusterCode: string;
        createdDate: string;
        updatedDate: string;
        trafficLoad?: number;
        tool: IApiSshTool;
    }

    export interface IApiResZoneVo {
        clusterCode: string;
        clusterName: string;
        clusterLoginType: 1 | 2;
    }

    export interface IApiAdminLocalZoneClusterListItem {
        appStdOutput: string;
        clusterCode: string;
        collectQueueUser: string;
        defultQueueCancle: string;
        disks: string[];
        lnNode: string[];
        loginType: number;
        name: string;
        schedulingSystem: string;
        zoneCode: string;
    }
    export interface IApiResScAccount {
        assignUserId: string;
        cloud: boolean;
        cloudScId: number;
        clusterCode: string;
        clusterLoginName: string;
        createdDate: string;
        scaId: number;
        status: boolean;
        tunnelLoginName: string;
        updatedDate: string;
        userId: string;
        uid: string;
    }

    export type IApiResPagedScAccountList = IApiResPagedDataModel<IApiResScAccount>;
    export interface IApiResUserPay {
        coreTime: number;
        createdDate: string;
        id: number;
        operator: string;
        uid: string;
        updatedDate: string;
    }
    export interface IApiReqAdminScaccountListParam {
        isCloud?: boolean;
        order?: string;
        orderBy?: string;
        page?: number;
        searchClusterCode?: string;
        searchClusterLoginName?: string;
        size?: number;
        status?: boolean;
        uid?: string;
    }
    export type IApiResPagedUserPayList = IApiResPagedDataModel<IApiResUserPay>;

    export interface IApiResWinAdOuWinAdOu {
        dn: string;
        name: string;
        ou: string;
    }

    export interface IApiReqDataUserAdLogin {
        ou: string;
        username: string;
        password?: string;
    }

    export type IApiResAppRenderType = 'inline-component' | 'website-link' | 'webview';

    export type IApiResAppType = 'JOB' | 'VIEWJOB' | 'DEFAULT' | 'JOBSTREAM';

    export interface PricingStrategies {
        stime: number;
        etime: number;
        pcqm: number;
        strategy: string;
    }
    export interface IApiResAppIconModel {
        appCode: string;
        appDesc: string;
        appIconUrl: string;
        appName: string;
        appPriority: number;
        appRenderType: IApiResAppRenderType;
        appType: IApiResAppType;
        appUrl: string;
        createdDate: string;
        id: number;
        updatedDate: string;
        defShow: boolean;
        appExpands: { nodes: { parentNo: number[]; node: IAppExpand } };
        appTimeSubmitStrategies?: PricingStrategies[];
    }

    export type IApiResAdminAppList = IApiResPagedDataModel<IApiResAppIconModel>;

    export interface IApiReqDataAdminAppList extends IApiReqPageParams {
        tagId?: number;
        appNameSearch?: string;
        appName?: string;
        appType?: string[] | string;
    }

    export interface IApiReqDataCreateApp {
        appCode: string;
        appName: string;
        appIconUrl: string;
        appUrl: string;
        appType: string;
        appRenderType: string;
        defShow: boolean;
        appExpands?: { nodes: { parentNo: number[]; node: IAppExpand } };
        appExpandsJson?: string;
        fastCalculationType: undefined | 'DISABLE' | 'FORCE' | 'NORMAL';
    }

    export interface IApiReqDataUpdateApp extends IApiReqDataCreateApp {
        id: number;
    }

    export interface IAppExpand {
        appCode: string;
        title: string;
        description?: string;
    }

    export interface IApiResAdminAppGroupAssigned {
        enableFastCalculationTimeEnd: number;
        enableFastCalculationTimeStart: number;
        appId: number;
        createdDate: string;
        gid: number;
        id: number;
        status: boolean;
        updatedDate: string;
        fastCalculationType: string | null;
        appTimeSubmitStrategies?: PricingStrategies[];
    }

    export interface IApiResAdminAppUserAssigned {
        enableFastCalculationTimeStart: number;
        enableFastCalculationTimeEnd: number;
        gid: any;
        appId: number;
        createdDate: string;
        id: number;
        status: boolean;
        uid: string;
        updatedDate: string;
        fastCalculationType: string | null;
        appTimeSubmitStrategies?: PricingStrategies[];
    }

    export interface IApiResGetAdminAppAssigned {
        groupApps: IApiResAdminAppGroupAssigned[];
        userApps: IApiResAdminAppUserAssigned[];
    }

    export interface IApiReqPostAdminAppAssign {
        appId: number;
        gids?: number[];
        blackGids?: number[];
        blackUids?: string[];
        recursive?: boolean;
        uids?: string[];
        defaultPendingChangeQueueMinutes?: number;
        enableEditFastCalculationTime?: boolean;
        enableFastCalculationTimeEnd?: number;
        enableFastCalculationTimeStart?: number;
        fastCalculationType?: string;
        appTimeSubmitStrategies?: PricingStrategies[];
    }

    interface IUserSearchParams {
        uidSearch?: string;
        usernameSearch?: string;
    }

    export type IApiReqGetAdminAppUserAssignable = IApiReqPageParams &
        IUserSearchParams & {
            appId: number;
        };

    export interface IApiResAppTagModel {
        id: number;
        tagCode: string;
        tagName: string;
        createdDate: string;
        updatedDate: string;
    }

    export type IApiResAppTagList = IApiResPagedDataModel<IApiResAppTagModel>;

    export interface IApiReqDataAdminTagsPage extends IApiReqPageParams {
        appCode?: string;
    }

    export interface IApiReqDataUpdateAppTag {
        tagCode: string;
        tagName: string;
    }

    export interface IApiReqDataCreateAppTag extends IApiReqDataUpdateAppTag {
        id: number;
    }

    export interface IApiReqDataAdminAppVersionList extends IApiReqPageParams {
        appCode?: string;
    }

    export interface IApiResAppVersionModel {
        enableSscElasticity: boolean;
        licenseAppModels: string[];
        id: number;
        appCode: string;
        appVersion: string;
        appIconUrl: string;
        appName: string;
        singleJobMaxCores: number;
        createdDate: string;
        updatedDate: string;
        appSubmitType: 'CALCULATE' | 'VIEW';
        type: 'GPU' | 'CPU';
        appVersionOrder: number;
        licenseAppCode: string;
    }

    export type IApiResAppVersionList = IApiResPagedDataModel<IApiResAppVersionModel>;

    export interface IApiReqDataPostAppVersion {
        appCode: string;
        appVersion: string;
        appTemplateText: string;
        appSubmitType?: 'CALCULATE' | 'VIEW';
        singleJobMaxCores?: number;
        appVersionOrder?: number;
    }

    export interface IApiReqDataAppVersionAssign {
        appVersionId: number;
        blackGids?: number[];
        blackUids?: string[];
        gids?: number[];
        uids?: string[];
        recursive?: boolean;
    }

    export interface IApiResAppVersionAssignedGroups {
        id: number;
        appVersionId: number;
        gid: number;
        status: boolean;
        groupName: string;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiResAppVersionAssignedUsers {
        id: number;
        appVersionId: number;
        uid: string;
        status: boolean;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiResAppVersionAssigned {
        gruopAppVersions: IApiResAppVersionAssignedGroups[];
        userAppVersions: IApiResAppVersionAssignedUsers[];
    }

    export interface IApiReqDataDeleteAppVersionAssigned {
        groupAppVersionIds?: number[];
        userAppVersionIds?: number[];
    }

    export interface IApiReqDataPostAppVersionQueue {
        version: string;
        appCode: string;
        qeueueResourceId: number;
    }

    export interface IApiResAppVersionQueue {
        id: number;
        aliasName: string;
        appCode: string;
        appVersionId: number;
        changeQueue: boolean;
        cluster: string;
        cpus: number;
        defaultPendingChangeQueueMinutes: number;
        elasticityQueue: boolean;
        elasticityQueues: string;
        version: string;
        zone: string;
        zoneUser: string;
        name: string;
        platform: string;
        queue: string;
        queueInfo: string;
        queueName: string;
        queueResourceId: number;
        startDate: string;
        endDate: string;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiReqDataAdminAppScriptList extends IApiReqPageParams {
        appCode?: string;
    }

    export interface IApiResAppScriptModel {
        id: number;
        appCode: string;
        appIconUrl: string;
        appName: string;
        zone: string;
        cluster: string;
        version: string;
        queue?: string;
        user?: string;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiResAppScriptContent {
        appScript: string;
        envScript: string;
        submitScript: string;
    }

    export type IApiResAppScriptList = IApiResPagedDataModel<IApiResAppScriptModel>;

    export interface IApiReqDataPostAppScript {
        appCode: string;
        zone: string;
        cluster: string;
        version: string;
        queue?: string;
        user?: string;
        appScript: string;
        envScript: string;
        submitScript: string;
    }

    export type IApiResQueueBillingType = 'FIXED_LEASE' | 'FIXED_YEAR_LEASE' | 'JOB_CORES' | 'NONE';

    export interface IApiResQueueModel {
        id: number;
        name: string;
        cluster: string;
        queue: string;
        queueName: string;
        zone: string;
        zoneUser?: string;
        cpus: number;
        defaultPendingChangeQueueMinutes: number;
        elasticityQueues: string;
        elasticityQueueResources: IApiResQueueElasticityList;
        elasticityQueueResourceParams: IApiResQueueElasticityList;
        createdDate: string;
        updatedDate: string;
        queueOrder: number;
    }
    export interface IApiResQueueElasticityModel {
        cluster: string;
        queueName: string;
        zone: string;
        zoneUser: string;
    }

    export type IApiResQueueElasticityList = IApiResQueueElasticityModel[];

    export interface IApiReqPutAdminQueueElasticity {
        elasticityQueueResourceParams: IApiResQueueElasticityList;
        id: number;
    }

    export interface IApiReqAdminQueueList extends IApiReqPageParams {
        id?: number;
        zone?: string;
        cluster?: string;
        queueNameSearch?: string;
    }

    export type IApiResAdminQueueList = IApiResPagedDataModel<IApiResQueueModel>;

    export interface IApiReqPostAdminQueue {
        id: number;
        gpus: any;
        queueType: string;
        queue: string;
        name: string;
        zone: string;
        cluster: string;
        queueName: string;
        zoneUser?: string;
        cpus: number;
        elasticity?: boolean;
        billingType: IApiResQueueBillingType;
        elasticityQueueResourceParams: IApiResQueueElasticityList;
    }

    export interface IApiReqPutAdminQueue extends IApiReqPostAdminQueue {
        id: number;
    }

    export interface IApiResAdminQueueAssignedGroup {
        id: number;
        queueResourceId: number;
        gid: number;
        status: boolean;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiResAdminQueueAssignedUser {
        id: number;
        queueResourceId: number;
        uid: string;
        status: boolean;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiResAdminQueueUserAssigned {
        gruopQueueResources: IApiResAdminQueueAssignedGroup[];
        userQueueResources: IApiResAdminQueueAssignedUser[];
    }

    export interface IApiReqPostAdminQueueAssign {
        queueResourceId: number;
        blackGids?: number[];
        blackUids?: string[];
        recursive?: boolean;
        gids?: number[];
        uids?: string[];
    }

    export type IApiResZoneAccessType = 'LOCAL_AREA_NETWORK' | 'INTERNET' | 'PARA_GATEWAY';

    export interface IApiResZoneModel {
        id: number;
        zoneAccessType: IApiResZoneAccessType;
        zoneCode: string;
        zoneName: string;
        zoneLoginType: 1 | 2;
        cloud: boolean;
        hide: boolean;
        createdDate: string;
        updatedDate: string;
    }

    export type IApiResZoneList = IApiResPagedDataModel<IApiResZoneModel>;

    export interface IApiReqGetAdminZoneList extends IApiReqPageParams {
        hide?: boolean;
        cloud?: boolean;
    }

    export interface IApiReqPostAdminZone {
        zoneAccessType: IApiResZoneAccessType;
        zoneCode: string;
        zoneName: string;
        zoneLoginType: 1 | 2;
    }

    export interface IApiReqPutAdminZone extends IApiReqPostAdminZone {
        id: number;
    }

    export type IApiResSchedulingSystem = 'PBS' | 'SLURM' | 'LSF';

    export interface IApiResClusterModel {
        zoneName: string;
        zoneAlias: any;
        id: number;
        clusterCode: string;
        zoneCode: string;
        appStdOutput: string;
        changeQueueCommand?: string;
        collectUser: string;
        jobCancelCommand: string;
        jobPriorityCommand?: string;
        jobResumeCommand?: string;
        jobSuspendCommand?: string;
        schedulingSystem: IApiResSchedulingSystem;
        createdDate: string;
        updatedDate: string;
    }

    export type IApiResClusterList = IApiResPagedDataModel<IApiResClusterModel>;

    export interface IApiReqPostAdminCluster {
        clusterCode: string;
        zoneCode: string;
        schedulingSystem: IApiResSchedulingSystem;
        appStdOutput: string;
        collectUser: string;
        jobCancelCommand: string;
        changeQueueCommand?: string;
        jobPriorityCommand?: string;
        jobResumeCommand?: string;
        jobSuspendCommand?: string;
    }

    export interface IApiReqGetAdminClusterList extends IApiReqPageParams {
        zoneCode?: string;
    }

    export interface IApiReqPutAdminCluster extends IApiReqPostAdminCluster {
        id?: number;
    }

    export type IApiConstantAdminZoneClusterDiskType =
        | 'WORK_DIR'
        | 'GLOBAL_DIR'
        | 'BACKUP_DIR'
        | 'DEPARTMENT_DIR'
        | 'TEMP_DIR';

    export interface IApiReqGetAdminZoneClusterDiskList {
        type?: IApiConstantAdminZoneClusterDiskType;
        cluster?: string;
        zone?: string;
    }

    export interface IApiResGetAdminZoneClusterDiskModel {
        id: number;
        name: string;
        zone: string;
        cluster: string;
        type: IApiConstantAdminZoneClusterDiskType;
        path: string;
        order: number;
        createdDate: string;
        updatedDate: string;
    }

    export type IApiResGetAdminZoneClusterDiskList = IApiResGetAdminZoneClusterDiskModel[];

    export interface IApiReqPostAdminZoneClusterDisk {
        cluster: string;
        name: string;
        order?: number;
        path: string;
        type: IApiConstantAdminZoneClusterDiskType;
        zone: string;
    }

    export interface IApiReqPutAdminZoneClusterDisk extends IApiReqPostAdminZoneClusterDisk {
        id: number;
    }

    export type IApiResSshNodeTool = 'WEB_SSH' | 'Putty' | 'WinSCP' | 'XSHELL' | 'KNOP' | 'SSH';

    export interface IApiResSshNodeModel {
        id: number;
        clusterCode: string;
        zoneCode: string;
        host: string;
        port: number;
        pingPort?: number;
        tool: IApiResSshNodeTool;
        name?: string;
        createdDate: string;
        updatedDate: string;
    }

    export type IApiResSshNodeList = IApiResPagedDataModel<IApiResSshNodeModel>;

    export interface IApiReqGetAdminSshNodeList extends IApiReqPageParams {
        clusterCode?: string;
        zoneCode?: string;
    }

    export interface IApiReqPostAdminSshNode {
        clusterCode: string;
        zoneCode: string;
        host: string;
        port: number;
        pingPort?: number;
        tool: IApiResSshNodeTool;
        name?: string;
    }

    export interface IApiReqPutAdminSshNode extends IApiReqPostAdminSshNode {
        id: number;
    }

    export interface IApiReqGetAdminSshAccount {
        uid?: string;
        enableAccess?: boolean;
        clusterCode?: string;
    }

    export interface IApiResGetAdminSshAccount {
        accountLabel: string;
        // clusterCertPass: string;
        clusterCode: string;
        clusterLoginCert?: string;
        clusterLoginName: string;
        clusterLoginPass?: string;
        createdDate: string;
        enableAccess: boolean;
        id: number;
        sshAccountType: 'SELF';
        tunnelLoginName?: string;
        tunnelLoginPass?: string;
        updatedDate: string;
        userId: string;
    }

    export interface IApiReqPostAdminSshAccount {
        accountLabel?: string;
        clusterCertPass?: string;
        clusterCode: string;
        clusterLoginCert?: string;
        clusterLoginName: string;
        clusterLoginPass?: string;
        uid: string;
        enableAccess: boolean;
    }

    export interface IApiReqDeleteAdminSshAccount {
        uid: string;
        clusterCode: string;
        clusterLoginName: string;
    }

    export interface IApiQueueInfoModel {
        queueType: 'GPU' | 'CPU';
        billingType: IApiResQueueBillingType;
        cluster: string;
        cpus: number;
        queuePrice: any;
        createdDate: string;
        elasticity: boolean;
        enable: boolean;
        endDate: string;
        id: number;
        name: string;
        platform: string;
        queue: string;
        queueInfo: string;
        reserve: boolean;
        startDate: string;
        updatedData: string;
        zone: string;
        disableDashBoardShow: boolean;
        route: boolean;
        isCloud: boolean;
    }

    export type IApiResGetAdminQueueInfoList = IApiResPagedDataModel<IApiQueueInfoModel>;

    export interface IApiPutAdminQueueInfo {
        billintType: string;
        id: number;
        name: string;
        platform: string;
        queueInfo: string;
    }

    export interface IApiPutAdminQueueInfoBatch {
        elasticity: boolean;
        ids: number[];
    }

    export interface IApiReqGetAdminQueueInfoList extends IApiReqPageParams {
        enable?: boolean;
        cpus?: number;
        elasticity?: boolean;
        cluster?: string;
        zones?: string[];
        zone?: string;
        queueName?: string;
        nameSearch?: string;
        billingType?: string;
    }
    export interface IApiPutAdminQueueInfoUpdatename {
        cluster: string;
        name: string;
        queue: string;
        zone: string;
    }

    export interface IApiReqGetAdminJobList {
        appCode?: string[];
        billingType?: IApiResQueueBillingType;
        containsDelete?: boolean;
        createdEnd?: string;
        createdStart?: string;
        jobStatus?: string[];
        jobUser?: string[];
        order?: 'ASC' | 'DESC';
        orderBy?: string;
        page?: number;
        scope?: 'SELF' | 'GROUP' | 'ALL';
        showSyncStatus?: string[];
        size?: number;
        status?: string[];
        version?: string[];
        zone?: string[];
    }

    export type IApiResGetAdminJobList = IApiResPagedDataModel<IApiResJobTaskModel>;

    export interface IApiResDcvNodeModel {
        adminName: string;
        createdDate: string;
        dcvDesktopGroupId: number;
        dcvDesktopGroupName: string;
        id: number;
        ip: string;
        nodeName: string;
        osType: string;
        port: number;
        updatedDate: string;
    }

    export interface IApiReqPutDcvNode {
        dcvDesktopGroupId: number;
        id: number;
        nodeName: string;
        osType: string;
    }

    export interface IApiReqPutDcvConn {
        adminName: string;
        adminPassword: string;
        id: number;
        ip: string;
        port: number;
    }

    export interface IApiReqPostDcvNode extends IApiReqPutDcvNode {
        adminName: string;
        adminPassword: string;
        ip: string;
        port: number;
    }

    export interface IApiDcvGroupModel {
        cpuThreshold: number;
        createdDate: string;
        id: number;
        maxServerConn: number;
        memThreshold: number;
        name: string;
        updatedDate: string;
    }

    export interface IApiReqPostDcvGroup {
        cpuThreshold: number;
        maxServerConn: number;
        memThreshold: number;
        name: string;
    }

    export interface IApiReqPutDcvGroup extends IApiReqPostDcvGroup {
        id: number;
    }

    export interface IApiGroupDcvAssignedModel {
        createdDate: string;
        dcvDesktopGroupId: number;
        gid: number;
        groupName: string;
        id: number;
        status: boolean;
        updatedDate: string;
    }

    export interface IApiUserDcvAssignedModel {
        createdDate: string;
        dcvDesktopGroupId: number;
        uid: string;
        userName: string;
        id: number;
        status: boolean;
        updatedDate: string;
    }

    export interface IApiResGetDcvGroupAssigned {
        groupDcvDesktopGroups: IApiGroupDcvAssignedModel[];
        userDcvDesktopGroups: IApiUserDcvAssignedModel[];
    }

    export interface IApiReqAdminFileUpload {
        file: File;
        path: string;
    }

    export interface IApiResProjectModel {
        projectMonthGpuTimeQuota: number;
        projectMonthCpuTimeQuota: number;
        id: number;
        name: string;
        description: string;
        open: boolean;
        disabled: boolean;
        createdDate: string;
        updatedDate: string;
        balance: number;
        balanceType: 'IN_ARREARS' | 'NORMAL';
        totalAmount: number;
        enableFastCalculationTimeStart: number;
        enableFastCalculationTimeEnd: number;
        fastCalculationType: 'DISABLE' | 'FORCE' | 'NORMAL';
        appTimeSubmitStrategies?: PricingStrategies[];
    }

    export type IApiReqAdminProjectListParams = IApiReqPageParams & {
        gids?: number[];
        uids?: string[];
    };

    export type IApiResAdminProjectList = IApiResPagedDataModel<IApiResProjectModel>;

    export interface IApiReqDataCreateProject {
        name: string;
        level1?: string;
        level2?: string;
        level3?: string;
        open?: boolean;
        description?: string;
        disabled?: boolean;
    }

    export interface IApiReqDataUpdateProject extends Omit<IApiReqDataCreateProject> {
        id: number;
        name: string;
        fastCalculationType: undefined | 'DISABLE' | 'FORCE' | 'NORMAL';
        appTimeSubmitStrategies?: PricingStrategies[];
    }

    export interface IApiResAdminProjectGroupAssigned {
        projectId: number;
        createdDate: string;
        gid: number;
        id: number;
        status: boolean;
        updatedDate: string;
    }

    export interface IApiResAdminProjectUserAssigned {
        projectId: number;
        createdDate: string;
        id: number;
        status: boolean;
        uid: string;
        updatedDate: string;
    }

    export interface IApiResGetAdminProjectAssigned {
        gruopProjects: IApiResAdminProjectGroupAssigned[];
        userProjects: IApiResAdminProjectUserAssigned[];
    }

    export interface IApiReqPostAdminProjectAssign {
        projectId: number;
        blackGids?: number[];
        blackUids?: string[];
        recursive?: boolean;
        gids?: number[];
        uids?: string[];
    }

    export type IApiReqGetAdminProjectUserAssignable = IApiReqPageParams &
        IUserSearchParams & {
            projectId: number;
        };

    export interface IApiReqGetAdminProjectLevel2 {
        level1s?: string[];
        level2Search?: string;
    }

    export interface IApiReqGetAdminProjectLevel3 {
        level1s?: string[];
        level2s?: string;
        level3Search?: string;
    }
    export interface IReqGetBillingStatistics {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        uid?: string;
        gid?: string;
        jobTimeUnit?: 'DAYS' | 'HOURS' | 'MINUTES' | 'MONTHS' | 'WEEKS';
    }

    export interface IResGetBilling {
        avgIntervalPendTime: number;
        avgIntervalRunTime: number;
        billingCpuTimePercent: number;
        billingGpuTimePercent: number;
        customPayGroupName: string;
        groupName: string;
        jobNum: number;
        totalAvgIntervalPendTime: number;
        totalAvgIntervalRunTime: number;
        totalBillingCpuTime: number;
        totalBillingGpuTime: number;
        totalIntervalBillingCpuTime: number;
        totalIntervalBillingGpuTime: number;
        isTotal?: boolean;
    }

    export interface IResGetBillingStatisticsSoftware extends IResGetBilling {
        software: string;
    }

    export interface IResGetBillingStatisticsQueue extends IResGetBilling {
        queue: string;
        queueName: string;
        platform: string;
    }

    export interface IResGetBillingStatisticsNcpus extends IResGetBilling {
        ncpus: string;
    }

    export interface IResGetBillingStatisticsUid extends IResGetBilling {
        uid: string;
    }

    export interface IResGetBillingStatisticsGroup extends IResGetBilling {
        gid: string;
    }

    export interface IResGetBillingStatisticsVersion extends IResGetBilling {
        version: string;
    }
    export interface IReqGetBillingSccJobsList extends IReqGetBillingStatistics, IApiReqPageParams {
        uid?: string;
        appealStatus?: string[];
        billingType?: IApiResQueueBillingType;
        softwares?: string;
        jobTimeUnit?: 'MINUTES' | 'HOURS' | 'DAYS' | 'MONTHS';
        order?: string;
        orderBy?: string;
        page?: number;
        size?: number;
        searchRawJobId?: string;
        searchUid?: string;
        zoneClusterQueue?: string[];
    }

    export interface IResGetBillingJobsList {
        allNodes: string;
        appealStatus: string;
        billingCpuTime: number;
        billingGpuTime: number;
        billingType: IApiResQueueBillingType;
        cluster: string;
        createdDate: string;
        department: string;
        endPendingTime: string;
        endRunTime: string;
        endTime: string;
        exitCode: number;
        exitDesc: string;
        id: number;
        intervalEndTime: string;
        intervalPendTime: number;
        intervalRunTime: number;
        intervalStartTime: string;
        jobName: string;
        ncpus: number;
        ngpus: number;
        nodeNum: number;
        nodes: string;
        originState: string;
        outputPath: string;
        pendReason: string;
        pendReasonCn: string;
        pendReasonDesc: string;
        pendTime: number;
        platform: string;
        priority: string;
        queue: string;
        rawJobId: string;
        remark: string;
        runTime: number;
        sccUser: string;
        software: string;
        startPendingTime: string;
        startRunTime: string;
        startTime: string;
        state: string;
        submitTime: string;
        uid: string;
        updatedDate: string;
        workDir: string;
        zone: string;
        zoneClusterQueue: string;
        billingFreeCpuTime: number;
        intervalFreeTime: number;
    }

    export interface IResGetBillingJobsFilter {
        softwares: string[];
        uids: string[];
        queues: Record<
            string,
            {
                name: string;
                zone: string;
                cluster: string;
                queue: string;
                reserve: boolean;
                elasticity: boolean;
                billingType: IApiResQueueBillingType;
                enable: boolean;
                platform: string;
                cpus: number;
            }
        >;
        groups: {
            def: boolean;
            description?: string;
            disable: boolean;
            groupName: string;
            id: number;
            level: number;
            sid: string;
        }[];
    }

    export interface IReqGetBillingStatisticsChartCpuTime extends IReqGetBillingStatistics {
        billingAccountId?: number;
        jobTimeUnit?: 'MONTHS' | 'HOURS' | 'DAYS' | 'MINUTES';
        software?: string;
        uids?: string;
        zoneClusterQueues?: string;
        workingType?: 'ALL_DAY' | 'NON_WORKDAY' | 'WORKDAY';
    }

    export interface IResGetBillingStatisticsChartCpuTime {
        cpuTime: number[];
        jobOver: number[];
        licenseLimit: number[];
        licenseOver: number[];
        otherLimit: number[];
        personalLimit: number[];
        queueLimit: number[];
        resourceLimit: number[];
        totalCpuTime: number[];
        clusterCpusLimit: number[];
        x: number[];
        totalCpuTime: number;
        totalCpuTimeLimit: number;
        totalCpuTimeUsage: number;
    }

    export interface IResRole {
        admin: boolean;
        assignable: boolean;
        createdDate: string;
        description: string;
        id: number;
        name: string;
        updatedDate: string;
        roleType: 'ADMIN' | 'GROUP_MANAGER' | 'NORMAL';
        // ADMIN 超管 特殊的角色，不可分配
        // GROUP_MANAGER 组超管 特殊的角色，不可分配
        // NORMAL 正常 (这个值不要轻易修改，role db 使用了 'NORMAL')
    }

    export interface IReqRole {
        name: string;
        assignable: boolean;
        description: string;
    }

    export interface RoleMenu {
        assign: boolean;
        assignable: boolean;
        id: number;
        menuSort: number;
        name: string;
        menuName: string;
        parameters: string;
        parentId: number;
        path: string;
        routes: RoleMenu[] | undefined;
    }
    export interface IReqCreateMenu {
        id?: number;
        assign: boolean;
        assignable: boolean;
        menuSort: number;
        name: string;
        menuName: string;
        parameters: string;
        parentId: number;
        path: string;
        routes: RoleMenu[];
    }
    export interface IReqRoleMenu {
        menuIds: number[];
        roleId: number;
    }
    export interface IReqGetAdminJobMail {
        appCode?: string;
    }

    export interface IResGetAdminJobMail {
        appCode: string;
        mailJobStatus: {
            DONE: boolean;
            PREP: boolean;
            CANCEL: boolean;
            FAIL: boolean;
            PENDING: boolean;
            SUSPENDED: boolean;
            RUN: boolean;
            UNKNOWN: boolean;
        };
        intervalTime: {
            RUN: number;
            PENDING: number;
        };
    }

    export interface IReqPostAdminJobMail {
        appCode: string;
        intervalTime?: number;
        mailJobStatus:
            | 'DONE'
            | 'PREP'
            | 'CANCEL'
            | 'FAIL'
            | 'PENDING'
            | 'SUSPENDED'
            | 'RUN'
            | 'UNKNOWN';
    }

    export interface IResAdminGroupMonthQuota {
        gid: number;
        groupMonthCpuTimeQuota: number;
        groupMonthGpuTimeQuota: number;
    }

    export interface IResAdminUserMonthQuota {
        uid: string;
        userMonthCpuTimeQuota: number;
        userMonthGpuTimeQuota: number;
    }
    export interface IResAdminUserAssignRole {
        uid: string;
        roleId: number | null;
    }
    export interface IResAdminFileTaskConfig {
        totalUpload: number;
        totalDownload: number;
    }

    export interface IApiResMenuUser {
        name: string;
        path: string;
        routes: IApiResMenuUser[];
        assign?: boolean;
        id: number;
        menuName: string;
        menuOrder?: number;
        parameters?: string;
        parentId?: number;
        /** tabs显隐控制字段，详见/components/control_tab/index.tsx */
        tabs?: IApiResMenuUser[];
        type?: 'PAGE' | 'TABLE';
    }

    export interface IReqAdminAuditLog {
        uid?: string;
        models?: string;
        usernameSearch?: string;
        page?: number;
        size?: number;
        beginDate?: string;
        endDate?: string;
    }
    export interface IReqGetAdminAuditModelList {
        modelDescSearch?: string;
    }
    export interface IResGetAdminAuditModelList {
        auditModel: string;
        modelDesc: string;
    }

    type IAdminAuditLogLevelEnum = 'LOW' | 'MODERATE' | 'HIGH';
    type IJobSecurityLevel = 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED';

    export interface IResAdminAuditLog {
        createdDate: string;
        updatedDate: string;
        id: number;
        uid: string;
        username: string;
        type: string;
        typeDesc: string;
        success: boolean;
        description: string;
        ip: string;
        errorCode?: string;
        errorMsg?: string;
        level: IAdminAuditLogLevelEnum;
        userSecurityLevel: string;
        jobSecurityLevel: IJobSecurityLevel;
        sufferer: string;
        auditModel: string;
    }

    export interface IApiReqGetAdminMonitorNodePage extends IApiReqPageParams {
        zoneCode?: string;
        nodeName?: string;
        clusterCode?: string;
    }
    export interface IAdminMonitorNode {
        pasPlatforms: any;
        cloud: boolean;
        zone: string;
        cluster: string;
        name: string;
        state: string;
        cores: number;
        ntype: string;
        queue?: string;
        ibsw: string;
        platform: string;
        jobids: number[];
        availableMem: number;
        availableNcpus: number;
        assignedMem: number;
        assignedNcpus: number;
    }

    export interface IResAdminPermission {
        id: number;
        model: string;
        type: 'WRITE' | 'CREATE' | 'DELETE' | 'READ';
        description: string;
        disable: boolean;
    }

    interface IPermission {
        id: number;
        model: string;
        authDescription: string;
        type: 'WRITE' | 'CREATE' | 'DELETE' | 'READ';
        description: string;
        disable: boolean;
        select: boolean;
    }

    export interface IResAdminPermissionAssign {
        model: string;
        modelNam: string;
        permissionList: IPermission[];
    }

    interface IResGetAdminAssignedPublicScAccountModel {
        createdDate: string;
        id: number;
        sshAccountId: number;
        status: boolean;
        updatedDate: string;
    }

    export interface IResGetAdminPublicScAccountAssignedGroup
        extends IResGetAdminAssignedPublicScAccountModel {
        gid: number;
        groupName: string;
    }

    export interface IResGetAdminPublicScAccountAssignedUser
        extends IResGetAdminAssignedPublicScAccountModel {
        uid: string;
        username: string;
    }

    export interface IResGetAdminAccountAssigned {
        groupPublicScAccounts: IResGetAdminPublicScAccountAssignedGroup[];
        userPublicScAccounts: IResGetAdminPublicScAccountAssignedUser[];
    }

    export interface IReqPostAdminAccountAssign {
        blackGids?: number[];
        blackUids?: string[];
        gids?: number[];
        recursive?: boolean;
        sshAccountId: number;
        uids?: string[];
    }

    export interface IReqAdminUserSecurity {
        uid: string;
        userSecurityLevel: string;
    }
    export interface IResGetAdminStatisticsSccQueue {
        billingType: string;
        cluster: string;
        cpus: number;
        createdDate: string;
        disableDashBoardShow: boolean;
        elasticity: boolean;
        enable: boolean;
        endDate: string;
        gpus: number;
        id: number;
        name: string;
        platform: string;
        queue: string;
        queueInfo: string;
        queuePrice: number;
        queueType: string;
        reserve: boolean;
        startDate: string;
        updatedDate: string;
        zone: string;
    }

    export interface IResGetAdminStatisticsChartSccQueue {
        metric: Record<string, string>;
        values: [number, number][];
    }
    export interface IResGetAdminViewsMonitorMachine {
        metric: Record<string, string>;
        values: [number, string][];
    }

    export interface IReqGetAdminStatisticsChartSccQueue {
        zones: string;
        clusters: string;
        queues: string;
        step?: number;
        createdStart: string;
        createdEnd: string;
    }

    export interface IResGetAdminStatisticsSccPlatform {
        zone: string;
        cluster: string;
        platform: string;
    }

    export interface IReqGetAdminStatisticsSccPlatform {
        zone: string;
        cluster: string;
        platform: string;
        step?: number;
        createdStart: string;
        createdEnd: string;
    }

    export interface IReqGetAdminStatisticsSccPercentJobNode {
        zone: string;
        cluster: string;
        step?: number;
        createdStart: string;
        createdEnd: string;
    }

    export interface IReqGetAdminStatisticsListActiveUser extends IApiReqPageParams {
        beginDateTime: string;
        endDateTime: string;
        gid?: string;
        uid?: string;
        username?: string;
    }

    export interface IResGetAdminStatisticsListActiveUser {
        lastLoginTime: string;
        loginNum: string;
        uid: string;
        username: string;
    }

    export interface IReqGetAdminStatisticsUserLogin {
        beginDateTime: string;
        endDateTime: string;
    }

    export interface IReqGetAdminStatisticsChartActiveGroupUserJob {
        beginDateTime: string;
        endDateTime: string;
        isGUI?: boolean;
        isRawJob?: boolean;
        gid?: number;
        recursive?: boolean;
    }

    export interface IResGetAdminStatisticsChartActiveGroupUserJob {
        x: string[];
        y: number[];
    }

    export interface IReqGetAdminStatisticsChartAppJob {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
    }

    export interface IResGetAdminStatisticsChartAppJob {
        x: number[];
        y: Record<string, number[]>;
    }

    export interface IReqGetAdminStatisticChartViewSession {
        createdStart: string;
        createdEnd: string;
        viewType: 'CITIRIX';
        desktopGroups?: string[];
        step?: number;
    }

    export interface IReqGetAdminStatisticViewsSessionUser extends IApiReqPageParams {
        beginDateTime: string;
        endDateTime: string;
        gids?: number[];
        recursive?: boolean;
    }

    export interface IResGetAdminStatisticViewsSessionUser {
        groupName: string;
        groupName: string;
        lastLiveTime: string;
        loginNum: number;
        uid: string;
        username: string;
    }

    export interface IResAdminAppVersionResourceAssigned {
        createdDate: string;
        updatedDate: string;
        id: number;
        appCode: string;
        appVersion: string;
        singleJobMaxCores: number;
        appSubmitType: string;
        appVersionOrder: number;
    }

    export interface IReqPostAdminAppVersionResource {
        appVersionIds?: any[];
        queueResourceIds?: any[];
    }

    export interface IReqDeleteAdminAppVersionResource {
        appResourceIds?: number[];
    }

    export interface IReqGetAdminStatisticChartSccCalculatePercentPlatform {
        zone: string;
        cluster: string;
        platform: string;
        createdStart: string;
        createdEnd: string;
        step?: number;
    }

    export interface IReqGetAdminAccountUserAssignable extends IApiReqPageParams {
        sshAccountId: number;
    }

    export interface IReqGetAdminStatisticChartViewConnect {
        createdStart: string;
        createdEnd: string;
        step?: number;
        name: string;
        viewType: 'DCV' | 'CITIRIX';
    }

    export interface IReqGetAdminStatisticChartDesktopGroupViewConnect {
        createdStart: string;
        createdEnd: string;
        step?: number;
        desktopGroup: string;
        viewType: 'DCV' | 'CITIRIX';
    }

    export interface IReqGetAdminStatisticChartGraphNode {
        createdStart: string;
        createdEnd: string;
        step?: number;
        viewType: 'DCV' | 'CITIRIX';
    }

    export interface IResGetSoftwareLicenseIssueData {
        createdDate: string; // 创建时间
        id: number;
        isActive: boolean; // 服务器状态
        lid: number; // 证书id
        module: string; // 软件模块
        remainLicenses: number; // 剩余证书许可量
        software: string; // 软件名称
        totalLicenses: number; // 总的证书许可
        updatedDate: string; // 更新时间
        usedLicenses: number; // 已使用证书许可
    }
    export type IResGetSoftwareLicenseIssue =
        IApiResPagedDataModel<IResGetSoftwareLicenseIssueData>;

    export interface IResGetAdminStatisticsChartActiveUserMonth {
        x: number[];
        y: number[];
    }
    export interface IReqGetAdminBillingStatisticsCpuJob {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        gid?: number;
        containSelf?: boolean;
        unit?: string;
    }
    export interface IResGetAdminBillingStatisticsCpuJobY {
        count: number[];
        ncpu: number[];
    }
    export interface IResGetAdminBillingStatisticsCpuJob {
        x: number[];
        y: IResGetAdminBillingStatisticsCpuJobY;
    }
    export interface IReqPutAdminJobJobIdHigher {
        jobId: string;
        higher: number;
    }
    export interface IReqGetAdminJobExport {
        createdStart: string;
        createdEnd: string;
    }
    export interface IReqGetAdminViewsMonitorMachineMachineName {
        machineName: string;
        beginDateTime: string;
        endDateTime: string;
        step: number;
    }
    export interface IApiResGetStatisticModel {
        avgIntervalPendTime: number;
        avgIntervalRunTime: number;
        donePercent: number;
        jobDoneNum: number;
        jobNotDoneNum: number;
        jobNum: number;
        maxIntervalPendTime: number;
        maxIntervalRunTime: number;
        maxPendTime: number;
        maxRunTime: number;
        totalIntervalPendTime: number;
        totalIntervalRunTime: number;
    }
    export interface IApiResGetStatisticCpuModel extends IApiResGetStatisticModel {
        billingCpuTimePercent: number;
        totalBillingCpuTime: number;
        totalElasticityIntervalBillingCpuTime: number;
        totalIntervalBillingCpuTime: number;
        totalNotElasticityIntervalBillingCpuTime: number;
    }

    export interface IApiResGetStatisticCpuSoftware extends IApiResGetStatisticCpuModel {
        software: string;
    }
    export interface IReqgetAdminBillingStatisticsQuantityJob {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        unit?: 'DAYS' | 'HOURS' | 'MINUTES' | 'MONTHS' | 'WEEKS';
    }
    export interface IReqGetAdminJobRuntime {
        rowJobId: number;
        zone: string;
        cluster: string;
    }
    export interface IResGetAdminJobRuntime {
        walltime: number;
    }

    export interface IApiResGetAdminJobChangeQueue {
        coresPending: number;
        coresRemaining: number;
        coresRunning: number;
        coresTotal: number;
        queue: string;
        queueInfo: QueueInfo;
        sccUser: string;
        queueInfo: {
            billingType: string;
            cluster: string;
            cpus: number;
            createdDate: string;
            disableDashBoardShow: boolean;
            elasticity: boolean;
            enable: boolean;
            endDate: string;
            gpus: number;
            id: number;
            name: string;
            platform: string;
            queue: string;
            queueInfo: string;
            queuePrice: number;
            queueType: string;
            reserve: boolean;
            startDate: string;
            updatedDate: string;
            zone: string;
        }[];
    }
    export interface IApiResGetAdminJobFilesDownload {
        createdDate: string;
        destAccount: string;
        destCluster: string;
        destClusterConnectionIp: string;
        destClusterConnectionPort: number;
        destClusterPath: string;
        destZone: string;
        fileTransferStatus: 'CANCEL' | 'DONE' | 'FAIL' | 'INIT' | 'RUN';
        id: number;
        jobId: string;
        message: string;
        nextAccessTime: string;
        percentage: number;
        requestAccount: string;
        requestCluster: string;
        requestZone: string;
        speed: number;
        totalFileCount: number;
        totalFileSize: number;
        transferEndDate: string;
        transferPriority: IApiJobTransferPriority;
        transferStartDate: string;
        transferTime: number;
        updatedDate: string;
        userId: string;
    }
    export interface IApiResGetAdminJobView {
        url: string;
    }
    export interface IApiResGetgetAdminViewsOdataMachine {
        data: { hostedMachineName: string }[];
    }

    export interface IApiResGetAdminBillingStatisticsAnalysisDurationCpuJob {
        value: number;
        name: string;
    }
    export interface IApiResGetAdminBillingStatisticsQuantityJob {
        x: number[];
        y: Record<string, number[]>;
    }
    export interface IApiResGetAdminBillingStatisticsMonthSummary {
        month: number;
        totalActiveUsers: number;
        totalAvgRunJobTime: number;
        totalCores: number;
        totalCpuTime: number;
        totalCpuUsage: number;
        totalPendingTimes: number;
        totalRunJobs: number;
    }

    export interface IApiResGetAdminNotifyJobConfigure {
        appCode: string;
        configs: {
            assign: boolean | null;
            exceedCpuTime: number | null;
            exceedPendingTime: number | null;
            exceedRunTime: number | null;
            intervalTime: number | null;
            jobStatus: string;
        }[];
    }

    export interface IApiResGetAdminNotifyJobPerformanceConfigure {
        appCode: string;
        configs: {
            killThreshold: number;
            threshold: number;
            intervalTime: number;
            notifyEventType: string;
            jobCpuRuntimeKillThreshold: number;
            jobRuntimeKillThreshold: number;
        }[];
    }

    export type IApiReqPostAdminNotifyEventPerformanceConfigure =
        IApiResGetAdminNotifyJobPerformanceConfigure;

    // 作业标签
    export interface JobTag {
        id: number;
        key: string;
        name: string;
        open: boolean;
        createdDate: string;
        updatedDate: string;
    }

    // 创建作业标签
    export type IApiReqPostAdminJobTag = Pick<JobTag, 'id' | 'name' | 'open'>;

    // 创建标签类型
    export type IApiReqPostAdminJobTagType = Pick<JobTag, 'key' | 'name'>;
    // 修改标签类型
    export type IApiReqPutAdminJobTagType = Pick<JobTag, 'id' | 'key' | 'name'>;

    // 作业列表
    export type IApiResGetAdminJobTagList = IApiResPagedDataModel<Exclude<JobTag, 'open'>>;

    // 作业标签值
    export interface IApiResGetAdminJobTagOption {
        id: number;
        jobTagId: number;
        jobTagOptionName: string;
        jobTagOptionValue: string;
    }

    // 标签组分配用户组
    export interface IApiGetAdminJobTagAssignnedGroupJobTags {
        gid: number;
        groupName: string;
        id: number;
        jobTagId: number;
        status: boolean;
    }

    // 标签组分配用户
    export interface IApiGetAdminJobTagAssignnedUserJobTags {
        id: number;
        jobTagId: number;
        status: boolean;
        uid: string;
        username: string;
    }

    export interface IApiGetAdminJobTagAssigned {
        groupJobTags: IApiGetAdminJobTagAssignnedGroupJobTags[];
        userJobTags: IApiGetAdminJobTagAssignnedUserJobTags[];
    }

    export interface IApiReqAdminJobTagUserAssignable {
        jobTagId: number;
        page?: number;
        size?: number;
        uidSearch?: string;
        usernameSearch?: string;
    }

    /** 查询作业标签选项请求参数 */
    export interface IApiReqGetAdminJobTagGroupExpandOption {
        statisticExpandType: 'EXPAND1' | 'EXPAND2' | 'EXPAND3' | 'EXPAND4' | 'EXPAND5';
        gids?: number[];
    }

    /** 查询作业标签选项返回类型 */
    export interface IApiResGetAdminJobTagGroupExpandOption {
        createdDate: string;
        id: number;
        jobTagId: number;
        jobTagOptionName: string;
        jobTagOptionOrder: number;
        jobTagOptionValue: string;
        updatedDate: string;
    }

    export interface IApiReqpostAdminJobTagAssign {
        jobTagId: number;
        gids?: number[];
        blackGids?: number[];
        blackUids?: string[];
        recursive?: boolean;
        uids?: string[];
    }

    export interface IApiResAdminJobTagUserAssignable {
        accountExpires: string;
        allowDownload: boolean;
        changePassword: boolean;
        createdDate: string;
        description: string;
        disable: boolean;
        disableDate: string;
        email: string;
        expandDateFirst: string;
        expandFirst: string;
        expandSecond: string;
        gid: number;
        homeDirectory: string;
        id: number;
        init: boolean;
        passwordExpires: string;
        resign: boolean;
        resignDate: string;
        roleId: number;
        sid: string;
        uid: string;
        updatedDate: string;
        userSecurityLevel: string;
        username: string;
    }

    export interface IApiDeleteAdminJobTagAssigned {
        userJobTagIds?: number[];
        groupJobTagIds?: number[];
    }
    export interface IApiGetAdminUserExport {
        gids?: number[];
        recursive?: boolean;
        usernameSearch?: string;
        uidSearch?: string;
        disable?: boolean;
    }
    export interface IApiGetAdminGroupExport {
        gid: number;
        recursive?: boolean;
        searchName?: string;
    }
    export interface IApiReqGetAdminRoleUser {
        id: number;
        page?: number;
        size?: number;
        uidSearch?: string;
        usernameSearch?: string;
    }
    export interface IApiReqDeleteAdminRoleUser {
        id?: number;
        uids: string[];
    }
    export type IApiResGetAdminRoleUser = IApiResPagedDataModel<IApiResAdminCreateUserModel>;

    export interface IApiReqGetAdminSystemAnnouncement {
        content: string;
        id: number;
        open: boolean;
        title: string;
        expiredAt: string;
        effectiveAt: string;
        createdDate: string;
        updatedDate: string;
        source: 'AUTO' | 'MANUAL';
        type: 'NORMAL' | 'POPUP';
    }

    export type IApiReqGetAdminSystemAnnouncementList =
        IApiResPagedDataModel<IApiReqGetAdminSystemAnnouncement>;

    export interface IApiReqPostAdminSystemAnnouncement {
        description: string;
        content: string;
        effectiveAt: string;
        expiredAt: string;
        gids: number[];
        open: boolean;
        recursive: boolean;
        title: string;
        type: 'NORMAL' | 'POPUP';
        uids: number[];
    }

    // 列出公告已分配的组和用户
    export interface IApiGroupAnnouncements {
        announcementId: number;
        gid: number;
        id: number;
        groupName: string;
        createdDate: string;
        updatedDate: string;
    }

    export interface IApiUserAnnouncements {
        announcementId: number;
        delete: boolean;
        id: number;
        status: boolean;
        uid: string;
        username: string;
        createdDate: string;
        updatedDate: string;
    }
    export interface IApiReqGetAdminSystemAnnouncementAssigned {
        groupAnnouncements: IApiGruopAnnouncements[];
        userAnnouncements: IApiUserAnnouncements[];
    }

    export interface IApiReqPostAdminSystemAnnouncementAssign {
        announcementId?: number;
        gids?: number[];
        uids?: string[];
        recursive?: boolean;
    }

    export interface IApiReqGetAdminSystemAnnouncementUserAssigned extends IApiReqPageParams {
        announcementId?: number;
        gid?: number;
        uidSearch?: string;
        usernameSearch?: string;
    }

    export interface IAnnouncementUserAssigned {
        accountExpires: string;
        allowDownload: boolean;
        changePassword: boolean;
        createdDate: string;
        description: string;
        disable: boolean;
        disableDate: string;
        email: string;
        expandDateFirst: string;
        expandFirst: string;
        expandSecond: string;
        gid: number;
        homeDirectory: string;
        id: number;
        init: boolean;
        passwordExpires: string;
        resign: boolean;
        resignDate: string;
        roleId: number;
        sid: string;
        uid: string;
        updatedDate: string;
        userSecurityLevel: string;
        username: string;
    }

    export type IApiResGetAdminSystemAnnouncementUserAssigned =
        IApiResPagedDataModel<IAnnouncementUserAssigned>;
    export interface IApiReqGetAdminBillingRecharge {
        rechargeAmount: number;
        rechargeMsg: string;
    }

    export interface IApiReqGetAdminBillingChargeRecordList extends IApiReqPageParams {
        startTime: string;
        endTime: string;
        gid?: number;
        uid?: string;
    }
    export interface IApiResGetAdminBillingChargeRecord {
        billingAccountName: any;
        cpgName: string;
        projectName: string;
        billingStrategy: 'PROJECT' | 'GROUP' | 'USER' | 'CUSTOM_PAY_GROUP' | 'BILLING_ACCOUNT';
        gid: number; // 被充值组GID
        groupName: string; // 被充值组名
        id: number;
        uid: string; // 被充值用户UID
        userName: string; // 被充值用户名
        balance: number; // 用户/组余额
        operatorUid: string; // 操作充值用户ID
        operatorUserName: string; // 操作充值用户名
        rechargeAmount: number; // 充值金额
        rechargeMsg: string; // 充值说明
        rechargeTime: string; // 充值时间
    }

    export type IApiResGetAdminBillingChargeRecordList =
        IApiResPagedDataModel<IApiResGetAdminBillingChargeRecord>;

    export type IApiResGetConfigMail = IApiResPagedDataModel<IApiResConfigMailDataItem>;

    export interface IApiResConfigMailDataItem {
        ccEmail: string;
        createdDate: string;
        id: number;
        notifyEventType: string;
        notifyEventTypeValue: string;
        eventCcTypeEnum: string;
        eventCcTypeValue: string;
        updatedDate: string;
    }

    export type IApiReqGetConfigMail = IApiReqPageParams & IApiReqPostConfigMail;

    export interface IApiReqPostConfigMail {
        id?: number;
        notifyEventTypeEnum?: string;
        eventCcType?: string;
        ccEmail?: string;
        subject?: string;
        templateContent?: string;
        sender?: string;
    }

    export interface IApiResGetNotifyEvevtType {
        notifyEventTypeEnum: string;
        notifyEventTypeValue: string;
    }

    export interface IApiResGetNotifyEvevtCcType {
        eventCcTypeEnum: string;
        eventCcTypeValue: string;
    }

    export interface IApiResGetReminderTemplateListItem {
        createdDate: string;
        eventTypeConfigId: number;
        id: number;
        notifyToolType: string;
        subject: string;
        templateContent: string;
        updatedDate: string;
    }

    export interface IApiReqPostNotifyTemplateBind {
        eventTypeConfigId: number;
        id?: number;
        notifyToolType: string;
        subject: string;
        templateContent: string;
    }

    export interface IApiReqPostReminderTemplateList {
        eventTypeConfigId: number;
        notifyToolType?: string;
    }

    export interface IApiResGetNotifyTemplateTool {
        notifyToolEnum: string;
        notifyToolValue: string;
    }

    export interface IApiReqGetAdminBillingStatisticsCount {
        queueType: 'CPU' | 'GPU';
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        uids?: string[];
        gids?: string[];
        projectIds?: string[];
        expand1s?: string[];
        expand2s?: string[];
        expand3s?: string[];
        expand4s?: string[];
        expand5s?: string[];
        recursive?: boolean;
    }

    export interface IApiResGetAdminBillingStatisticsCount {
        expandXName: string;
        expandXOrder: number;
        expandXValue: string;
        expandYName: string;
        expandYOrder: number;
        expandYValue: string;
        queueType: string;
        time: number;
    }
    export interface IApiResGetAdminBillingStatisticsCountProjectExpand {
        expandName: string;
        expandOrder: number;
        expandValue: string;
        projectId: number;
        projectName: string;
        queueType: string;
        time: number;
    }
    export interface IApiResGetAdminBillingStatisticsCountGroupExpand {
        expandName: string;
        expandOrder: number;
        expandValue: string;
        gid: number;
        groupName: string;
        queueType: string;
        time: number;
    }
    export interface IApiResGetAdminBillingStatisticsCountUserExpand {
        expandName: string;
        expandOrder: number;
        expandValue: string;
        queueType: string;
        time: number;
        uid: string;
        userName: string;
    }

    export interface IApiResGetAdminBillingStatisticsCountExpendInfo {
        jobTagOptionName: string;
        jobTagOptionValue: string;
        jobTagOptionOrder: number;
    }
    export interface IApiResGetAdminJobStatisticsJobRunApp {
        app: string;
        appname: string;
        coresRunning: number;
        errorLimit: number;
        licenseLimit: number;
        otherLimit: number;
        pendingJob: number;
        personalLimit: number;
        queueType: string;
        resourceLimit: number;
        runJob: number;
    }
    export interface IApiResGetAdminJobStatisticsJobRunGroup {
        coresRunning: number;
        errorLimit: number;
        gid: number;
        groupname: string;
        licenseLimit: number;
        otherLimit: number;
        pendingJob: number;
        personalLimit: number;
        queueType: string;
        resourceLimit: number;
        runJob: number;
    }
    export interface IApiResGetAdminJobStatisticsJobRunJobuser {
        cluster: string;
        coresRunning: number;
        errorLimit: number;
        jobUser: string;
        licenseLimit: number;
        otherLimit: number;
        pendingJob: number;
        personalLimit: number;
        queueType: string;
        resourceLimit: number;
        runJob: number;
        zone: string;
        jobUserLimit: number;
    }
    export interface IApiResGetAdminJobStatisticsJobRunPlatform {
        coresRunning: number;
        errorLimit: number;
        licenseLimit: number;
        otherLimit: number;
        pendingJob: number;
        personalLimit: number;
        platfom: string;
        queueType: string;
        resourceLimit: number;
        runJob: number;
    }
    export interface IApiResGetAdminJobStatisticsJobRunProject {
        coresRunning: number;
        errorLimit: number;
        licenseLimit: number;
        otherLimit: number;
        pendingJob: number;
        personalLimit: number;
        projectId: number;
        projectName: string;
        queueType: string;
        resourceLimit: number;
        runJob: number;
    }
    export interface IApiResGetAdminJobStatisticsJobRunQueue {
        cluster: string;
        coresRunning: number;
        errorLimit: number;
        licenseLimit: number;
        otherLimit: number;
        pendingJob: number;
        personalLimit: number;
        queue: string;
        queueType: string;
        resourceLimit: number;
        runJob: number;
        zone: string;
        queueLimit: number;
    }
    export interface IApiReqGetAdminStatisticsChartJobTask {
        createdStart: string;
        createdEnd: string;
        zones?: string;
        clusters?: string;
        uids?: string;
        gids?: string;
        projectIds?: string;
        queueTypes?: string;
        platforms?: string;
        queues?: string;
        apps?: string;
        step?: number;
        expand1s?: string;
        expand2s?: string;
        expand3s?: string;
        expand4s?: string;
        expand5s?: string;
    }
    export interface IApiResGetAdminStatisticsChartJobTask {
        metric: { type: string; __name__: string };
        values: [number, number][];
    }

    export interface IApiResGetAdminFileTaskConfig {
        cluster: string;
        createdDate: string;
        downloadCorePoolSize: number;
        downloadTotal: number;
        id: number;
        manualDownloadTotal: number;
        retry: number;
        updatedDate: string;
        uploadCorePoolSize: number;
        uploadTotal: number;
        zone: string;
    }
    export interface IApiReqPostAdminFileTaskConfig {
        cluster: string;
        downloadCorePoolSize: number;
        downloadTotal: number;
        manualDownloadTotal: number;
        retry: number;
        uploadCorePoolSize: number;
        uploadTotal: number;
        zone: string;
    }
    export interface IApiReqGetAdminBillingStoragePrice extends IApiReqPageParams {
        nameSearch?: string;
        zone?: string;
        billingType?: 'FIXED_LEASE' | 'FIXED_YEAR_LEASE' | 'JOB_CORES' | 'NONE';
    }
    export interface IApiResGetAdminBillingStoragePrice {
        billingType: string;
        comment: string;
        createdDate: string;
        id?: number;
        name: string;
        price: number;
        unit: string;
        updatedDate: string;
        zone: string;
    }
    export type IApiResGetAdminBillingStoragePriceList =
        IApiResPagedDataModel<IApiResGetAdminBillingStoragePrice>;

    export type IApiReqGetAdminBillingQueuePriceList = IApiReqPageParams<{
        id?: string;
        name?: string;
        zone?: string;
        cluster?: string;
        queue?: string;
        elasticity?: string;
        billingType?: 'FIXED_LEASE' | 'FIXED_YEAR_LEASE' | 'JOB_CORES' | 'NONE';
        queueType?: 'GPU' | 'CPU';
        platform?: string;
    }>;
    export interface IApiResGetAdminBillingQueuePrice {
        billingType: string;
        cluster: string;
        cpuPrice: number;
        gpuPrice: number;
        name: string;
        platform: string;
        queue: string;
        queueInfo: string;
        queueType: string;
        zone: string;
        id: number;
        elasticity: boolean;
    }
    export type IApiResGetAdminBillingQueuePriceList =
        IApiResPagedDataModel<IApiResGetAdminBillingQueuePrice>;

    export interface IApiReqGetAdminBillingQueueStepPricing extends IApiReqPageParams {
        accountId?: number;
        nameSearch?: string;
    }

    export interface IApiPricingStrategy {
        createdDate: string;
        discount: number;
        id: number;
        maxValue: number;
        minValue: number;
        pricingObjectId: number;
        pricingType: string;
        updatedDate: string;
    }
    export interface IApiResGetAdminBillingQueueStepPricing {
        comment: string;
        id: number;
        name: string;
        pricingStrategies: IApiPricingStrategy[];
    }
    export type IApiResGetAdminBillingQueueStepPricingList =
        IApiResPagedDataModel<IApiResGetAdminBillingQueueStepPricing>;

    export interface IApiReqGetAdminBillingAccountTypeList extends IApiReqPageParams {
        nameSearch?: number;
    }

    export interface IApiResGetAdminBillingAccountType {
        accountName: string;
        comment: string;
        createdDate: string;
        id: number;
        updatedDate: string;
    }

    export interface IApiReqGetAdminBillingAccountStoragePricingList extends IApiReqPageParams {
        accountId?: number;
        queue?: string;
        zone?: string;
        queueType?: string;
    }

    export interface IApiResGetAdminBillingAccountStoragePricing {
        accountId: number;
        billingType: string;
        comment: string;
        discount: number;
        endDate: string;
        id: number;
        name: string;
        price: number;
        pricingStrategies: PricingStrategy[];
        startDate: string;
        unit: string;
        zone: string;
        storagePricingId: number;
    }

    interface PricingStrategy {
        createdDate: string;
        discount: number;
        id: number;
        maxValue: number;
        minValue: number;
        pricingObjectId: number;
        pricingType: string;
        updatedDate: string;
    }

    export interface IApiReqDeleteAdminBillingAccountStoragePricing {
        id: number;
    }

    export interface IApiReqPostAdminBillingAccountStoragePricing {
        id?: number;
        accountId?: number;
        billingType: string;
        discount: number;
        endDate: string;
        startDate: string;
        storagePricingId: number;
        zone: string;
        cluster: string;
    }

    export interface IApiReqDeleteAdminBillingAccountQueuePricing {
        id: number;
    }

    export interface IApiReqPostAdminBillingAccountType {
        accountName: string;
        comment: string;
        id?: number;
    }
    export interface IApiReqGetAdminBillingStorageStepPricing extends IApiReqPageParams {
        accountId?: number;
        nameSearch?: string;
        type?: string;
    }

    interface PricingStrategy {
        createdDate: string;
        discount: number;
        id: number;
        maxValue: number;
        minValue: number;
        pricingObjectId: number;
        pricingType: string;
        updatedDate: string;
    }
    export interface IApiResGetAdminBillingStorageStepPricing {
        comment: string;
        id: number;
        name: string;
        pricingStrategies: PricingStrategy[];
        type: string;
        unit: string;
    }
    export interface IApiReqGetAdminBillingStatisticsCpuUidSoftware extends IApiReqPageParams {
        software: string;
        intervalStartTimeStart?: string;
        intervalStartTimeEnd?: string;
    }
    export interface IApiResGetAdminBillingStatisticsCpuUidSoftware {
        allCpuTime: number;
        allFreeCpuTimePercentage: number;
        cpuTime: number;
        freeCpuTime: number;
        freeCpuTimePercentage: number;
        fullCpuTime: number;
        uid: string;
    }
    export interface IApiReqGetAdminStatisticsChartMonthStorage {
        createdStart: string;
        createdEnd: string;
        cluster?: string;
        timeUnit?: 'HOURS' | 'DAYS' | 'MONTHS';
    }

    export interface IApiResGetAdminStatisticsChartMonthStorage {
        metric: {
            cluster?: string;
            type?: string;
        };
        values: [string, string | null][];
    }

    export interface IApiResGetAdminBillingAccountTypeAccountIdBindGroupList {
        accountId: number;
        createdDate: string;
        gid: number;
        id: number;
        uid: string;
        updatedDate: string;
    }

    export interface IApiReqPostAdminBillingAccountTypeGroupUnbind {
        accountId: number;
        gid?: number[];
        uid?: string[];
    }

    export interface IApiReqGetAdminBillingAccountTypeBindUserAssignable extends IApiReqPageParams {
        accountId?: number;
        gid?: number;
        uidSearch?: string;
        usernameSearch?: string;
    }

    interface Role {
        admin: boolean;
        description: string;
        id: number;
        name: string;
    }
    export interface IApiResGetAdminBillingAccountTypeBindUserAssignable {
        allowDownload: boolean;
        description: string;
        disable: boolean;
        expandFirst: string;
        expandSecond: string;
        gid: number;
        homeDirectory: string;
        id: number;
        resign: boolean;
        role: Role;
        sid: string;
        uid: string;
        username: string;
    }

    interface PricingStrategy {
        createdDate: string;
        discount: number;
        id: number;
        maxValue: number;
        minValue: number;
        pricingObjectId: number;
        pricingType: string;
        updatedDate: string;
    }
    export interface IApiResGetAdminBillingAccountQueuePricingList {
        accountId: number;
        billingType: string;
        discount: number;
        id: number;
        pricingStrategies: PricingStrategy[];
        queue: string;
        queuePricingId: number;
        queueType: string;
        zone: string;
        cluster: string;
    }

    export interface IApiReqGetAdminBillingAccountQueuePricingAssignable extends IApiReqPageParams {
        accountId?: number;
        zone?: string;
        cluster?: string;
        queueSearch?: string;
    }

    export interface IApiResGetAdminBillingAccountQueuePricingAssignable {
        billingType: string;
        cluster: string;
        cpuPrice: number;
        createdDate: string;
        elasticity: boolean;
        gpuPrice: number;
        id: number;
        name: string;
        platform: string;
        queue: string;
        queueInfo: string;
        queueType: string;
        updatedDate: string;
        zone: string;
    }

    export interface IApiReqGetAdminBillingAccountQueuePricing extends IApiReqPageParams {
        queuePricingId: number | undefined;
        id?: number;
        accountId: number;
        cpuDiscount: number;
        gpuDiscount: number;
        billingType?: string;
        zone?: string;
        cluster?: string;
        name?: string;
        queuePriceId?: number;
        queueType?: string;
    }

    export interface IApiReqPostAdminBillingAccountQueuePricing {
        id?: number;
        accountId?: number;
        billingType?: string;
        cluster?: string;
        cpuDiscount?: number;
        gpuDiscount?: number;
        queue?: string;
        queuePriceId?: number;
        queuePricingId?: number;
        queueType?: string;
        zone?: string;
    }

    export interface IApiReqGetBillingOrder extends IApiReqPageParams {
        intervalStartTimeStart?: string;
        intervalStartTimeEnd?: string;
        uidSearch?: string;
    }

    export interface IApiReqGetAdminBillingStatisticsMonthBillGid extends IApiReqPageParams {
        intervalStartTimeStart?: string;
        intervalStartTimeEnd?: string;
        gid?: string;
    }

    export interface IApiResGetBillingOrder {
        cpuResourceCost: number;
        gpuResourceCost: number;
        month: number;
        payUid: string;
        payGid: string;
        status: string;
    }

    export interface IApiDashboardStatisticsCpuTime {
        cpuTimeNum: number;
        startDate: string;
    }

    export interface IApiDashboardStatisticsGpuTime {
        gpuTimeNum: number;
        startDate: string;
    }

    export interface IApiDashboardStatisticsGroup {
        cpuTimeUsed: number;
        gid: number;
        groupName: string;
        jobCount: number;
    }

    export interface IApiDashboardStatisticsProject {
        cpuTimeUsed: number;
        jobCount: number;
        projectId: string;
        projectName: string;
    }

    export interface IApiDashboardStatisticsUserJobCpuTime {
        cpuTimeUsed: number;
        jobCount: number;
        uid: string;
        userName: string;
    }

    export interface IApiDashboardJobStatusStatistics {
        pendingJobNum: number;
        preparingJobNum: number;
        runningJobNum: number;
        sumJobNum: number;
        waitingJobNum: number;
    }

    export interface IApiDashboardJobSubmitNumStatistics {
        count: number;
        date: string;
    }

    export interface IApiDashboardStatisticsUser {
        activeUserNum: number;
        totalUserNum: number;
    }

    export interface IApiDashboardStatisticsNodeStatus {
        nodes: number;
        offlineNodes: number;
        onlineNodes: number;
        onlineNodesPercent: number;
    }

    export interface IApiDashboardStatisticsCpuStatus {
        cpus: number;
        freeCpus: number;
        usedCpus: number;
        usedCpusPercent: number;
    }

    export interface IApiDashboardStatisticsGpuStatus {
        freeGpus: number;
        gpus: number;
        usedGpus: number;
        usedGpusPercent: number;
    }

    export interface IApiDashboardStatisticsNodeUsage {
        cpuUsageRate: number;
        gpuUsageRate: number;
        startDate: string;
    }

    export interface IApiDashboardStatisticsStorageUsed {
        totalStorage: string;
        remainStorage: string;
        usedStorage: string;
        usedPercent: string;
        remainPercent: string;
    }

    export interface IApiReqGetAdminBillingStatisticsMonthBillExport {
        intervalStartTimeStart?: string;
        intervalStartTimeEnd?: string;
        uidSearch?: string;
        uid?: string;
        gid?: string;
    }
    export interface IApiReqGetAdminBillingStatisticsJobStatus {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
    }
    export interface IApiResGetAdminBillingStatisticsJobStatus {
        date: number;
        doneNums: number;
        failNums: number;
    }

    export interface IApiReqGetAdminBillingStatisticsMonthBillDetail extends API.IApiReqPageParams {
        intervalStartTimeStart?: string;
        intervalStartTimeEnd?: string;
        uid?: string;
        gid?: string;
    }

    export interface IApiResGetAdminBillingStatisticsMonthBillDetail
        extends API.IResGetBillingJobsList {
        billStartDate: number;
        billEndDate: number;
        payUid: number;
        intervalRuntime: number;
        ncpus: number;
        ngpus: number;
        cpuCost: number;
        gpuCost: number;
        pricingStrategy: string;
    }

    export interface IApiPricingStrategyJsonStr {
        type: string;
        strategies: {
            createdDate: number;
            discount: number;
            id: number;
            maxValue: number;
            minValue: number;
            pricingObjectId: number;
            pricingType: string;
            updatedDate: number;
        }[];
        unit?: string;
    }

    export interface IApiReqPostAdminGroupUpdateAdmin {
        uid: string;
        gid: number;
    }

    export interface IApiReqGetAdminBillingStatisticsJobAnalysisChat {
        statisticExpandType: 'EXPAND1' | 'EXPAND2' | 'EXPAND3' | 'EXPAND4' | 'EXPAND5';
        statisticExpandValue: string;
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        gids?: string;
        uids?: string;
        projectIds?: string;
    }

    export interface IApiResGetAdminBillingStatisticsJobAnalysisChat {
        coreMilliseconds: number;
        date: number;
        jobCount: number;
    }

    export interface IApiResGetBillingStatisticsCpuPlatform {
        avgIntervalPendTime: number;
        avgIntervalRunTime: number;
        billingCpuTimePercent: number;
        donePercent: number;
        jobDoneNum: number;
        jobNotDoneNum: number;
        jobNum: number;
        maxIntervalPendTime: number;
        maxIntervalRunTime: number;
        maxPendTime: number;
        maxRunTime: number;
        platform: string;
        totalBillingCpuTime: number;
        totalElasticityIntervalBillingCpuTime: number;
        totalIntervalBillingCpuTime: number;
        totalIntervalPendTime: number;
        totalIntervalRunTime: number;
        totalNotElasticityIntervalBillingCpuTime: number;
    }

    export interface IApiReqPutAdminCoreUserLimit {
        appId: number;
        uid: string;
        cpusLimit: number;
        ngpusLimit: number;
    }
    export interface IApiReqPutAdminCoreGroupLimit {
        appId: number;
        gid: string;
        cpusLimit: number;
        ngpusLimit: number;
    }
    export interface IApiResGetAdminLicenseModelTotalList {
        expireDate: string;
        licenseFeature: string;
        licenseName: string;
        licenseServer: string;
        licenseStatus: string;
        licenseUT: string;
        licenseVendor: string;
        totalCount: number;
        usageCount: number;
    }

    export interface IApiReqGetAdminLicenseSessionTotalList extends API.IApiReqPageParams {
        appLicenseCode?: string;
        appLicenseModels?: string;
    }

    export interface IApiResGetAdminLicenseSessionTotalList {
        checkOutDate: string;
        checkOutHostName: string;
        gid: number;
        groupName: string;
        licenseFeature: string;
        licenseUT: string;
        totalCount: number;
        usageCount: number;
        userId: string;
        userName: string;
    }
    export interface IApiReqGetAdminLicenseUsageRate {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        appLicenseCode: string;
        appLicenseModels: string;
        step: number;
    }

    export interface IApiResGetAdminLicenseUsageRate {
        metric: {
            appLicenseCode: string;
            appLicenseModel: string;
        };
        values: [number, string][];
    }

    export interface IApiResGetAdminBillingAsyncExportJobList {
        intervalStartTimeEnd: string;
        intervalStartTimeStart: string;
        progress: number;
        status: string;
        taskId: string;
    }
    export interface IApiResGetAdminLicenseSessionModelList {
        licenseFeature: string;
        sessionEndDate: string;
        sessionStartDate: string;
        usageCount: number;
        usageMachineHour: number;
        usageTime: number;
        userId: string;
        userName: string;
    }

    export interface IApiResGetAdminLicenseUsagePeak {
        appLicenseCode: string;
        appLicenseModel: string;
        maxLicenseUsed: number;
        minLicenseUsed: number;
    }

    export interface IApiReqGetBillingStatisticsGroup {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        gids?: number;
    }

    export interface IApiResGetAdminBillingStatisticsCountGroupQueue {
        expandOrder: number;
        gid: number;
        groupName: string;
        queueName: string;
        queueType: string;
        time: number;
        zoneClusterQueue: string;
    }

    export interface IResGetAdminLicense {
        appLicenseCode: string;
        appLicenseCommand: string;
        appLicenseServer: string;
        appLicenseServerIP: string;
        appLicenseServerPort: number;
        appLicenseVersion: string;
        createdDate: string;
        expiryDate: string;
        id: number;
        licenseServerDate: string;
        licenseStatus: string;
        licenseType: string;
        master: boolean;
        updatedDate: string;
    }

    export interface IReqPostAdminLicenseApp {
        appLicenseCode: string;
        appLicenseServerIP: string;
        appLicenseServerPort: string;
        id: number;
        licenseType: string;
    }
    export interface IReqGetAdminBillingStatisticsProject {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        uid?: string;
        queueType?: 'CPU' | 'GPU';
    }

    export interface IResGetAdminBillingStatisticsProject {
        totalIntervalBillingGpuTime: any;
        avgIntervalPendTime: number;
        avgIntervalRunTime: number;
        billingCpuTimePercent: number;
        donePercent: number;
        jobDoneNum: number;
        jobNotDoneNum: number;
        jobNum: number;
        maxIntervalPendTime: number;
        maxIntervalRunTime: number;
        maxPendTime: number;
        maxRunTime: number;
        projectId: string;
        projectName: string;
        totalBillingCpuTime: number;
        totalElasticityIntervalBillingCpuTime: number;
        totalIntervalBillingCpuTime: number;
        totalIntervalPendTime: number;
        totalIntervalRunTime: number;
        totalNotElasticityIntervalBillingCpuTime: number;
    }

    export interface IReqGetAdminAuditLogExport {
        createdStart: string;
        createdEnd: string;
        uids: string[];
    }

    export interface IResGetSystemInfo {
        branch: string;
        commitId: string;
        commitMessage: string;
    }

    export interface IReqGetAdminViewList {
        uids?: number[];
        gids?: number[];
        states?: number[];
        types?: number[];
    }
    export interface IResGetAdminViewList {
        agentVersion: string;
        appName: string;
        catalogId: string;
        catalogName: string;
        closeDate: string;
        connectedViaHostName: string;
        connectedViaIPAddress: string;
        connectionStateChangeDate: string;
        controllerDnsName: string;
        createdDate: string;
        dns: string;
        durationTimeSS: number;
        failureCategory: number;
        failureCategoryName: string;
        failureDate: string;
        failureReason: number;
        failureReasonName: string;
        geometry: string;
        gid: number;
        id: number;
        ip: string;
        isAnonymous: boolean;
        isDeleted: boolean;
        isMultiScreen: boolean;
        isSecureIca: boolean;
        lastCollectorTime: string;
        lastConnectionDate: string;
        lastDisconnectionTime: string;
        launchedViaHostName: string;
        launchedViaIPAddress: string;
        name: string;
        nassetId: string;
        nassetName: string;
        numOfConnections: number;
        openDate: string;
        osType: string;
        protocol: string;
        rawPassword: string;
        serverGroupId: string;
        serverGroupName: string;
        sessionId: string;
        sessionIdleTime: string;
        sessionIdleTimeSS: number;
        sessionKey: string;
        sessionName: string;
        sessionSupport: number;
        uid: string;
        updatedDate: string;
        viewClientName: string;
        viewClientVersion: string;
        viewIp: string;
        viewPassword: string;
        viewPath: string;
        viewPort: number;
        viewState: string;
        viewType: string;
        viewUser: string;
        viewUserAdId: string;
        viewUserDomain: string;
        viewUserFullName: string;
        viewUserUpn: string;
    }

    export interface IReqGetAdminBillingStatisticsMonthBillProject {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        projectIds?: number[];
    }

    export interface IResGetAdminBillingStatisticsMonthBillProject {
        cpuAdjustmentCost: number;
        cpuCost: number;
        createdDate: string;
        deducted: boolean;
        gpuAdjustmentCost: number;
        gpuCost: number;
        id: number;
        month: string;
        otherAdjustmentCost: number;
        otherCost: number;
        payGid: number;
        payProjectId: number;
        payUid: string;
        projectName: string;
        totalCost: number;
        updatedDate: string;
        pricingStrategy: string;
    }

    export interface IApiReqDataGetAppealJobList {
        startTime: string;
        endTime: string;
        uidSearch?: string;
        rawJobIdSearch?: string;
        cluster?: string;
        jobAppealStatusType?: string;
        page?: number;
        size?: number;
        order?: string;
        orderBy?: string;
    }

    export type IApiResGetAdminAppealJobList =
        IApiResPagedDataModel<IApiResGetAdminAppealJobListRecord>;

    export interface IApiResGetAdminAppealJobListRecord {
        id: string;
        appeal: string;
        appealCategory: string;
        appealCategoryName: string;
        appealDate: string;
        appealStatus: string;
        cluster: string;
        disposed: boolean;
        jobId: string;
        rawJobId: string;
        uid: string;
        updatedDate: string;
        queueType: 'CPU' | 'GPU';
        slots: number;
        ngpus: number;
        walltime: number;
        cpuTime: number;
        gpuTime: number;
        jobAppealStatusType:
            | 'REQUESTED'
            | 'NO_APPEAL'
            | 'IN_COMPLAINT'
            | 'APPEALED'
            | 'NOT_APPROVED'
            | 'VOUCHER_RETURNED';
    }

    export interface IApiReqDataPutAppealJobUpdate {
        appeal?: string;
        appealCategory?: string;
        jobId: string;
    }

    export interface IApiResPomsData<T> {
        code: string;
        data: T;
        success: boolean;
        totalPages: number;
        message: string;
        totalElements: number;
        ts: number;
    }

    export interface IApiResAppealCategory {
        paaName: string;
        deleted: boolean;
        name: string;
        nameCn: string;
        remark: string;
        id: number;
        type: string;
        value: string;
        isShow: number;
    }
    export interface IReqGetAdminBillingStatisticsChartCpuTimeUsage {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        zoneClusterQueues?: string[];
        zoneClusterPlatforms?: string[];
        jobTimeUnit: string;
    }

    export interface IResGetAdminBillingStatisticsChartCpuTimeUsage {
        totalCpuTime: number;
        totalCpuTimeLimit: number;
        totalCpuTimeUsage: number;
    }

    export interface IReqGetAdminAccountUserAssignableList extends API.IApiReqPageParams {
        sshAccountId?: number;
        uidSearch?: string;
        groupNameSearch?: string;
    }
    export interface IResGetAdminAccountUserAssignableList {
        createdDate: string;
        id: number;
        sshAccountId: number;
        status: boolean;
        uid: string;
        updatedDate: string;
        username: string;
    }

    export interface IResGetAdminAccountGroupAssignableList {
        createdDate: string;
        id: number;
        sshAccountId: number;
        status: boolean;
        gid: number;
        updatedDate: string;
        valid?: boolean;
    }

    export interface IResGetAdminAccountProjectAssignableList {
        /**
         * 创建时间
         */
        createdDate?: Date;
        id?: number;
        level1?: string;
        sshAccountId?: number;
        status?: boolean;
        /**
         * 更新时间
         */
        updatedDate?: Date;
    }

    export interface IReqGetAdminBillingStorageQuotaList extends API.IApiReqPageParams {
        uidSearch?: string;
        payGids?: number;
        cluster?: number;
        billingType?: number;
    }

    export interface IResGetAdminBillingStorageQuotaList {
        billingType: string;
        cluster: string;
        createdDate: string;
        discount: number;
        endDate: string;
        id: number;
        payGid: number;
        payUid: string;
        price: number;
        stepPricingStrategy: {
            pricingStrategies: PricingStrategy[];
            unit: string;
        };
        pricingStrategyId: number;
        storagePriceId: number;
        quota: number;
        startDate: string;
        updatedDate: string;
        zone: string;
        unit: string;
        valid: boolean;
        uid: string;
        payGroupName: string;
    }

    export interface IReqGetAdminBillingStorageQuotaStatistics extends API.IApiReqPageParams {
        statisticsTimeUnit: 'DAYS' | 'MONTHS' | 'WEEKS' | 'YEARS';
        billStartDate: string;
        billEndDate: string;
        payUidSearch?: string[];
        payGids?: number[];
        billingType?: 'DAYS' | 'MONTHS' | 'YEARS';
    }

    export interface IResGetAdminBillingStorageQuotaStatistics {
        cost: number;
        payGid: number;
        payGroupName: string;
        payUid: string;
        time: number;
        unit: string;
        quota: number;
    }

    export interface IReqPostAdminBillingStorageQuota {
        billingType: 'DAY' | 'MONTH' | 'YEAR';
        cluster: string;
        discount: number;
        endDate: string;
        payGid: number;
        payUids: string;
        price: number;
        pricingStrategyId?: number;
        storagePriceId: number;
        quota: number;
        startDate: string;
        unit: string;
        zone: string;
        id: number;
        payer?: string;
        selectedPayGidUids?: string[];
        selectedGid?: number;
        timeRange?: number;
    }

    export interface IResPostAdminBillingStorageQuota {
        failuresNums: number;
        successfulNums: number;
    }

    export interface IReqPutAdminBillingStorageQuota {
        quota: number;
        id: number;
    }

    export interface IReqPutAdminZoneClusterCommandNodeLabelBind {
        zoneCode: string;
        clusterCode: string;
        nodeName: string;
        tagName: string;
        addTagNames: string[];
        deleteTagNames: string[];
    }

    interface IApiGetResAdminQueueResource {
        cluster: string;
        cpus: number;
        createdDate: string;
        elasticityQueueResources: ElasticityQueueResource[];
        elasticityQueues: string;
        gpus: number;
        id: number;
        name: string;
        queue: string;
        queueName: string;
        queueOrder: number;
        updatedDate: string;
        zone: string;
    }

    interface ElasticityQueueResource {
        cluster: string;
        queueName: string;
        zone: string;
    }

    export interface IReqGetAdminJobstatusDownloadConfigList extends API.IApiReqPageParams {
        appCode?: string;
        appVersion?: string;
    }
    export interface IResGetAdminJobstatusDownloadConfigList {
        appCode: string;
        appVersion: string;
        createdDate: string;
        fileTransferType: 'APPEND' | 'OVERRIDE';
        id: number;
        jobStatus: string;
        skipInputFile: boolean;
        status: boolean;
        updatedDate: string;
    }

    interface IReqPostAdminJobstatusDownloadConfigList {
        appCode: string;
        appVersion: string;
        fileTransferType: 'APPEND' | 'OVERRIDE';
        jobStatus: string;
        skipInputFile: boolean;
        status: boolean;
    }

    interface IApiReqGetAdminQueueAccountInfoList extends IApiReqPageParams {
        zones: string;
        clusters: string;
        queues: string;
    }

    interface IApiResGetAdminQueueAccountInfoList {
        account: string;
        cluster: string;
        createdDate: string;
        id: number;
        pendingCpuLimit: number;
        pendingGpuLimit: number;
        queue: string;
        updatedDate: string;
        zone: string;
    }

    interface IApiPostAdminQueueAccountInfoList {
        account: string;
        cluster: string;
        pendingCpuLimit: number;
        pendingGpuLimit: number;
        queue: string;
        zone: string;
    }
    interface IApiPutAdminQueueAccountInfoList {
        id?: number;
        pendingCpuLimit: number;
        pendingGpuLimit: number;
    }
    interface IApiGetAdminCustomPayGroupListReq extends IApiReqPageParams {
        cpgid?: number;
        cpgNameSearch?: string;
        uid?: string;
    }

    interface IApiGetAdminCustomPayGroupListRes {
        balance: number;
        balanceType: string;
        createdDate: string;
        id: number;
        name: string;
        totalAmount: number;
        updatedDate: string;
    }

    interface IApiGetBillingStatisticsCpuCustomPayGroupRes {
        avgIntervalPendTime: number;
        avgIntervalRunTime: number;
        billingCpuTimePercent: number;
        cloud: boolean;
        cloudUserNum: number;
        cpgid: string;
        customPayGroupName: string;
        donePercent: number;
        jobDoneNum: number;
        jobNotDoneNum: number;
        jobNum: number;
        localUserNum: number;
        maxIntervalPendTime: number;
        maxIntervalRunTime: number;
        maxPendTime: number;
        maxRunTime: number;
        totalBillingCpuTime: number;
        totalElasticityIntervalBillingCpuTime: number;
        totalIntervalBillingCpuTime: number;
        totalIntervalBillingGpuTime: number;
        totalIntervalPendTime: number;
        totalIntervalRunTime: number;
        totalNotElasticityIntervalBillingCpuTime: number;
        uid: string;
    }

    interface IApiGetAdminCustomPayGroupUserListRes {
        cpgId: number;
        id: number;
        uid: string;
        username: string;
    }

    interface IApiGetBillingStatisticsMonthBillCustomPayGroupRes extends IApiReqPageParams {
        intervalStartTimeStart?: string;
        intervalStartTimeEnd?: string;
        cpgids?: number[];
    }

    interface IApiGetBillingStatisticsMonthBillCustomPayGroupReq {
        cpuAdjustmentCost: number;
        cpuCost: number;
        createdDate: string;
        customPayGroupName: string;
        deducted: boolean;
        gpuAdjustmentCost: number;
        gpuCost: number;
        id: number;
        month: string;
        otherAdjustmentCost: number;
        otherCost: number;
        payCpgid: number;
        payGid: number;
        payProjectId: number;
        payUid: string;
        totalCost: number;
        updatedDate: string;
    }

    export interface IApiResGetAdminProjectLevel1 {
        levels: string[];
    }

    export interface IApiReqGetAdminBillingStatisticsCpuCustomPayGroupActiveUser {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
    }

    export interface IApiResGetAdminBillingStatisticsCpuCustomPayGroupActiveUser {
        avgIntervalPendTime: number;
        avgIntervalRunTime: number;
        billingCpuTimePercent: number;
        cloud: boolean;
        cloudUserNum: number;
        cpgid: string;
        customPayGroupName: string;
        donePercent: number;
        jobDoneNum: number;
        jobNotDoneNum: number;
        jobNum: number;
        localUserNum: number;
        maxIntervalPendTime: number;
        maxIntervalRunTime: number;
        maxPendTime: number;
        maxRunTime: number;
        totalBillingCpuTime: number;
        totalElasticityIntervalBillingCpuTime: number;
        totalIntervalBillingCpuTime: number;
        totalIntervalPendTime: number;
        totalIntervalRunTime: number;
        totalNotElasticityIntervalBillingCpuTime: number;
        totalUserNum: number;
        uid: string;
    }

    export type IApiReqStorageQuotaNotSetParams = IApiReqPageParams & {
        uidSearch?: string;
    };

    export type IApiResStorageQuotaNotSetUserList =
        IApiResPagedDataModel<IApiResStorageQuotaNotSetData>;

    export interface IApiResStorageQuotaNotSetData {
        allowDownload: boolean;
        description: string;
        disable: boolean;
        expandFirst: string;
        expandSecond: string;
        gid: number;
        homeDirectory: string;
        id: number;
        resign: boolean;
        role: {
            admin: boolean;
            description: string;
            id: number;
            name: string;
        };
        sid: string;
        uid: string;
        userScope: string;
        username: string;
    }

    export interface IApiReqGetAdminAppQueueElasticityStrategyList {
        appCode: string;
        appVersion: string;
        cpuRatio: number;
        destCluster: string;
        destQueue: string;
        destZone: string;
        origCluster: string;
        origQueue: string;
        origZone: string;
    }
    export interface IApiResGetAdminAppQueueElasticityStrategyList {
        appCode: string;
        appVersion: string;
        cpuRatio: number;
        createdDate: string;
        destCluster: string;
        destQueue: string;
        destZone: string;
        id: number;
        origCluster: string;
        origQueue: string;
        origZone: string;
        queueCpuConvertStrategy: string;
        updatedDate: string;
    }

    export interface IApiReqPostAdminAppQueueElasticityStrategy {
        appCode: string;
        appVersion: string;
        cpuRatio: number;
        destCluster: string;
        destQueue: string;
        destZone: string;
        origCluster: string;
        origQueue: string;
        origZone: string;
        queueCpuConvertStrategy: string;
    }
    export interface IApiReqPutAdminAppQueueElasticityStrategy {
        appCode: string;
        appVersion: string;
        cpuRatio: number;
        destCluster: string;
        destQueue: string;
        destZone: string;
        id: number;
        origCluster: string;
        origQueue: string;
        origZone: string;
        queueCpuConvertStrategy: string;
    }

    export interface IApiReqDeleteAdminAppQueueElasticityStrategy {
        id: number;
    }
    export interface IApiReqGetAdminBillingStatisticsMonthBill extends IApiReqPageParams {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        uidSearch?: string;
    }

    export interface IApiResGetAdminBillingStatisticsMonthBill {
        payUid: string;
        month: string;
        jobCost: number;
        storageQuotaCost: number;
        totalCost: number;
    }

    export interface IApiReqGetAdminBillingBillPayuidList extends IApiReqPageParams {
        payUid?: string;
        billStartDate?: string;
        billEndDate?: string;
    }

    export interface IApiResGetAdminBillingBillPayuidList {
        billEndDate: string;
        billStartDate: string;
        createdDate: string;
        id: number;
        jobCost: number;
        payCpgid: number;
        payGid: number;
        payProjectId: number;
        payUid: string;
        storageQuotaCost: number;
        totalCost: number;
        updatedDate: string;
    }

    export interface IApiReqGetAdminBillingBillPayuidJob extends IApiReqPageParams {
        payUid?: string;
        billStartDate?: string;
        billEndDate?: string;
    }

    export interface IApiResGetAdminBillingBillPayuidJob {
        billEndDate: string;
        billStartDate: string;
        cluster: string;
        cpuCost: number;
        cpuDiscount: number;
        cpuTime: number;
        customPayGroupName: string;
        gid: number;
        gpuCost: number;
        gpuDiscount: number;
        gpuTime: number;
        groupName: string;
        intervalRunTime: number;
        jobName: string;
        ncpus: number;
        ngpus: number;
        payCpgid: number;
        payGid: number;
        payProjectId: number;
        payUid: string;
        pricingStrategy: string;
        projectId: number;
        projectName: string;
        queue: string;
        queueCpuPrice: number;
        queueGpuPrice: number;
        rawJobId: string;
        sccUser: string;
        software: string;
        uid: string;
        zone: string;
    }

    export interface IApiResGetAdminBillingBillPayuidStorage {
        billEndDate: string;
        billStartDate: string;
        billingType: string;
        cluster: string;
        cost: number;
        createdDate: string;
        deducted: boolean;
        discount: number;
        discountQuota: number;
        endDate: string;
        id: number;
        payGid: number;
        payUid: string;
        price: number;
        pricingStrategy: PricingStrategy[];
        quota: number;
        startDate: string;
        storageQuotaId: number;
        unit: string;
        unitPrice: number;
        updatedDate: string;
        zone: string;
    }
    export interface IApiReqGetProjectResourcesAssigned {
        queueResourceId: number;
    }
    export interface IApiResGetProjectResourcesAssigned {
        createdDate: string;
        id: number;
        projectId: number;
        projetName: string;
        queueResourceId: number;
        status: boolean;
        updatedDate: string;
    }
    export interface IApiResGetProjectResourcesAssignable {
        createdDate: string;
        defaultPendingChangeQueueMinutes: number;
        description: string;
        disabled: boolean;
        enableEditFastCalculationTime: boolean;
        enableFastCalculationTimeEnd: number;
        enableFastCalculationTimeStart: number;
        fastCalculationType: string;
        id: number;
        level1: string;
        name: string;
        open: boolean;
        sid: string;
        updatedDate: string;
    }

    export interface IApiReqAssignProjectResources {
        projectIds: number[];
        queueResourceId: number[];
    }
    export interface IApiDeleteAssignProjectResources {
        projectResourceIds: number[];
    }

    export interface IApiResGetAdminProjectQueueResourcesAssigned {
        cluster: string;
        cpus: number;
        createdDate: string;
        elasticityQueueResources: ElasticityQueueResource[];
        elasticityQueues: string;
        gpus: number;
        id: number;
        name: string;
        queue: string;
        queueName: string;
        queueOrder: number;
        queueType: string;
        updatedDate: string;
        zone: string;
    }

    export interface IApiReqGetJobCpuHistory extends IApiReqPageParams {
        intervalStartTimeStart: string;
        intervalStartTimeEnd: string;
        zones?: string[];
        platforms?: string[];
        queues?: string[];
        uids?: string[];
        gids?: string[];
        softwares?: string[];
    }
    export interface IApiResGetJobCpuHistory {
        errorLimit: any[];
        jobCpuTimeStatistics: JobCpuTimeStatistics;
        licenseLimit: any[];
        otherLimit: any[];
        personalLimit: any[];
        queueJob: any[];
        resourceLimit: any[];
        runCore: any[];
        runJob: any[];
        submitJob: any[];
        totalCore: any[];
        x: any[];
        exclusiveLimit: string[];
    }
    interface JobCpuTimeStatistics {
        totalCpuTime: number;
        totalCpuTimeLimit: number;
        totalCpuTimeUsage: number;
    }
    export interface IApiResGetJobGpuHistory {
        errorLimit: any[];
        jobGpuTimeStatistics: JobGpuTimeStatistics;
        licenseLimit: any[];
        otherLimit: any[];
        personalLimit: any[];
        queueJob: any[];
        resourceLimit: any[];
        runGpu: any[];
        runJob: any[];
        submitJob: any[];
        totalGpu: any[];
        x: any[];
        exclusiveLimit: string[];
    }

    interface JobGpuTimeStatistics {
        totalGpuTime: number;
        totalGpuTimeLimit: number;
        totalGpuTimeUsage: number;
    }

    interface IApiReqGetJobStatusPage extends IApiReqPageParams {
        clusters?: string;
        jobStatus?: string;
        jobUserSearch?: string;
    }

    interface IApiResGetJobStatusPage {
        /**
         * 是否手动传输
         */
        activeManualTransfer?: boolean;
        allChildJobEnd?: boolean;
        /**
         * 应用编码
         */
        appCode?: string;
        /**
         * 作业提交方式
         */
        appSubmitType?: AppSubmitType;
        averageDownlondSpeed?: number;
        averageUploadSpeed?: number;
        /**
         * 批量作业ID
         */
        batchId?: string;
        /**
         * 批量作业组名
         */
        batchName?: string;
        /**
         * 批量作业编号
         */
        batchNo?: number;
        /**
         * 计费类型
         */
        billingType?: BillingType;
        /**
         * 计算精度
         */
        calculationAccuracy?: string;
        /**
         * 是否转换队列
         */
        changeQueue?: boolean;
        /**
         * 子作业任务列表
         */
        childJobTasks?: JobTask[];
        /**
         * 是否是云端作业， 1：是, 0: 否
         */
        cloud?: boolean;
        /**
         * 作业集群
         */
        cluster?: string;
        code?: number;
        /**
         * 作业描述
         */
        comment?: string;
        /**
         * cpu核时
         */
        cpuTime?: number;
        /**
         * 创建时间
         */
        createdDate?: Date;
        /**
         * 是否删除
         */
        delete?: boolean;
        /**
         * 部门标签
         */
        departmentTag?: string;
        /**
         * 作业是否完成
         */
        done?: boolean;
        /**
         * 是否对结果文件进行解压
         */
        doneUnZip?: boolean;
        /**
         * 下载结束时间
         */
        downloadEndDate?: Date;
        /**
         * 下载开始时间
         */
        downloadStartDate?: Date;
        /**
         * 文件下载时间
         */
        downloadTime?: number;
        /**
         * 下行优先级
         */
        downloadTransferPriority?: LoadTransferPriority;
        downlondPendingTime?: number;
        downlondTime?: number;
        /**
         * 是否是弹性队列
         */
        elasticityQueue?: boolean;
        /**
         * 结束时间
         */
        endTime?: number;
        /**
         * 作业审核列表
         */
        eventApplies?: EventApply[];
        /**
         * 作业定时开始时间
         */
        executionTime?: number;
        /**
         * 作业退出状态
         */
        exitCode?: string;
        /**
         * 文件删除状态
         */
        fileDelete?: number;
        /**
         * 用户组
         */
        gid?: number;
        /**
         * gpu卡时
         */
        gpuTime?: number;
        /**
         * 否为第一条作业
         */
        groupFirst?: boolean;
        /**
         * 作业优先级
         */
        higher?: number;
        /**
         * 是否批量提交
         */
        isBatch?: boolean;
        /**
         * 作业申诉状态
         */
        jobAppealStatusType?: JobAppealStatusType;
        /**
         * 作业备注
         */
        jobDesc?: string;
        /**
         * 作业编号
         */
        jobId?: string;
        /**
         * 作业名称
         */
        jobName?: string;
        /**
         * 作业原始状态
         */
        jobOriginalStatus?: string;
        /**
         * 作业参数
         */
        jobParams?: string;
        /**
         * 作业密级
         */
        jobSecurityLevel?: JobSecurityLevel;
        /**
         * 作业状态
         */
        jobStatus?: string;
        /**
         * 节点优先级
         */
        jobTaskPriority?: JobTaskPriority;
        /**
         * 作业标签列表
         */
        jobTaskTags?: JobTaskTag[];
        /**
         * 作业类型
         */
        jobType?: JobType;
        /**
         * 作业提交账号
         */
        jobUser?: string;
        lastAccessDate?: Date;
        /**
         * 作业文件延迟删除时间
         */
        lazyDeleteFileTime?: number;
        /**
         * 本地日志路径
         */
        localLogPath?: string;
        /**
         * 作业本地输出集群
         */
        localOutputCluster?: string;
        /**
         * 作业本地输出区域
         */
        localOutputZone?: string;
        /**
         * 作业本地同步路径
         */
        localPath?: string;
        /**
         * 作业本地同步基础路径
         */
        localPathBase?: string;
        /**
         * 作业本地开始运行时间
         */
        localStartTime?: number;
        /**
         * 作业日志路径
         */
        logPath?: string;
        /**
         * 临时日志路径
         */
        logPathTmp?: string;
        /**
         * mpi类型
         */
        mpi?: string;
        msg?: string;
        nextAccessDate?: Date;
        /**
         * 作业gpu核数
         */
        ngpus?: number;
        /**
         * 作业节点
         */
        nodes?: string;
        /**
         * 输出文件匹配规则
         */
        outputFileDoneMatch?: string;
        /**
         * 输出文件排除匹配规则
         */
        outputFileExcludeDoneMatch?: string;
        /**
         * 输出文件运行排除匹配规则
         */
        outputFileExcludeRunMatch?: string;
        /**
         * 输出文件运行匹配规则
         */
        outputFileRunMatch?: string;
        /**
         * 作业输出目录
         */
        outPutPath?: string;
        /**
         * 作业输出临时目录
         */
        outPutPathTmp?: string;
        /**
         * 父作业ID
         */
        parentJobId?: string;
        /**
         * 支付用户组id
         */
        payGid?: number;
        /**
         * 支付项目组id
         */
        payProjectId?: number;
        /**
         * 支付用户id
         */
        payUid?: string;
        /**
         * PBS平台
         */
        pbsPlatform?: string;
        /**
         * 切换队列等待时长
         */
        pendingChangeQueueTime?: number;
        /**
         * 队列排队次序
         */
        pendingOrder?: number;
        /**
         * 队列排队核数
         */
        pendingSlots?: number;
        /**
         * 排队时间
         */
        pendingTime?: number;
        /**
         * 排队原因
         */
        pendReason?: string;
        /**
         * 排队原因原文
         */
        pendReasonDesc?: string;
        /**
         * 排队原因类型
         */
        pendReasonKey?: string;
        /**
         * 百分比
         */
        percentage?: number;
        /**
         * 作业平台别名
         */
        platform?: string;
        /**
         * 作业任务上一个状态
         */
        preStatus?: Status;
        /**
         * 节点优先级
         */
        priority?: number;
        /**
         * 项目ID
         */
        projectId?: number;
        /**
         * 项目level1
         */
        projectLevel1?: string;
        /**
         * 项目名
         */
        projectName?: string;
        /**
         * 队列
         */
        queue?: string;
        /**
         * 队列类型
         */
        queueType?: QueueType;
        /**
         * 真实作业编号
         */
        rawJobId?: string;
        /**
         * 作业原因
         */
        reason?: string;
        /**
         * 真实作业根编号
         */
        rootRawJobId?: string;
        /**
         * 当前上传文件大小(byte)
         */
        runTotalCurrent?: number;
        /**
         * 上传文件总大小(byte)
         */
        runTotalLength?: number;
        /**
         * 作业同步状态
         */
        showSyncStatus?: ShowSyncStatus;
        simrightPercentage?: number;
        /**
         * 作业simright状态
         */
        simrightState?: SimrightState;
        /**
         * 作业核数
         */
        slots?: number;
        /**
         * 作业申请核数
         */
        slotsRequested?: number;
        /**
         * 作业软件
         */
        software?: string;
        /**
         * 软件模块
         */
        softwareModule?: string;
        /**
         * 同步类型
         */
        speedType?: SpeedType;
        /**
         * 作业开始时间
         */
        startTime?: number;
        /**
         * 作业任务状态
         */
        status?: Status;
        /**
         * 提交时间
         */
        submitTime?: number;
        /**
         * 作业输入文件数量
         */
        totalInputFileCount?: number;
        /**
         * 作业输入文件大小
         */
        totalInputFileSize?: number;
        /**
         * 作业输出文件数量
         */
        totalOutputFileCount?: number;
        /**
         * 作业输出文件大小
         */
        totalOutputFileSize?: number;
        /**
         * 传输速度
         */
        totalSpeed?: number;
        /**
         * 作业同步文件大小数量
         */
        totalSyncFileCount?: number;
        /**
         * 作业同步文件大小
         */
        totalSyncFileSize?: number;
        uid?: string;
        /**
         * 更新时间
         */
        updatedDate?: Date;
        updateTime?: number;
        /**
         * 作业上传基本路径
         */
        uploadBasePath?: string;
        /**
         * 上传结束时间
         */
        uploadEndDate?: Date;
        uploadPendingTime?: number;
        /**
         * 上传开始时间
         */
        uploadStartDate?: Date;
        /**
         * 文件上传时间
         */
        uploadTime?: number;
        /**
         * 上行优先级
         */
        uploadTransferPriority?: LoadTransferPriority;
        /**
         * 应用版本
         */
        version?: string;
        /**
         * 可视化子作业版本
         */
        viewChildAppVersion?: string;
        /**
         * 可视化提醒
         */
        viewRemind?: boolean;
        /**
         * 作业运行时长(单位:秒)
         */
        walltime?: number;
        /**
         * 作业工作目录
         */
        workDir?: string;
        /**
         * 作业区域
         */
        zone?: string;
        /**
         * 集群接入类型
         */
        zoneAccessType?: ZoneAccessType;
        [property: string]: any;
    }

    /**
     * 作业提交方式
     */
    export enum AppSubmitType {
        Calculate = 'CALCULATE',
        View = 'VIEW',
    }

    /**
     * 计费类型
     */
    export enum BillingType {
        FixedLease = 'FIXED_LEASE',
        FixedYearLease = 'FIXED_YEAR_LEASE',
        JobCores = 'JOB_CORES',
        None = 'NONE',
    }

    /**
     * JobTask，作业详请
     */
    export interface JobTask {
        /**
         * 应用编码
         */
        appCode?: string;
        /**
         * 作业提交方式
         */
        appSubmitType?: AppSubmitType;
        /**
         * 批量作业ID
         */
        batchId?: string;
        /**
         * 批量作业组名
         */
        batchName?: string;
        /**
         * 批量作业编号
         */
        batchNo?: number;
        /**
         * 计费类型
         */
        billingType?: BillingType;
        /**
         * 计算精度
         */
        calculationAccuracy?: string;
        /**
         * 是否转换队列
         */
        changeQueue?: boolean;
        /**
         * 是否是云端作业， 1：是, 0: 否
         */
        cloud?: boolean;
        /**
         * 作业集群
         */
        cluster?: string;
        code?: number;
        /**
         * 作业描述
         */
        comment?: string;
        /**
         * cpu核时
         */
        cpuTime?: number;
        /**
         * 创建时间
         */
        createdDate?: Date;
        /**
         * 是否删除
         */
        delete?: boolean;
        /**
         * 部门标签
         */
        departmentTag?: string;
        /**
         * 作业是否完成
         */
        done?: boolean;
        /**
         * 是否对结果文件进行解压
         */
        doneUnZip?: boolean;
        /**
         * 下载结束时间
         */
        downloadEndDate?: Date;
        /**
         * 下载开始时间
         */
        downloadStartDate?: Date;
        /**
         * 文件下载时间
         */
        downloadTime?: number;
        /**
         * 是否是弹性队列
         */
        elasticityQueue?: boolean;
        /**
         * 结束时间
         */
        endTime?: number;
        /**
         * 作业定时开始时间
         */
        executionTime?: number;
        /**
         * 作业退出状态
         */
        exitCode?: string;
        /**
         * 文件删除状态
         */
        fileDelete?: number;
        /**
         * 用户组
         */
        gid?: number;
        /**
         * gpu卡时
         */
        gpuTime?: number;
        /**
         * 否为第一条作业
         */
        groupFirst?: boolean;
        /**
         * 作业优先级
         */
        higher?: number;
        /**
         * 是否批量提交
         */
        isBatch?: boolean;
        /**
         * 作业备注
         */
        jobDesc?: string;
        /**
         * 作业编号
         */
        jobId?: string;
        /**
         * 作业名称
         */
        jobName?: string;
        /**
         * 作业原始状态
         */
        jobOriginalStatus?: string;
        /**
         * 作业密级
         */
        jobSecurityLevel?: JobSecurityLevel;
        /**
         * 作业状态
         */
        jobStatus?: string;
        /**
         * 节点优先级
         */
        jobTaskPriority?: JobTaskPriority;
        /**
         * 作业类型
         */
        jobType?: JobType;
        /**
         * 作业提交账号
         */
        jobUser?: string;
        lastAccessDate?: Date;
        /**
         * 作业文件延迟删除时间
         */
        lazyDeleteFileTime?: number;
        /**
         * 本地日志路径
         */
        localLogPath?: string;
        /**
         * 作业本地输出集群
         */
        localOutputCluster?: string;
        /**
         * 作业本地输出区域
         */
        localOutputZone?: string;
        /**
         * 作业本地同步路径
         */
        localPath?: string;
        /**
         * 作业本地同步基础路径
         */
        localPathBase?: string;
        /**
         * 作业本地开始运行时间
         */
        localStartTime?: number;
        /**
         * 作业日志路径
         */
        logPath?: string;
        /**
         * mpi类型
         */
        mpi?: string;
        msg?: string;
        nextAccessDate?: Date;
        /**
         * 作业gpu核数
         */
        ngpus?: number;
        /**
         * 作业节点
         */
        nodes?: string;
        /**
         * 输出文件匹配规则
         */
        outputFileDoneMatch?: string;
        /**
         * 输出文件排除匹配规则
         */
        outputFileExcludeDoneMatch?: string;
        /**
         * 输出文件运行排除匹配规则
         */
        outputFileExcludeRunMatch?: string;
        /**
         * 输出文件运行匹配规则
         */
        outputFileRunMatch?: string;
        /**
         * 作业输出目录
         */
        outPutPath?: string;
        /**
         * 作业输出临时目录
         */
        outPutPathTmp?: string;
        /**
         * 父作业ID
         */
        parentJobId?: string;
        /**
         * 支付用户组id
         */
        payGid?: number;
        /**
         * 支付项目组id
         */
        payProjectId?: number;
        /**
         * 支付用户id
         */
        payUid?: string;
        /**
         * PBS平台
         */
        pbsPlatform?: string;
        /**
         * 切换队列等待时长
         */
        pendingChangeQueueTime?: number;
        /**
         * 队列排队次序
         */
        pendingOrder?: number;
        /**
         * 队列排队核数
         */
        pendingSlots?: number;
        /**
         * 排队时间
         */
        pendingTime?: number;
        /**
         * 排队原因
         */
        pendReason?: string;
        /**
         * 排队原因原文
         */
        pendReasonDesc?: string;
        /**
         * 排队原因类型
         */
        pendReasonKey?: string;
        /**
         * 作业平台别名
         */
        platform?: string;
        /**
         * 作业任务上一个状态
         */
        preStatus?: Status;
        /**
         * 节点优先级
         */
        priority?: number;
        /**
         * 项目ID
         */
        projectId?: number;
        /**
         * 项目level1
         */
        projectLevel1?: string;
        /**
         * 项目名
         */
        projectName?: string;
        /**
         * 队列
         */
        queue?: string;
        /**
         * 队列类型
         */
        queueType?: QueueType;
        /**
         * 真实作业编号
         */
        rawJobId?: string;
        /**
         * 作业原因
         */
        reason?: string;
        /**
         * 真实作业根编号
         */
        rootRawJobId?: string;
        /**
         * 作业同步状态
         */
        showSyncStatus?: ShowSyncStatus;
        /**
         * 作业核数
         */
        slots?: number;
        /**
         * 作业申请核数
         */
        slotsRequested?: number;
        /**
         * 作业软件
         */
        software?: string;
        /**
         * 软件模块
         */
        softwareModule?: string;
        /**
         * 作业开始时间
         */
        startTime?: number;
        /**
         * 作业任务状态
         */
        status?: Status;
        /**
         * 提交时间
         */
        submitTime?: number;
        /**
         * 作业输入文件数量
         */
        totalInputFileCount?: number;
        /**
         * 作业输入文件大小
         */
        totalInputFileSize?: number;
        /**
         * 作业输出文件数量
         */
        totalOutputFileCount?: number;
        /**
         * 作业输出文件大小
         */
        totalOutputFileSize?: number;
        /**
         * 作业同步文件大小数量
         */
        totalSyncFileCount?: number;
        /**
         * 作业同步文件大小
         */
        totalSyncFileSize?: number;
        uid?: string;
        /**
         * 更新时间
         */
        updatedDate?: Date;
        /**
         * 作业上传基本路径
         */
        uploadBasePath?: string;
        /**
         * 上传结束时间
         */
        uploadEndDate?: Date;
        /**
         * 上传开始时间
         */
        uploadStartDate?: Date;
        /**
         * 文件上传时间
         */
        uploadTime?: number;
        /**
         * 应用版本
         */
        version?: string;
        /**
         * 作业运行时长(单位:秒)
         */
        walltime?: number;
        /**
         * 作业工作目录
         */
        workDir?: string;
        /**
         * 作业区域
         */
        zone?: string;
        /**
         * 集群接入类型
         */
        zoneAccessType?: ZoneAccessType;
        [property: string]: any;
    }

    /**
     * 作业密级
     */
    export enum JobSecurityLevel {
        Confidential = 'CONFIDENTIAL',
        Internal = 'INTERNAL',
        Public = 'PUBLIC',
        Restricted = 'RESTRICTED',
    }

    /**
     * 节点优先级
     */
    export enum JobTaskPriority {
        High = 'HIGH',
        Low = 'LOW',
        Medium = 'MEDIUM',
    }

    /**
     * 作业类型
     */
    export enum JobType {
        Cmd = 'CMD',
        GUI = 'GUI',
        Stream = 'STREAM',
    }

    /**
     * 作业任务上一个状态
     *
     * 作业任务状态
     */
    export enum Status {
        Cancel = 'CANCEL',
        DownloadError = 'DOWNLOAD_ERROR',
        DownloadInit = 'DOWNLOAD_INIT',
        DownloadPend = 'DOWNLOAD_PEND',
        DownloadSuspend = 'DOWNLOAD_SUSPEND',
        Downloading = 'DOWNLOADING',
        End = 'END',
        Error = 'ERROR',
        Init = 'INIT',
        JobEnd = 'JOB_END',
        Prep = 'PREP',
        Run = 'RUN',
        SubmitPend = 'SUBMIT_PEND',
        UploadError = 'UPLOAD_ERROR',
        UploadPend = 'UPLOAD_PEND',
        UploadSuspend = 'UPLOAD_SUSPEND',
        Uploading = 'UPLOADING',
        Wait = 'WAIT',
    }

    /**
     * 队列类型
     */
    export enum QueueType {
        CPU = 'CPU',
        GPU = 'GPU',
    }

    /**
     * 作业同步状态
     */
    export enum ShowSyncStatus {
        DownloadCancel = 'DOWNLOAD_CANCEL',
        DownloadEnd = 'DOWNLOAD_END',
        DownloadError = 'DOWNLOAD_ERROR',
        DownloadPend = 'DOWNLOAD_PEND',
        DownloadSuspend = 'DOWNLOAD_SUSPEND',
        DownloadUn = 'DOWNLOAD_UN',
        Downloading = 'DOWNLOADING',
        UploadCancel = 'UPLOAD_CANCEL',
        UploadEnd = 'UPLOAD_END',
        UploadError = 'UPLOAD_ERROR',
        UploadPend = 'UPLOAD_PEND',
        UploadSuspend = 'UPLOAD_SUSPEND',
        UploadUn = 'UPLOAD_UN',
        Uploading = 'UPLOADING',
    }

    /**
     * 集群接入类型
     */
    export enum ZoneAccessType {
        Internet = 'INTERNET',
        LocalAreaNetwork = 'LOCAL_AREA_NETWORK',
        ParaGateway = 'PARA_GATEWAY',
    }

    /**
     * 下行优先级
     *
     * 上行优先级
     */
    export enum LoadTransferPriority {
        Normal = 'NORMAL',
        Urgent = 'URGENT',
    }

    /**
     * EventApply
     */
    export interface EventApply {
        applyAction?: ApplyAction;
        applyHigher?: number;
        applyQueue?: string;
        batchApply?: boolean;
        batchRawJobIds?: string;
        changeZone?: boolean;
        /**
         * 创建时间
         */
        createdDate?: Date;
        eventId?: string;
        eventStatus?: EventStatus;
        id?: number;
        jobId?: string;
        newJobId?: string;
        newRawJobId?: string;
        notifyActionType?: ApplyAction;
        notifyEventType?: NotifyEventType;
        rawJobId?: string;
        receiver?: string;
        uid?: string;
        /**
         * 更新时间
         */
        updatedDate?: Date;
        uuid?: string;
        [property: string]: any;
    }

    export enum ApplyAction {
        CitrixSession = 'CITRIX_SESSION',
        DcvSession = 'DCV_SESSION',
        JobApplyChangeQueue = 'JOB_APPLY_CHANGE_QUEUE',
        JobApplyHigher = 'JOB_APPLY_HIGHER',
        JobMemoryUtilization = 'JOB_MEMORY_UTILIZATION',
        JobNodeUtilization = 'JOB_NODE_UTILIZATION',
        JobPerformance = 'JOB_PERFORMANCE',
        JobStatus = 'JOB_STATUS',
        StorageRate = 'STORAGE_RATE',
    }

    export enum EventStatus {
        Approval = 'APPROVAL',
        Pass = 'PASS',
        Reject = 'REJECT',
    }

    export enum NotifyEventType {
        Job3DvncNode = 'JOB_3DVNC_NODE',
        JobCPUNodeUtilization = 'JOB_CPU_NODE_UTILIZATION',
        JobCancel = 'JOB_CANCEL',
        JobDone = 'JOB_DONE',
        JobFail = 'JOB_FAIL',
        JobMemoryNodeUtilization = 'JOB_MEMORY_NODE_UTILIZATION',
        JobPending = 'JOB_PENDING',
        JobPendingInterval = 'JOB_PENDING_INTERVAL',
        JobRunInterval = 'JOB_RUN_INTERVAL',
        JobRunTimeout = 'JOB_RUN_TIMEOUT',
        JobRunUnitHour = 'JOB_RUN_UNIT_HOUR',
        JobRuntimeKill = 'JOB_RUNTIME_KILL',
        JobWaiting = 'JOB_WAITING',
        StorageRateExcessive = 'STORAGE_RATE_EXCESSIVE',
    }

    /**
     * 作业申诉状态
     */
    export enum JobAppealStatusType {
        Appealed = 'APPEALED',
        InComplaint = 'IN_COMPLAINT',
        NoAppeal = 'NO_APPEAL',
        NotApproved = 'NOT_APPROVED',
        Requested = 'REQUESTED',
        VoucherReturned = 'VOUCHER_RETURNED',
    }

    /**
     * JobTaskTag
     */
    export interface JobTaskTag {
        /**
         * 创建时间
         */
        createdDate?: Date;
        id?: number;
        jobId?: string;
        jobTagOptionName?: string;
        jobTagOptionValue?: string;
        jobTagTypeId?: number;
        jobTagTypeKey?: string;
        jobTagTypeName?: string;
        statisticExpandType?: StatisticExpandType;
        /**
         * 更新时间
         */
        updatedDate?: Date;
        [property: string]: any;
    }

    export enum StatisticExpandType {
        Expand1 = 'EXPAND1',
        Expand2 = 'EXPAND2',
        Expand3 = 'EXPAND3',
        Expand4 = 'EXPAND4',
        Expand5 = 'EXPAND5',
    }

    /**
     * 作业simright状态
     */
    export enum SimrightState {
        SyncCancel = 'SYNC_CANCEL',
        SyncEnd = 'SYNC_END',
        SyncError = 'SYNC_ERROR',
        SyncIng = 'SYNC_ING',
        SyncWait = 'SYNC_WAIT',
    }

    /**
     * 同步类型
     */
    export enum SpeedType {
        Download = 'DOWNLOAD',
        Upload = 'UPLOAD',
    }
    interface IApiReqGetCpuSum {
        createdStart: string;
        createdEnd: string;
        clusters: string[];
        jobStatus: string[];
        jobUserSearch: string;
        queueSearch: string;
        zoneSearch: string;
    }
    interface IApiResGetCpuSum {
        cpuNum: number;
        jobNum: number;
        jobStatus: string;
    }
    type IApiResGetCpuSumType = Record<
        'RUN' | 'PENDING' | 'WAITING' | 'EXECUTION' | 'STAGEOUT',
        IApiResGetCpuSum
    >;

    export interface IApiReqAppeal {
        appeal?: string;
        appeal_category?: string;
        appeal_user?: string;
        cluster_id?: string;
        cluster_job?: string;
        job_id?: string;
    }
    export interface IReqPostAdminZoneClusterCommandNode {
        /**
         * 集群编码
         */
        clusterCode: string;
        /**
         * 节点名称
         */
        nodeName: string;
        /**
         * 区域编码
         */
        zoneCode: string;
    }

    export interface IApiResGetJobResourceLogZoneList {
        appStdOutput: string;
        changeJobCommand: string;
        changeQueueCommand: string;
        clusterCode: string;
        collectUser: string;
        createdDate: Date;
        /**
         * 是否启用gpcc采集作业， 1：是, 0: 否
         */
        enableGpcc: boolean;
        id: number;
        jobCancelCommand: string;
        jobHigherCommand: string;
        jobPriorityCommand: string;
        jobResumeCommand: string;
        jobResumePendingCommand: string;
        jobSuspendCommand: string;
        jobSuspendPendingCommand: string;
        pscpCommandBase: string;
        pscpIp: string;
        pscpPort: number;
        schedulingSystem: SchedulingSystem;
        simrightAccount: string;
        simrightDirPath: string;
        simrightUserId: string;
        updatedDate: Date;
        zoneAlias: string;
        zoneCode: string;
        zoneName: string;
    }
    export interface IApiReqGetAdminJobTopByResourceTypesRequest {
        /**
         * 集群列表
         */
        clusters?: string;
        /**
         * 软件列表
         */
        jobUsers?: string;
        /**
         * 排序规则
         */
        order?: 'ASC' | 'DESC';
        /**
         * 排序字段
         */
        orderBy?: string;
        /**
         * 平台名称列表
         */
        platforms?: string;
        /**
         * 队列名称列表
         */
        queues?: string;
        /**
         * 资源名称
         */
        resources?: string;
        /**
         * 软件列表
         */
        softwares?: string;
        /**
         * 区域列表
         */
        zones?: string;
    }
    interface IApiResVoJobTaskCpuMonitorMap {
        errorLimit: string[];
        exclusiveLimit: string[];
        licenseLimit: string[];
        otherLimit: string[];
        personalLimit: string[];
        queueJob: string[];
        resourceLimit: string[];
        runCore: string[];
        runJob: string[];
        totalCore: string[];
        x: string[];
    }
    export interface IApiResGetAdminJobTopByResourceTypesResponse {
        voJobTaskCpuMonitorMap: {
            clusterQueue: IApiResVoJobTaskCpuMonitorMap;
            clusterUser: IApiResVoJobTaskCpuMonitorMap;
            resource: IApiResVoJobTaskCpuMonitorMap;
            software: IApiResVoJobTaskCpuMonitorMap;
        };
    }

    export interface IApiReqDataResourcePrice {
        expenseType: MONTH | NEED | YEAR | FOREVER_LICENSE;
        id?: number;
        name: string;
        price?: number;
        productName?: string;
        productSource?: NON_SELF_OPERATED | PURCHASED | SELF_SOURCED;
        remark?: string;
        showQuantity?: CPU_OR_GPU | NONE | QUANTITY;
        sku: string;
        type:
            | COMPUTING
            | EDGE
            | GRAPHICAL
            | LICENSE
            | NETWORK
            | PLATFORM
            | SOFTWARE
            | SOFTWARE_RENTAL
            | STORAGE
            | SWITCH
            | TECHNICAL_SERVICE;
    }

    export interface IApiResAdminBillingResourcePriceModel {
        createdDate: string;
        expenseType: MONTH | NEED | YEAR | FOREVER_LICENSE;
        id: number;
        name: string;
        price: number;
        productName: string;
        productSource: NON_SELF_OPERATED | PURCHASED | SELF_SOURCED;
        remark: string;
        saleStatus: OFF | ON | WAIT;
        showQuantity: CPU_OR_GPU | NONE | QUANTITY;
        sku: string;
        subType: CPU | GPU;
        support: EXCLUSIVE | NORMAL;
        zone: string;
        cluster: string;
        queue: string;
        type:
            | COMPUTING
            | EDGE
            | GRAPHICAL
            | LICENSE
            | NETWORK
            | PLATFORM
            | SOFTWARE
            | SOFTWARE_RENTAL
            | STORAGE
            | SWITCH
            | TECHNICAL_SERVICE;
        unit:
            | APPLICATION_SOFTWARE
            | BANDWIDTH
            | CARD_HOURS
            | CONCURRENT_SESSIONS
            | CONCURRENT_USERS
            | CORE_HOURS
            | KILOMETER
            | MODULE
            | MONTH
            | NODE
            | PERSON_DAY
            | PERSON_YEAR
            | PETABYTE
            | TERABYTE
            | UNIT
            | YEAR;
        updatedDate: string;
        vendor: string;
    }

    export interface IApiReqDataGetResourcePriceList extends IApiReqPageParams {
        skuSearch?: string;
        nameSearch?: string;
        type?: string;
        expenseType?: string;
        saleStatus?: string;
        support?: string;
    }

    export interface IApiResAdminBillingQueuesModel {
        billingType: string;
        cluster: string;
        cpuPrice: number;
        createdDate: string;
        elasticity: false;
        gpuPrice: number;
        id: number;
        name: string;
        platform: string;
        queue: string;
        queueInfo: string;
        queueType: string;
        updatedDate: string;
        zone: string;
    }

    /**
     * 排序
     */
    export enum Order {
        Asc = 'ASC',
        Desc = 'DESC',
    }
    /**
     * 计费模式
     */
    export enum ExpenseType {
        ForeverLicense = 'FOREVER_LICENSE',
        Month = 'MONTH',
        Need = 'NEED',
        Year = 'YEAR',
    }

    /**
     * 资源类型
     */
    export enum ResourcePricingType {
        Computing = 'COMPUTING',
        Edge = 'EDGE',
        Graphical = 'GRAPHICAL',
        License = 'LICENSE',
        Network = 'NETWORK',
        Platform = 'PLATFORM',
        Software = 'SOFTWARE',
        SoftwareRental = 'SOFTWARE_RENTAL',
        Storage = 'STORAGE',
        Switch = 'SWITCH',
        TechnicalService = 'TECHNICAL_SERVICE',
    }

    interface IApiReqGetAdminBillingOrderList extends IApiReqPageParams {
        /**
         * 计费模式
         */
        expenseType?: ExpenseType;
        /**
         * 排序字段
         */
        orderBy?: string;
        /**
         * 订单生效查询结束日期
         */
        orderEndTime?: string;
        /**
         * 订单编号
         */
        orderNo?: string;
        /**
         * 订单结束查询开始日期
         */
        orderStartTime?: string;
        /**
         * 页数
         */
        page?: number;
        /**
         * 资源类型
         */
        resourcePricingType?: ResourcePricingType;
        /**
         * 大小
         */
        size?: number;
        /**
         * 产品编号sku
         */
        sku?: string;
    }

    /**
     * 订单状态
     */
    export enum OrderStatus {
        Executing = 'EXECUTING',
        Terminated = 'TERMINATED',
        Unexecuted = 'UNEXECUTED',
    }

    /**
     * 计费单位
     */
    export enum Unit {
        ApplicationSoftware = 'APPLICATION_SOFTWARE',
        Bandwidth = 'BANDWIDTH',
        CardHours = 'CARD_HOURS',
        ConcurrentSessions = 'CONCURRENT_SESSIONS',
        ConcurrentUsers = 'CONCURRENT_USERS',
        CoreHours = 'CORE_HOURS',
        Kilometer = 'KILOMETER',
        Module = 'MODULE',
        Month = 'MONTH',
        Node = 'NODE',
        PersonDay = 'PERSON_DAY',
        PersonYear = 'PERSON_YEAR',
        Petabyte = 'PETABYTE',
        Terabyte = 'TERABYTE',
        Unit = 'UNIT',
        Year = 'YEAR',
    }

    interface IApiResGetAdminBillingOrderList {
        voBillingAccounts: {
            billingAccountId: number;
            billingAccountIdName: string;
        }[];
        /**
         * 基础价格(List价格)
         */
        basePrice: number;
        /**
         * cluster
         */
        cluster?: string;
        /**
         * 核心上限
         */
        cores?: number;
        createdDate?: Date;
        /**
         * 折扣(合同折扣)
         */
        discount?: number;
        /**
         * 折扣单价
         */
        discountPrice: number;
        /**
         * 是否可以编辑按需核时
         */
        enableEditDemandCpus?: boolean;
        /**
         * 结束时间
         */
        endTime?: Date;
        /**
         * 计费模式
         */
        expenseType?: ExpenseType;
        id?: number;
        /**
         * 是否已删除
         */
        isDeleted?: boolean;
        /**
         * 订单编号
         */
        orderNo?: string;
        /**
         * 订单状态
         */
        orderStatus?: OrderStatus;
        /**
         * 订单类型
         */
        orderType?: string;
        payGroupId?: number;
        /**
         * 计费资源名称
         */
        platform?: string;
        /**
         * 标准单价
         */
        price: number;
        /**
         * 数量
         */
        quantity?: number;
        /**
         * 关联队列
         */
        queue?: string;
        /**
         * 备注
         */
        remark?: string;
        /**
         * 是否自动续期 0不续期,1自动续月，2自动续年
         */
        renewal?: number;
        /**
         * 资源定价id
         */
        resourceId?: number;
        /**
         * 资源类型
         */
        resourcePricingType?: ResourcePricingType;
        /**
         * sku
         */
        sku?: string;
        /**
         * 开始时间
         */
        startTime?: Date;
        /**
         * 计费单位
         */
        unit?: Unit;
        updatedDate?: Date;
        /**
         * zone
         */
        zone?: string;
        days: number | string;
    }

    interface IApiReqExportGetAdminBillingOrderLis {
        /**
         * 计费模式
         */
        expenseType?: ExpenseType;
        /**
         * 排序
         */
        order?: Order;
        /**
         * 排序字段
         */
        orderBy?: string;
        /**
         * 订单生效查询结束日期
         */
        orderEndTime?: string;
        /**
         * 订单编号
         */
        orderNo?: string;
        /**
         * 订单结束查询开始日期
         */
        orderStartTime?: string;
        /**
         * 资源类型
         */
        resourcePricingType?: ResourcePricingType;
        /**
         * 产品编号sku
         */
        sku?: string;
    }

    interface IApiPostAdminBillingOrderList {
        /**
         * cluster
         */
        cluster?: string;
        /**
         * 折扣
         */
        discount?: number;
        /**
         * 折扣价
         */
        discountPrice?: number;
        /**
         * 结束时间
         */
        endTime?: string;
        /**
         * 付费组id
         */
        payGroupId?: number;
        /**
         * 数量
         */
        quantity?: number;
        /**
         * queue(关联队列)
         */
        queue?: string;
        /**
         * 备注
         */
        remark?: string;
        /**
         * 资源ID
         */
        resourceId?: number;
        /**
         * 开始时间
         */
        startTime?: string;
        /**
         * zone
         */
        zone?: string;
    }

    interface IApiPutAdminBillingOrderList {
        /**
         * 折扣
         */
        discount?: number;
        /**
         * 折扣价
         */
        discountPrice?: number;
        /**
         * 结束时间
         */
        endTime?: string;
        /**
         * 订单ID
         */
        orderId?: number;
        /**
         * 数量
         */
        quantity?: number;
        /**
         * 备注
         */
        remark?: string;
        /**
         * 开始时间
         */
        startTime?: string;
    }
    interface IApiDeleteAdminBillingOrderList {
        id: number;
    }

    interface IApiReqGetAdminBillingBillingAccount extends IApiReqPageParams {
        /**
         * 计费账号id
         */
        id?: number;
    }

    interface IApiResGetAdminBillingBillingAccount {
        /**
         * 计费账号id
         */
        id?: number;
        /**
         * 计费账号名称
         */
        name?: string;
    }

    export interface IApiReqGetAdminParamonNodeMonitor {
        cluster: string;
        nodes?: string;
    }

    export interface IApiResAdminParamonMetricModel {
        metric: {
            /** 指标类型 */
            type:
                | 'gflops'
                | 'memthr'
                | 'memthw'
                | 'memthrw'
                | 'cpuutil'
                | 'memutil'
                | 'ibrecei'
                | 'ibtrans'
                | 'ethrecei'
                | 'ethtrans'
                | 'diskrw'
                | 'diskro';
            /** 节点名 */
            instance: string;
        };
        /** 历史指标，job/monitor接口用 */
        values: [number, string][];
        /** 实时指标，node/monitor接口用 */
        value: [number, string];
    }

    export interface IApiReqGetAdminParamonJobMonitor {
        jobId: string;
        /** 步长，单位：秒 */
        step?: number;
        /** 开始时间(iso格式字符串) */
        start?: string;
        /** 结束时间(iso格式字符串) */
        end?: string;
    }

    export interface IApiReqGetAdminTransferQueueList extends IApiReqPageParams {
        fileTransferStatuses?: 'CANCEL' | 'DONE' | 'FAIL' | 'INIT' | 'RUN';
        manual?: boolean;
        rawJobIdSearch?: string;
        transferPriorities?: string[];
        zones?: string[];
    }

    export interface IApiResGetAdminTransferQueueList {
        /**
         * 集群
         */
        cluster?: string;
        /**
         * 同步方式
         */
        fileTransferMethod?: FileTransferMethod;
        /**
         * 同步状态
         */
        fileTransferStatus?: FileTransferStatus;
        /**
         * 组ID
         */
        gid?: number;
        /**
         * 传输任务ID
         */
        id?: number;
        /**
         * 作业索引号
         */
        jobId?: string;
        /**
         * 作业名称
         */
        jobName?: string;
        /**
         * 同步等待排名
         */
        order?: number;
        /**
         * 同步进度
         */
        percentage?: number;
        /**
         * 真实作业ID
         */
        rawJobId?: string;
        /**
         * 同步时间(ms)
         */
        syncTime?: number;
        /**
         * 总共文件大小
         */
        total?: number;
        /**
         * 同步速度
         */
        totalSpeed?: number;
        /**
         * 总共文件数量
         */
        totalSyncFileCount?: number;
        /**
         * 同步时间
         */
        transferDate?: Date;
        /**
         * 同步排队时间(ms)
         */
        transferPendingTime?: number;
        /**
         * 同步优先级
         */
        transferPriority?: TransferPriority;
        /**
         * 同步开始时间
         */
        transferStartDate?: Date;
        /**
         * 用户账号
         */
        uid?: string;
        /**
         * 分区
         */
        zone?: string;
    }

    export interface IApiReqGetAdminTransferQueueCount {
        manual?: boolean;
        rawJobIdSearch?: string;
        zones?: string[];
    }

    export interface IApiResGetAdminTransferQueueCount {
        cluster?: string;
        /**
         * 下行数量
         */
        downloads?: number;
        /**
         * 下行上限
         */
        downloadTotal?: number;
        /**
         * 上行数量
         */
        uploads?: number;
        /**
         * 上行上限
         */
        uploadTotal?: number;
        /**
         * 分区
         */
        zone?: string;
    }

    export interface IApiReqGetAdminBillingBillingAccountStatisticsBillMonthChart {
        /**
         * 结束时间
         */
        intervalStartTimeEnd: string;
        /**
         * 开始时间
         */
        intervalStartTimeStart: string;
        /**
         * 计费账号
         */
        billAccountIds?: number;
    }

    export interface IApiReqGetAdminBillingBillingAccountStatisticsOrderBillOverview
        extends IApiReqPageParams {
        /**
         * 计费账号
         */
        billingAccountId: number;
        /**
         * 计费模式
         */
        expenseTypes?: ExpenseTypes;
        /**
         * 结束时间
         */
        intervalStartTimeEnd: string;
        /**
         * 开始时间
         */
        intervalStartTimeStart: string;
        /**
         * order
         */
        order?: Order;
        /**
         * orderBy
         */
        orderBy?: string;
        /**
         * 订单号
         */
        orderNoSearch?: string;
        /**
         * page
         */
        page?: number;
        /**
         * 资源类型
         */
        resourcePricingTypes?: ResourcePricingTypes;
        /**
         * size
         */
        size?: number;
        /**
         * sku
         */
        skuSearch?: string;
    }
    export interface IApiReqGetAdminBillingBillingAccountStatisticsBillYearChart
        extends IApiReqPageParams {
        /**
         * 计费账号
         */
        billingAccountId: string;
        /**
         * 结束时间
         */
        intervalStartTimeEnd: string;
        /**
         * 开始时间
         */
        intervalStartTimeStart: string;
    }

    export interface IApiReqGetAdminBillingBillingAccountStatisticsBillYearList {
        /**
         * 计费账号
         */
        billingAccountId?: number;
        /**
         * 结束时间
         */
        intervalStartTimeEnd: string;
        /**
         * 开始时间
         */
        intervalStartTimeStart: string;
    }
    export interface IApiResGetAdminBillingBillingAccountStatisticsBillYear {
        /**
         * 计费账号ID
         */
        billingAccountId?: number;
        /**
         * 计费账号名称
         */
        billingAccountName?: string;
        /**
         * CPU费用
         */
        cpuCost?: number;
        /**
         * 核时
         */
        cpuTime?: number;
        /**
         * GPU费用
         */
        gpuCost?: number;
        /**
         * 卡时
         */
        gpuTime?: number;
        /**
         * 包年包月CPU费用
         */
        queueCpuCost?: number;
        /**
         * 包年包月核时
         */
        queueCpuTime?: number;
        /**
         * 包年包月GPU费用
         */
        queueGpuCost?: number;
        /**
         * 包年包月卡时
         */
        queueGpuTime?: number;
        /**
         * 备注
         */
        remark?: string;
        /**
         * 存储费用
         */
        storageCost?: number;
        /**
         * 总费用
         */
        totalCost?: number;
        /**
         * 总CPU费用
         */
        totalCpuCost?: number;
        /**
         * 总CPU费用
         */
        totalGpuCost?: number;
    }
    export interface IApiResGetAdminBillingBillingAccountStatisticsBillYearChart {
        totalStorageCost: number;
        totalCpuTime: number;
        /**
         * CPU费用
         */
        cpuCost?: number;
        /**
         * 核时
         */
        cpuTime?: number;
        /**
         * 账单时间戳
         */
        date?: number;
        /**
         * GPU费用
         */
        gpuCost?: number;
        /**
         * 卡时
         */
        gpuTime?: number;
        /**
         * 账单时间
         */
        month?: string;
        /**
         * 包年包月CPU费用
         */
        queueCpuCost?: number;
        /**
         * 包年包月核时
         */
        queueCpuTime?: number;
        /**
         * 包年包月GPU费用
         */
        queueGpuCost?: number;
        /**
         * 包年包月卡时
         */
        queueGpuTime?: number;
        /**
         * 存储费用
         */
        storageCost?: number;
        /**
         * 总CPU费用
         */
        totalCpuCost?: number;
        /**
         * 总CPU费用
         */
        totalGpuCost?: number;
    }
    export interface IApiResGetAdminBillingBillingAccountStatisticsJobListBill {
        /**
         * 占用节点列表(包含每节点占用核数)
         */
        allNodes?: string;
        /**
         * 计费结束时间
         */
        billEndTime?: Date;
        /**
         * 计费组id
         */
        billingAccountId?: number;
        /**
         * 计费时长单位毫秒
         */
        billRuntime?: number;
        /**
         * 计费开始时间
         */
        billStartTime?: Date;
        /**
         * 是否是云端作业， 1：是, 0: 否
         */
        cloud?: boolean;
        /**
         * 集群ID
         */
        cluster?: string;
        /**
         * 核时费用单位分
         */
        cpuCost?: number;
        /**
         * cpu警告阈值
         */
        cpuThreshold?: number;
        /**
         * 核秒
         */
        cpuTime?: number;
        /**
         * 部门
         */
        department?: string;
        /**
         * 作业是否正常完成
         */
        done?: boolean;
        /**
         * 是否弹性
         */
        elasticity?: boolean;
        /**
         * 作业是否完成
         */
        end?: boolean;
        /**
         * 作业结束时间
         */
        endTime?: Date;
        /**
         * 定时开始时间
         */
        executionTime?: Date;
        /**
         * exit_code
         */
        exitCode?: number;
        /**
         * 作业失败原因
         */
        exitDesc?: string;
        /**
         * 标签值扩展字段1
         */
        expand1?: string;
        /**
         * 标签值扩展字段2
         */
        expand2?: string;
        /**
         * 标签值扩展字段3
         */
        expand3?: string;
        /**
         * 标签值扩展字段4
         */
        expand4?: string;
        /**
         * 标签值扩展字段5
         */
        expand5?: string;
        /**
         * 计费模式
         */
        expenseType?: ExpenseType;
        /**
         * 作业第一次开始时间
         */
        firstStartTime?: Date;
        /**
         * 用户组
         */
        gid?: number;
        /**
         * 卡时费用单位分
         */
        gpuCost?: number;
        /**
         * 卡秒
         */
        gpuTime?: number;
        /**
         * 作业索引ID
         */
        jobId?: string;
        /**
         * 作业名
         */
        jobName?: string;
        /**
         * cpu核数
         */
        ncpus?: number;
        /**
         * gpu卡数
         */
        ngpus?: number;
        /**
         * 节点数量
         */
        nodeNum?: number;
        /**
         * 占用节点列表
         */
        nodes?: string;
        /**
         * 调度系统作业状态
         */
        originState?: string;
        /**
         * 输出文件
         */
        outputPath?: string;
        /**
         * 排队原因
         */
        pendReason?: string;
        /**
         * 排队原因中文描述
         */
        pendReasonCn?: string;
        /**
         * 排队原因详细描述
         */
        pendReasonDesc?: string;
        /**
         * 单位ms
         */
        pendTime?: number;
        /**
         * Platform
         */
        platform?: string;
        /**
         * 作业优先级
         */
        priority?: number;
        /**
         * 项目ID
         */
        projectId?: number;
        /**
         * 项目level1
         */
        projectLevel1?: string;
        /**
         * 项目level2
         */
        projectLevel2?: string;
        /**
         * 项目level3
         */
        projectLevel3?: string;
        /**
         * 项目名
         */
        projectName?: string;
        /**
         * 队列名
         */
        queue?: string;
        /**
         * Platform 别名
         */
        queueName?: string;
        /**
         * 队列类型
         */
        queueType?: QueueType;
        /**
         * 真实作业ID
         */
        rawJobId?: string;
        /**
         * 备注
         */
        remark?: string;
        /**
         * 运行时间,单位ms
         */
        runTime?: number;
        /**
         * 集群账号
         */
        sccUser?: string;
        /**
         * 应用名称
         */
        software?: string;
        /**
         * 拆分时间
         */
        splitDate?: Date;
        /**
         * 作业开始时间
         */
        startTime?: Date;
        /**
         * 状态
         */
        state?: string;
        /**
         * 提交时间
         */
        submitTime?: Date;
        /**
         * 用户
         */
        uid?: string;
        /**
         * 应用名称
         */
        version?: string;
        /**
         * 作业目录
         */
        workDir?: string;
        /**
         * 区域ID
         */
        zone?: string;
        /**
         * 队列查询索引
         */
        zoneClusterQueue?: string;
    }
    export interface IApiResGetAdminBillingBillingAccountStatisticsOrderBillOverview {
        /**
         * 消耗金额-应付金额
         */
        amount?: number;
        /**
         * 账单金额
         */
        billAmount?: number;
        /**
         * 计费组ID
         */
        billingAccountId?: number;
        /**
         * 计费组名称
         */
        billingAccountName?: string;
        /**
         * 账单List总额
         */
        billListAmount?: number;
        /**
         * 账单消耗数量-记账数量
         */
        billQuantity?: number;
        /**
         * 账单时间
         */
        billTime?: Date;
        /**
         * 分区编号
         */
        cluster?: string;
        /**
         * 核时,单位ms
         */
        cpuTime?: number;
        createdDate?: Date;
        /**
         * 折扣
         */
        discount?: number;
        /**
         * 折扣单价(分)
         */
        discountPrice?: number;
        /**
         * 结束时间
         */
        endTime?: Date;
        /**
         * 计费模式
         */
        expenseType?: ExpenseType;
        /**
         * 卡时,单位ms
         */
        gpuTime?: number;
        /**
         * 主键ID
         */
        id?: number;
        /**
         * List总额-标准费用
         */
        listAmount?: number;
        /**
         * 标准单价(分)
         */
        listPrice?: number;
        /**
         * 订单id
         */
        orderId?: number;
        /**
         * 订单编号
         */
        orderNo?: string;
        /**
         * 订单类型
         */
        orderType?: string;
        /**
         * 产品折扣-实际折扣
         */
        productDiscount?: number;
        /**
         * 消耗数量-记账数量
         */
        quantity?: number;
        /**
         * queue
         */
        queue?: string;
        queueType?: QueueType;
        remark?: string;
        /**
         * 资源定价id
         */
        resourceId?: number;
        /**
         * 资源类型
         */
        resourcePricingType?: ResourcePricingType;
        /**
         * 产品编号
         */
        sku?: string;
        /**
         * 开始时间
         */
        startTime?: Date;
        /**
         * 计费单位
         */
        unit?: Unit;
        updatedDate?: Date;
        /**
         * 分区编号
         */
        zone?: string;
    }

    /**
     * 计费模式
     */
    export enum ExpenseTypes {
        ForeverLicense = 'FOREVER_LICENSE',
        Month = 'MONTH',
        Need = 'NEED',
        Year = 'YEAR',
    }
    /**
     * order
     */
    export enum Order {
        Asc = 'ASC',
        Desc = 'DESC',
    }

    /**
     * 资源类型
     */
    export enum ResourcePricingTypes {
        Computing = 'COMPUTING',
        Edge = 'EDGE',
        Graphical = 'GRAPHICAL',
        License = 'LICENSE',
        Network = 'NETWORK',
        Platform = 'PLATFORM',
        Software = 'SOFTWARE',
        SoftwareRental = 'SOFTWARE_RENTAL',
        Storage = 'STORAGE',
        Switch = 'SWITCH',
        TechnicalService = 'TECHNICAL_SERVICE',
    }
    export interface IApiResGetAdminBillingBillingAccountStatisticsBillMonthChart {
        /**
         * 计费账号id
         */
        billAccountId?: number;
        /**
         * 计费账号名称
         */
        billAccountName?: string;
        /**
         * CPU费用
         */
        cpuCost?: number;
        /**
         * GPU费用
         */
        gpuCost?: number;
        /**
         * 存储费用
         */
        storageCost?: number;
        totalCpuCost: number;
        /**
         * 存储总费用
         */
        totalStorageCost?: number;
        queueGpuCost: number;
        queueCpuCost: number;
    }

    export interface IApiReqGetAdminBillingBillingAccountStatisticsBillMonth
        extends IApiReqPageParams {
        /**
         * 结束时间
         */
        intervalStartTimeEnd: string;
        /**
         * 开始时间
         */
        intervalStartTimeStart: string;
        /**
         * 计费账号
         */
        billAccountIds?: number;
    }
    export interface IApiResGetAdminBillingBillingAccountStatisticsBillMonth {
        /**
         * 计费账号ID
         */
        billingAccountId?: number;
        /**
         * 计费账号名称
         */
        billingAccountName?: string;
        /**
         * 计费账号备注
         */
        billingAccountRemark?: string;
        /**
         * CPU费用
         */
        cpuCost?: number;
        /**
         * 核时
         */
        cpuTime?: number;
        /**
         * GPU费用
         */
        gpuCost?: number;
        /**
         * 卡时
         */
        gpuTime?: number;
        /**
         * 月份
         */
        month?: Date;
        /**
         * 其他费用
         */
        otherCost?: number;
        /**
         * 队列费用
         */
        queueCost?: number;
        /**
         * 存储费用
         */
        storageCost?: number;
        /**
         * 总费用
         */
        totalCost?: number;
    }

    export interface IApiResGetAdminTransferQueueAllSpeed {
        /**
         * 自动任务下行同步速度
         */
        autoDownloadTotalSpeed?: number;
        /**
         * 自动任务上行同步速度
         */
        autoUploadTotalSpeed?: number;
        /**
         * 同步任务下行同步速度
         */
        downloadTotalSpeed?: number;
        /**
         * 手动任务下行同步速度
         */
        manualDownloadTotalSpeed?: number;
        /**
         * 手动任务上行同步速度
         */
        manualUploadTotalSpeed?: number;
        /**
         * 同步任务下行同步速度
         */
        uploadTotalSpeed?: number;
    }

    export interface IApiReqPostAdminBillingBillingAccount {
        gids?: number[];
        /**
         * 名称
         */
        name?: string;
        /**
         * 备注
         */
        remark?: string;
        uids?: string[];
    }

    export interface IApiReqPutAdminBillingBillingAccount {
        /**
         * 计费账号id
         */
        id?: number;
        /**
         * 计费账号名称
         */
        name?: string;
        /**
         * 备注
         */
        remark?: string;
    }

    export interface IApiReqPostAdminBillingBillingAccountUser {
        /**
         * 计费账号id
         */
        billingAccountId?: number;
        /**
         * 用户uid
         */
        uids?: string[];
    }
    export interface IApiReqPostAdminBillingBillingAccountGroup {
        /**
         * 计费账号id
         */
        billingAccountId?: number;
        /**
         * 用户组gid
         */
        gids?: string[];
    }

    export interface IApiReqGetAdminBillingBillingAccountUserAssignable extends IApiReqPageParams {
        uidSearch?: string;
        billingAccountId?: number;
    }
    export interface IApiResGetAdminBillingBillingAccountUserAssignable {
        allowDownload?: boolean;
        description?: string;
        disable?: boolean;
        expandFirst?: string;
        expandSecond?: string;
        gid?: number;
        homeDirectory?: string;
        id?: number;
        resign?: boolean;
        role?: VoRole;
        sid?: string;
        uid?: string;
        username?: string;
        userScope?: UserScope;
    }

    export interface IApiReqGetAdminBillingBillingAccountUserList extends IApiReqPageParams {
        /**
         * 自定义付费组id
         */
        billingAccountId?: number;
    }

    export interface IApiResGetAdminBillingBillingAccountUserList {
        /**
         * 计费账号组id
         */
        billingAccountId?: number;
        /**
         * id
         */
        id?: number;
        /**
         * uid
         */
        uid?: string;
        /**
         * 用户名
         */
    }

    export interface IApiPostAdminBillingBillingRecharge {
        /**
         * 计费账号id
         */
        billingAccountId?: number;
        /**
         * 充值金额，单位分，小数三位
         */
        rechargeAmount?: number;
        /**
         * 充值备注
         */
        rechargeMsg?: string;
    }

    export interface IApiResGetAdminBillingBillingAccountGroup {
        billingAccountId: null;
        groupName: string;
        gid: number;
    }

    export interface IApiReqGetAdminBillingBillingAccountSpiltBillList {
        /**
         * 部门名称多选
         */
        department?: string;
        /**
         * 结束时间
         */
        end: string;
        /**
         * 统计维度，department,groupName,project,user
         */
        groupBy?: string;
        /**
         * 排序顺序，asc/desc
         */
        order?: string;
        /**
         * 排序字段，user
         */
        orderBy?: string;
        /**
         * 开始时间
         */
        start: string;
        /**
         * 用户名称，多选
         */
        user?: string;
    }

    export interface IApiResGetAdminBillingBillingAccountSpiltBillListData {
        demandAmount: number;
        holidayVoucherCost: number;
        stepVoucherCost: number;
        project: string;
        appealCpuTime: number;
        exclusiveAmount: number;
        productVoucherCost: number;
        cashVoucherCost: number;
        groupName: string;
        cpuTime: number;
        appealAmount: number;
        deducationVoucherCost: number;
        department: string;
        user: string;
        voucherCost: number;
        testVoucherCost: number;
        appealVoucherCost: number;
    }
    export interface IApiResGetAdminBillingBillingAccountSpiltBillListTitle {
        colSpan?: string;
        title: string;
        key: string;
    }
    export interface IApiResGetAdminBillingBillingAccountSpiltBillList {
        data: {
            data: IApiResGetAdminBillingBillingAccountSpiltBillListData[];
            title: IApiResGetAdminBillingBillingAccountSpiltBillListTitle[];
        };
    }

    export interface IApiReqGetAdminProjectTimeQuota {
        /**
         * 项目ID
         */
        projectId?: number;
        /**
         * 月项目核时配额
         */
        projectMonthCpuTimeQuota?: number;
        /**
         * 月项目卡时配额
         */
        projectMonthGpuTimeQuota?: number;
    }

    export interface IApiReqGetAdminQueueInfoSccUserQueueList {
        id: number;
        scUser?: string;
    }

    export interface IApiResGetAdminQueueInfoSccUserQueueList {
        maxUserQueuedResNcpus?: number;
        maxUserQueuedResNgpus?: number;
        maxUserRunResNcpus?: number;
        maxUserRunResNgpus?: number;
        preasonCoresCount?: { [key: string]: number };
        preasonNgpusCount?: { [key: string]: number };
        queuedResNcpus?: number;
        queuedResNgpus?: number;
        runResNcpus?: number;
        runResNgpus?: number;
        scUser?: string;
    }

    export interface IApiReqGetAdminQueueInfoSccUserQueueMaxCoreUpdate {
        /**
         * 队列信息ID
         */
        id: number;
        /**
         * 超算账号
         */
        scUser: string;
        /**
         * cpu队列核心上限
         */
        cpuMaxCoreLimit?: number;
        /**
         * gpu队列核心上限
         */
        gpuMaxCoreLimit?: number;
    }
    export interface IApiReqDeleteAdminQueueInfoSccUserQueueMaxCoreUpdate {
        id: number;
        scUser: string;
    }

    export interface IApiReqGetContainerConfigsList extends IApiReqPageParams {
        /**
         * cluster
         */
        cluster?: string;
        /**
         * order
         */
        order?: Order;
        /**
         * orderBy
         */
        orderBy?: string;
        /**
         * page
         */
        page?: number;
        /**
         * size
         */
        size?: number;
        /**
         * zone
         */
        zone?: string;
    }
    export interface IApiResGetContainerConfigsList {
        /**
         * 默认块存储大小
         */
        blockDefStorageSize?: number;
        /**
         * 块存储类型
         */
        blockStorageClassName?: string;
        /**
         * 集群编码
         */
        cluster?: string;
        createdDate?: Date;
        /**
         * 默认存储大小
         */
        defStorageSize?: number;
        id?: number;
        /**
         * 集群IP
         */
        loadBalancerIp?: string;
        /**
         * 集群IP代理
         */
        loadBalancerIpProxy?: string;
        /**
         * 集群最大端口
         */
        loadBalancerMaxPort?: number;
        /**
         * 集群最小端口
         */
        loadBalancerMinPort?: number;
        open?: boolean;
        /**
         * 存储类型
         */
        storageClassName?: string;
        updatedDate?: Date;
        /**
         * 区域编码
         */
        zone?: string;
    }

    export interface IApiReqGetAdminBillingStatisticsContainerProjectTime {
        /**
         * 用户组ID列表
         */
        gids?: number;
        /**
         * 作业结束运行时间
         */
        intervalStartTimeEnd: string;
        /**
         * 作业开始运行时间
         */
        intervalStartTimeStart: string;
        /**
         * 一级项目列表
         */
        level1s?: string;
        /**
         * 一级项目列表
         */
        level2s?: string;
        /**
         * 一级项目列表
         */
        level3s?: string;
        /**
         * 项目ID列表
         */
        projectIds?: number;
        /**
         * 计算资源类型
         */
        queueType?: QueueType;
        /**
         * 组是否递归
         */
        recursive?: boolean;
        /**
         * 用户ID列表
         */
        uids?: string;
    }
    export interface IApiResGetAdminBillingStatisticsContainerProjectTime {
        projectId?: number;
        projectName?: string;
        time?: number;
    }
    export interface IApiReqGetAdminBillingStatisticsContainerUserTime {
        /**
         * 用户组ID列表
         */
        gids?: number;
        /**
         * 作业结束运行时间
         */
        intervalStartTimeEnd: string;
        /**
         * 作业开始运行时间
         */
        intervalStartTimeStart: string;
        /**
         * 一级项目列表
         */
        level1s?: string;
        /**
         * 一级项目列表
         */
        level2s?: string;
        /**
         * 一级项目列表
         */
        level3s?: string;
        /**
         * 项目ID列表
         */
        projectIds?: number;
        /**
         * 计算资源类型
         */
        queueType?: QueueType;
        /**
         * 组是否递归
         */
        recursive?: boolean;
        /**
         * 用户ID列表
         */
        uids?: string;
    }

    export interface IApiResGetAdminBillingStatisticsContainerUserTime {
        time?: number;
        userId?: string;
        userName?: string;
    }

    export interface IApiReqGetAdminBillingStatisticsContainerFilterResourceType {
        /**
         * 作业结束运行时间
         */
        intervalStartTimeEnd: string;
        /**
         * 作业开始运行时间
         */
        intervalStartTimeStart: string;
    }
}
