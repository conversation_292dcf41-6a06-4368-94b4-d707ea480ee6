import { request } from '@umijs/max';

/**
 * 获取作业CPU使用历史统计数据
 * @param params 查询参数，包含时间范围、区域集群、用户组织、软件等过滤条件
 * @returns 返回CPU使用统计数据，包含CPU时长、限制、使用率等信息
 */
export async function getJobCpuHistory(params: API.IApiReqGetJobCpuHistory) {
    return await request<API.IApiResGetJobCpuHistory>(`admin/billing/statistics/job/cpu/history`, {
        params,
        timeout: globalConfig.API_TIMEOUT * 1000,
    });
}

/**
 * 获取作业GPU使用历史统计数据
 * @param params 查询参数，包含时间范围、区域集群、用户组织、软件等过滤条件
 * @returns 返回GPU使用统计数据，包含GPU时长、限制、使用率等信息
 */
export async function getJobGpuHistory(params: API.IApiReqGetJobCpuHistory) {
    return await request<API.IApiResGetJobGpuHistory>(`admin/billing/statistics/job/gpu/history`, {
        params,
        timeout: globalConfig.API_TIMEOUT * 1000,
    });
}

// admin/job/resource/log/zone/list
export async function getJobResourceLogZoneList() {
    return await request<API.IApiResGetJobResourceLogZoneList[]>(
        `admin/job/resource/log/zone/list`,
    );
}
// admin/job/resource/log/platforms/list
export async function getJobResourceLogPlatformsList() {
    return await request<string[]>(`admin/job/resource/log/platforms/list`);
}

// admin/job/resource/log/queues/list
export async function getJobResourceLogQueuesList() {
    return await request<string[]>(`admin/job/resource/log/queues/list`);
}

// admin/job/resource/log/software/list
export async function getJobResourceLogSoftwareList() {
    return await request<string[]>(`admin/job/resource/log/software/list`);
}

// admin/job/resource/log/jobuser/list
export async function getJobResourceLogJobuserList() {
    return await request<string[]>(`admin/job/resource/log/jobuser/list`);
}

// admin/resource/log/cpu/type/list
export async function getJobResourceLogCpuTypeList() {
    return await request<string[]>(`admin/job/resource/log/cpu/type/list`);
}

// admin/job/resource/log/uid/list
export async function getJobResourceLogUidList() {
    return await request<string[]>(`admin/job/resource/log/uid/list`);
}
