import { request } from '@umijs/max';

export const getAdminAppQueueElasticityStrategyList = async (
    params: API.IApiReqPageParams & API.IApiReqGetAdminAppQueueElasticityStrategyList,
) => {
    return await request<
        API.IApiResPagedDataModel<API.IApiResGetAdminAppQueueElasticityStrategyList>
    >(`admin/app/queue/elasticity/strategy/list`, {
        params,
    });
};

// POST
export const postAdminAppQueueElasticityStrategy = async (
    data: API.IApiReqPostAdminAppQueueElasticityStrategy,
) => {
    return await request<null>(`admin/app/queue/elasticity/strategy`, {
        method: 'POST',
        data,
    });
};

// PUT
export const putAdminAppQueueElasticityStrategy = async (
    data: API.IApiReqPutAdminAppQueueElasticityStrategy,
) => {
    return await request<null>(`admin/app/queue/elasticity/strategy`, {
        method: 'PUT',
        data,
    });
};

// DELETE
export const deleteAdminAppQueueElasticityStrategy = async (
    params: API.IApiReqDeleteAdminAppQueueElasticityStrategy,
) => {
    return await request<null>(`admin/app/queue/elasticity/strategy`, {
        method: 'DELETE',
        params,
    });
};
