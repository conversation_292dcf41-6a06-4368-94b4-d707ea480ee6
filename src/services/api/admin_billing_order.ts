import { request } from '@umijs/max';

/**
 * 查询订单列表
 * @param params
 * @returns
 */
export async function getAdminBillingOrderList(params?: API.IApiReqGetAdminBillingOrderList) {
    return await request<API.IApiResPagedDataModel<API.IApiResGetAdminBillingOrderList>>(
        `admin/billing/order/list`,
        {
            method: 'GET',
            params,
        },
    );
}

/**
 * 导出订单列表
 * @param params
 * @returns
 */
export async function exportGetAdminBillingOrderList(
    params: API.IApiReqExportGetAdminBillingOrderLis,
) {
    return await request<Blob>(`admin/billing/order/list/export`, {
        method: 'GET',
        params,
        responseType: 'blob',
        getResponse: true,
        timeout: globalConfig.API_TIMEOUT * 20,
    });
}

/**
 * 创建订单
 * @param params
 * @returns
 */
export async function postAdminBillingOrderList(data: API.IApiPostAdminBillingOrderList) {
    return await request(`admin/billing/order`, {
        method: 'POST',
        data,
    });
}

/**
 * 更新订单
 * @param params
 * @returns
 */
export async function putAdminBillingOrderList(data: API.IApiPutAdminBillingOrderList) {
    return await request(`admin/billing/order`, {
        method: 'PUT',
        data,
    });
}

/**
 * 删除订单
 * @param params
 * @returns
 */
export async function deleteAdminBillingOrderList(id: number) {
    return await request(`admin/billing/order/${id}`, {
        method: 'DELETE',
    });
}

/**
 * 执行订单
 * @param id
 * @returns
 */
export async function getAdminBillingOrderExecute(id: number) {
    return await request(`admin/billing/order/execute/${id}`, {
        method: 'GET',
    });
}

/**
 * 终止订单
 * @param id
 * @returns
 */
export async function getAdminBillingOrderDone(id: number) {
    return await request(`admin/billing/order/done/${id}`, {
        method: 'GET',
    });
}
