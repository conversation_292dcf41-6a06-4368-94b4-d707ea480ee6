import { request } from '@umijs/max';

/**
 * 查询计费账号列表
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccount(params?: API.IApiReqGetAdminBillingOrderList) {
    return await request<API.IApiResPagedDataModel<API.IApiResGetAdminBillingBillingAccount>>(
        `admin/billing/billing/account/list`,
        {
            method: 'GET',
            params,
        },
    );
}

/**
 * 创建计费组
 * @param params
 * @returns
 */
export async function postAdminBillingBillingAccount(
    data?: API.IApiReqPostAdminBillingBillingAccount,
) {
    return await request<null>(`admin/billing/billing/account`, {
        method: 'POST',
        data,
    });
}

/**
 * 更新计费组
 * @param params
 * @returns
 */
export async function putAdminBillingBillingAccount(
    data?: API.IApiReqPutAdminBillingBillingAccount,
) {
    return await request<null>(`admin/billing/billing/account`, {
        method: 'PUT',
        data,
    });
}

/**
 * 删除计费组
 * @param params
 * @returns
 */
export async function deleteAdminBillingBillingAccount(id?: number) {
    return await request<null>(`admin/billing/billing/account/${id}`, {
        method: 'DELETE',
    });
}

/**
 * 添加计费账号用户
 * @param params
 * @returns
 */
export async function postAdminBillingBillingAccountUser(
    data?: API.IApiReqPostAdminBillingBillingAccountUser,
) {
    return await request<null>(`admin/billing/billing/account/user`, {
        method: 'POST',
        data,
    });
}

/**
 * 添加计费账号用户组
 * @param params
 * @returns
 */
export async function postAdminBillingBillingAccountGroup(
    data?: API.IApiReqPostAdminBillingBillingAccountGroup,
) {
    return await request<null>(`admin/billing/billing/account/group`, {
        method: 'POST',
        data,
    });
}

/**
 * 查询可分配用户
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccountUserAssignable(
    params?: API.IApiReqGetAdminBillingBillingAccountUserAssignable,
) {
    return await request<
        API.IApiResPagedDataModel<API.IApiResGetAdminBillingBillingAccountUserAssignable>
    >(`admin/billing/billing/account/user/assignable`, {
        method: 'GET',
        params,
    });
}

/**
 * 计费账号已分配用户组
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccountGroupAssigned(billingAccountId: number) {
    return await request<number[]>(
        `admin/billing/billing/account/group/${billingAccountId}/assigned`,
        {
            method: 'GET',
        },
    );
}

/**
 * 所有已分配用户组
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccountAllGroupAssigned() {
    return await request<number[]>(`admin/billing/billing/account/group/assigned`, {
        method: 'GET',
    });
}

/**
 * 计费账号已分配用户组
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccountGroup(billingAccountId: number) {
    return await request<API.IApiResGetAdminBillingBillingAccountGroup[]>(
        `admin/billing/billing/account/group/${billingAccountId}`,
        {
            method: 'GET',
        },
    );
}

/**
 * 计费账号已分配用户
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccountUserList(
    params: API.IApiReqGetAdminBillingBillingAccountUserList,
) {
    return await request<
        API.IApiResPagedDataModel<API.IApiResGetAdminBillingBillingAccountUserList>
    >(`admin/billing/billing/account/user/list`, {
        method: 'GET',
        params,
    });
}

/**
 * 计费账号已分配用户
 * @param params
 * @returns
 */
export async function deleteAdminBillingBillingAccountUser(uid: string) {
    return await request<null>(`admin/billing/billing/account/user/${uid}`, {
        method: 'DELETE',
    });
}

/**
 * 删除计费组用户组
 * @param params
 * @returns
 */
export async function deleteAdminBillingBillingAccountGroup(params: {
    billingAccountId?: number;
    gid?: number;
}) {
    return await request<null>(`admin/billing/billing/account/group`, {
        method: 'DELETE',
        params,
    });
}

/**
 * 计费账号充值
 * @param data
 * @returns
 */
export async function postAdminBillingBillingRecharge(
    data: API.IApiPostAdminBillingBillingRecharge,
) {
    return await request<null>(`admin/billing/billing/account/recharge`, {
        method: 'POST',
        data,
    });
}

// 按计费组分账

/**
 * 查询计费组列表
 * @param params
 * @returns
 */
export async function getAdminBillingBillingAccountSpiltBillList(
    params?: API.IApiReqGetAdminBillingBillingAccountSpiltBillList,
) {
    return await request<API.IApiResGetAdminBillingBillingAccountSpiltBillList>(
        `admin/billing/billing/account/cloud/spilt/bill/list`,
        {
            method: 'GET',
            timeout: globalConfig.API_TIMEOUT * 20,
            params,
        },
    );
}
