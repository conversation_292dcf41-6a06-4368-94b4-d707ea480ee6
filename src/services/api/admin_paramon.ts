import { request } from '@umijs/max';
import type { RequestOptions } from '@umijs/max';

export async function getAdminParamonNodeMonitor(
    params: API.IApiReqGetAdminParamonNodeMonitor,
    config?: RequestOptions,
) {
    return await request<API.IApiResAdminParamonMetricModel[]>('admin/paramon/node/monitor', {
        params,
        ...config,
    });
}

export async function getAdminParamonJobMonitor(
    { jobId, ...params }: API.IApiReqGetAdminParamonJobMonitor,
    config?: RequestOptions,
) {
    return await request<API.IApiResAdminParamonMetricModel[]>(
        `admin/paramon/job/monitor/${jobId}`,
        {
            params,
            ...config,
        },
    );
}
