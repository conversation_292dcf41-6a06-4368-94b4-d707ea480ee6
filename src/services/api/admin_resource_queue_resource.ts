import { request } from '@umijs/max';

export const getProjectResourcesAssigned = (params: API.IApiReqGetProjectResourcesAssigned) => {
    return request<API.IApiResGetProjectResourcesAssigned[]>('admin/project/resources/assigned', {
        params,
    });
};

// admin/project/assignable get
export const getProjectResourcesAssignable = (params: API.IApiReqGetProjectResourcesAssigned) => {
    return request<API.IApiResGetProjectResourcesAssignable[]>('admin/project/assignable', {
        params,
    });
};

// admin/project/resources post
export const postAssignProjectResources = (data: API.IApiReqAssignProjectResources) => {
    return request<null>('admin/project/resources', {
        method: 'POST',
        data,
    });
};
// admin/project/queue delete
export const deleteAssignProjectResources = (params: API.IApiDeleteAssignProjectResources) => {
    return request<null>('admin/project/queue', {
        method: 'DELETE',
        params,
    });
};
