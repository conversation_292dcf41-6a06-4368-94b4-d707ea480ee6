import { request } from '@umijs/max';

export async function getAdminBillingBillingAccountStatisticsBillMonthChart(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillMonthChart,
) {
    return await request<API.IApiResGetAdminBillingBillingAccountStatisticsBillMonthChart[]>(
        `admin/billing/billing/account/statistics/bill/month/chart`,
        {
            method: 'GET',
            params,
        },
    );
}

export async function getAdminBillingBillingAccountStatisticsBillMonth(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillMonth,
) {
    return await request<
        API.IApiResPagedDataModel<API.IApiResGetAdminBillingBillingAccountStatisticsBillMonth>
    >(`admin/billing/billing/account/statistics/bill/month`, {
        method: 'GET',
        params,
    });
}

export async function getAdminBillingBillingAccountStatisticsBillExport(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillMonth,
) {
    return await request<Blob>(`admin/billing/billing/account/statistics/bill/month/export`, {
        params,
        responseType: 'blob',
        getResponse: true,
        timeout: globalConfig.API_TIMEOUT * 20,
    });
}
