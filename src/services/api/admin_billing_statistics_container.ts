import { request } from '@umijs/max';

export async function getAdminBillingStatisticsContainerProjectTime(
    params: API.IApiReqGetAdminBillingStatisticsContainerProjectTime,
) {
    return await request<API.IApiResGetAdminBillingStatisticsContainerProjectTime[]>(
        `admin/billing/statistics/container/project/time`,
        {
            params,
        },
    );
}

export async function getAdminBillingStatisticsContainerUserTime(
    params: API.IApiReqGetAdminBillingStatisticsContainerUserTime,
) {
    return await request<API.IApiResGetAdminBillingStatisticsContainerUserTime[]>(
        `admin/billing/statistics/container/user/time`,
        {
            params,
        },
    );
}

export async function getAdminBillingStatisticsContainerFilterResourceType(
    params: API.IApiReqGetAdminBillingStatisticsContainerFilterResourceType,
) {
    return await request<string[]>(`admin/billing/statistics/container/filter/resourceType`, {
        params,
    });
}
