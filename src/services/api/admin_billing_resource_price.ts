import { request } from '@umijs/max';

/**
 * 新增资源定价
 */
export async function postAdminBillingResourcePrice(data: API.IApiReqDataResourcePrice) {
    return await request<API.IApiResAdminBillingResourcePriceModel>(
        `admin/billing/resource/price`,
        {
            method: 'post',
            data,
        },
    );
}

/**
 * 查询资源定价
 */
export async function getAdminBillingResourcePriceList(
    params: API.IApiReqDataGetResourcePriceList,
) {
    return await request<API.IApiResPagedDataModel<API.IApiResAdminBillingResourcePriceModel>>(
        `admin/billing/resource/price/list`,
        {
            params,
        },
    );
}

/**
 * 修改资源定价
 */
export async function putAdminBillingResourcePrice(data: API.IApiReqDataResourcePrice) {
    return await request<API.IApiResAdminBillingResourcePriceModel>(
        `admin/billing/resource/price`,
        {
            method: 'PUT',
            data,
        },
    );
}

/**
 * 删除资源定价
 */
export async function deleteAdminBillingResourcePrice(id: number) {
    return await request<any>(`admin/billing/resource/price/${id}`, {
        method: 'DELETE',
    });
}

/**
 * 导出资源定价
 */
export async function getAdminBillingResourcePriceExport(
    params: API.IApiReqDataGetResourcePriceList,
) {
    return await request<any>(`admin/billing/resource/price/export`, {
        params,
        responseType: 'blob',
        getResponse: true,
        timeout: globalConfig.API_TIMEOUT * 20,
    });
}

/**
 * 查询计算队列
 */
export async function getAdminBillingQueues(queueType: string) {
    return await request<API.IApiResAdminBillingQueuesModel[]>(`admin/queues`, {
        params: {
            queueType,
        },
    });
}
