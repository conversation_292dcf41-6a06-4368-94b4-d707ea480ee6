import { request } from '@umijs/max';

export async function getJobStatusPage(params: API.IApiReqGetJobStatusPage) {
    return await request<API.IApiResPagedDataModel<API.IApiResGetJobStatusPage>>(
        `admin/job/state/run/page`,
        {
            params,
        },
    );
}

export async function getCpuSum(params: API.IApiReqGetJobStatusPage) {
    return await request<{
        jobCountSummaryMap: Record<
            'RUN' | 'PENDING' | 'WAITING' | 'EXECUTION' | 'STAGEOUT',
            API.IApiResGetCpuSum
        >;
    }>(`admin/job/state/cpu/sum`, {
        params,
    });
}

export async function getJobStatusRunExport(params: API.IApiReqGetJobStatusPage) {
    return await request<Blob>(`admin/job/state/run/export`, {
        params,
        method: 'get',
        responseType: 'blob',
        getResponse: true,
        timeout: globalConfig.API_TIMEOUT * 20,
    });
}
