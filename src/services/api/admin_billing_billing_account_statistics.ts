import { request } from '@umijs/max';

export async function getAdminBillingBillingAccountStatisticsOrderBillOverview(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsOrderBillOverview,
) {
    return await request<
        API.IApiResPagedDataModel<API.IApiResGetAdminBillingBillingAccountStatisticsOrderBillOverview>
    >(`admin/billing/billing/account/statistics/order/bill/overview`, {
        method: 'GET',
        params,
    });
}

export async function getAdminBillingBillingAccountStatisticsOrderBillOverviewExport(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsOrderBillOverview,
) {
    return await request<Blob>(
        `admin/billing/billing/account/statistics/order/bill/overview/export`,
        {
            method: 'GET',
            params,
            responseType: 'blob',
            getResponse: true,
            timeout: globalConfig.API_TIMEOUT * 20,
        },
    );
}

export async function getAdminBillingBillingAccountStatisticsBillYearChart(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillYearChart,
) {
    return await request<API.IApiResGetAdminBillingBillingAccountStatisticsBillYearChart[]>(
        `admin/billing/billing/account/statistics/bill/year/chart`,
        {
            method: 'GET',
            params,
        },
    );
}

export async function getAdminBillingBillingAccountStatisticsBillYear(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillYearChart, // 此接口后端不提供分页@hjb
) {
    return await request<API.IApiResGetAdminBillingBillingAccountStatisticsBillYear[]>(
        `admin/billing/billing/account/statistics/bill/year`,
        {
            method: 'GET',
            params,
        },
    );
}

export async function getAdminBillingBillingAccountStatisticsJobListBill(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillYearChart,
) {
    return await request<
        API.IApiResPagedDataModel<API.IApiResGetAdminBillingBillingAccountStatisticsJobListBill>
    >(`admin/billing/billing/account/statistics/job/list/bill`, {
        method: 'GET',
        params,
    });
}

export async function getAdminBillingBillingAccountStatisticsJobListBillExport(
    params?: API.IApiReqGetAdminBillingBillingAccountStatisticsBillYearChart,
) {
    return await request<Blob>(`admin/billing/billing/account/statistics/job/list/bill/export`, {
        method: 'GET',
        params,
        responseType: 'blob',
        getResponse: true,
        timeout: globalConfig.API_TIMEOUT * 20,
    });
}
