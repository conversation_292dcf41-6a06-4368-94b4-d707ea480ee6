import { request } from '@umijs/max';

export async function getJobStateEndPage(params: API.IApiReqGetJobStatusPage) {
    return await request<API.IApiResPagedDataModel<API.IApiResGetJobStatusPage>>(
        `admin/job/state/end/page`,
        {
            params,
        },
    );
}

export async function getJobStateEndExport(params: API.IApiReqGetJobStatusPage) {
    return await request<Blob>(`admin/job/state/end/export`, {
        params,
        method: 'get',
        responseType: 'blob',
        getResponse: true,
        timeout: globalConfig.API_TIMEOUT * 100,
    });
}

export async function getJobStateEndImport(taskId: string) {
    return await request<{
        intervalStartTimeEnd: string;
        intervalStartTimeStart: string;
        progress: number;
        status: string;
        taskId: string;
    }>(`admin/billing/async/scc/jobs/import/file/progress`, {
        params: {
            taskId,
        },
    });
}
export async function cancelJobStateEndImport(taskId: string) {
    return await request(`admin/billing/async/scc/jobs/import/file/cancel`, {
        params: {
            taskId,
        },
    });
}

//billing/appeal
export async function postBillingAppeal(data: API.IApiReqAppeal) {
    return await request(`billing/appeal`, {
        method: 'post',
        data,
    });
}

// billing/appeal/batch
export async function postBillingAppealBatch(data: { appeal_jobs: API.IApiReqAppeal[] }) {
    return await request(`billing/appeal/batch`, {
        method: 'post',
        data,
    });
}
