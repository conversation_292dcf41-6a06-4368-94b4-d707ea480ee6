import { request } from '@umijs/max';

export async function getAdminTransferQueueList(params: API.IApiReqGetAdminTransferQueueList) {
    return await request<API.IApiResPagedDataModel<API.IApiResGetAdminTransferQueueList>>(
        'admin/transfer/queue/list',
        {
            params,
        },
    );
}

export async function getAdminTransferQueueCount(params: API.IApiReqGetAdminTransferQueueCount) {
    return await request<API.IApiResGetAdminTransferQueueCount[]>('admin/transfer/queue/count', {
        params,
    });
}

export async function getAdminTransferQueueAllSpeed(params: API.IApiReqGetAdminTransferQueueCount) {
    return await request<API.IApiResGetAdminTransferQueueAllSpeed>(
        'admin/transfer/queue/all/speed',
        {
            params,
        },
    );
}
