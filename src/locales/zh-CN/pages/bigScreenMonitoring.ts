export default {
    'bigScreen.title': '小米仿真研发系统',
    'bigScreen.projectStats': '项目信息统计(Top20)',
    'bigScreen.departmentStats': '部门信息统计(Top20)',
    'bigScreen.userStats': '用户信息统计(Top20)',
    'bigScreen.storageUsage': '存储利用率(%)',
    'bigScreen.jobStatus': '实时作业状态',
    'bigScreen.cpuTime': '核时统计',
    'bigScreen.gpuTime': '卡时统计',
    'bigScreen.basicInfo': '基本信息展示',
    'bigScreen.totalUsers': '用户总数',
    'bigScreen.activeUsers': '活跃数',
    'bigScreen.totalNodes': '节点总数',
    'bigScreen.online': '开机',
    'bigScreen.offline': '关机',
    'bigScreen.cpuCores': 'CPU(核)',
    'bigScreen.used': '已用',
    'bigScreen.unused': '未用',
    'bigScreen.gpuCards': 'GPU(个)',
    'bigScreen.usageRate': '利用率(%)',
    'bigScreen.jobSubmissions': '作业提交数',
    'bigScreen.coreHours': '核时',
    'bigScreen.jobCount': '作业数',
    'bigScreen.cardHours': '卡时',
    'bigScreen.runningJobs': '运行作业',
    'bigScreen.preparingJobs': '准备作业',
    'bigScreen.pendingJobs': '排队作业',
    'bigScreen.waitingJobs': '等待作业',
    'bigScreen.storageUsed': '已用',
    'bigScreen.storageRemaining': '剩余',
    'bigScreen.cpuLabel': 'CPU',
    'bigScreen.gpuLabel': 'GPU',
    'bigScreen.jobSubmissionsLabel': '作业提交数',
};
