export default {
    'pages.billing.inquiryTime': '查询时间',
    'pages.billing.makeEnquiries': '查询',
    'pages.billing.readingData': '正在读取数据...',
    'pages.billing.nuclearTimeStatistics': '核时统计',
    'pages.billing.coreStatistics': '核心统计',
    'pages.billing.accountStatistics': '账号统计',
    'pages.billing.jobUsage': '作业统计',
    'pages.billing.applicationStatistics': '应用统计',
    'pages.billing.activeUserUsage': '活跃用户统计',
    'pages.billing.departmentalStatistics': '部门统计',
    'pages.billing.departmentalStatisticsBusinessUnit': '部门统计（事业部）',
    'pages.billing.jobList': '作业清单',
    'pages.billing.jobDuration': '作业时长分析',
    'pages.billing.zoneClusterQueues': '队列名称',
    'pages.billing.softwares': '应用软件',
    'pages.billing.uid': '用户账号',
    'pages.billing.jobTimeUnit': '统计粒度',
    'pages.billing.userGroup': '用户组',
    'pages.billing.workingTime': '工作日时间',
    'pages.billing.workingStart': '工作日开始时间',
    'pages.billing.workingEnd': '工作日结束时间',
    'pages.billing.cpuTime': '核时',
    'pages.billing.runCpuTime': '运行核时',
    'pages.billing.queueLimit': '队列不足',
    'pages.billing.usedQuota': '已用配额',
    'pages.billing.quota': '配额',
    'pages.billing.personalLimit': '配额不足',
    'pages.billing.licenseLimit': '许可不足',
    'pages.billing.resourceLimit': '资源不足',
    'pages.billing.licenseOver': '许可超额',
    'pages.billing.jobOver': '作业超额',
    'pages.billing.otherLimit': '其它',
    'pages.billing.amountTo': '总计',
    'pages.billing.accountNumber': '账号',
    'pages.billing.jobCheckTime': '作业核时',
    'pages.billing.jobNum': '作业数量',
    'pages.billing.noDataAvailable': '暂无数据',
    'pages.billing.accountName': '账号名称',
    'pages.billing.groupName': '组名称',
    'pages.billing.whenFixingCore': '固定核时',
    'pages.billing.totalElasticityIntervalBillingCpuTime': '弹性核时',
    'pages.billing.avgIntervalRunTime': '作业时长(avg)',
    'pages.billing.maxIntervalRunTime': '作业时长(max)',
    'pages.billing.avgIntervalPendTime': '排队时长(avg)',
    'pages.billing.maxIntervalPendTime': '排队时长(max)',
    'pages.billing.billingCpuTimePercent': '核时占比',
    'pages.billing.usedCpuTime': '已用核时',
    'pages.billing.totalCpuTime': '核时总量',
    'pages.billing.utilizationRate': '利用率',
    'pages.billing.exportAccountStatistics': '导出账号统计',
    'pages.billing.exportError': '导出错误',
    'pages.billing.exportApplicationStatistics': '导出应用统计',
    'pages.billing.queueStatistics': '队列统计',
    'pages.billing.processStatistics': '进程统计',
    'pages.billing.versionStatistics': '版本统计',
    'pages.billing.total': '合计',
    'pages.billing.other': '其他',
    'pages.billing.softwareVersion': '软件版本',
    'pages.billing.departmentName': '部门名称',
    'pages.billing.departmentRoot': '部门根目录',
    'pages.billing.pleaseEnterJobId': '请输入作业ID',
    'pages.billing.billingTypeAll': '全部',
    'pages.billing.billingTypeYear': '包年',
    'pages.billing.billingTypeMonth': '包月',
    'pages.billing.billingTypeOnDemand': '按需',
    'pages.billing.billingTypeNoCharge': '不计费',
    'pages.billing.billingTypeNeverExpired': '永久许可',
    'pages.billing.cloudJobDataStatisticsExport': '云端作业数据量统计导出',
    'pages.billing.pleaseSelectTheExportPeriod': '请选择导出时间段',
    'pages.billing.downloadingData': '正在下载数据...',
    'pages.billing.timePeriod': '时间周期',
    'pages.billing.week': '周',
    'pages.billing.month': '月',
    'pages.billing.custom': '自定义',
    'pages.billing.exportRange': '导出范围',
    'pages.billing.queueAlias': '队列别名',
    'pages.billing.billingStartTime': '计费开始时间',
    'pages.billing.billingEndTime': '计费截止时间',
    'pages.billing.startTime': '开始时间',
    'pages.billing.endTime': '结束时间',
    'pages.billing.chargingTime': '计费时长',
    'pages.billing.gpuTime': '卡时',
    'pages.billing.queuingTime': '排队时长',
    'pages.billing.queueReason': '排队原因',
    'pages.billing.supercomputerAccount': '超算账号',
    'pages.billing.statisticalType': '统计类型',
    'pages.billing.chargeRecord': '充值记录',
    'pages.billing.operatorUserName': '操作用户名',
    'pages.billing.rechargeObject': '充值对象',
    'pages.billing.rechargeObjectType': '充值对象类型',
    'pages.billing.rechargeAmount': '充值金额',
    'pages.billing.rechargeTime': '充值时间',
    'pages.billing.rechargeMsg': '充值留言',
    'pages.billing.resourceZoon': '资源分区',
    'pages.billing.hardwarePlatform': '硬件平台',
    'pages.billing.indivual': '个',
    'pages.billing.billingFreeCpuTime': '空闲核时',
    'pages.billing.intervalFreeTime': '空闲时长',
    'pages.billing.FreeTimePercentage': '空闲时长占比',
    'pages.billing.allCpuTime': '总核时',
    'pages.billing.allJob': '总作业数',
    'pages.billing.fullCpuTime': '满载核时',
    'pages.billing.clusterCpusLimit': '核心上限',
    'pages.billing.clusterGpusLimit': '卡数上限',
    'pages.billing.3DVNC': '3DVNC利用率',
    'pages.billing.3DVNCcpuTime': '3DVNC核时总量',
    'pages.billing.3DVNfreeCpuTime': '3DVNC空闲核时占比',
    'pages.billing.3DVNfreeCpuTimePercentage': '3DVNC空闲核时总占比',
    'pages.billing.platformStatistics': '平台统计',
    'pages.billing.queueType': '队列类型',
    'pages.billing.projectUsage': '项目统计',

    'pages.billing.resourceCapsByPartition': '资源上限（按分区）',
    'pages.billing.resourceQueuing': '资源排队',
    'pages.billing.quotaQueuing': '配额排队',
    'pages.billing.licenseQueue': '许可排队',
    'pages.billing.exceptionQueuing': '异常排队',
    'pages.billing.otherQueues': '其他排队',
    'pages.billing.exclusiveQueues': '专属排队',
    'pages.billing.numberOfJobsRunning': '运行作业数',
    'pages.billing.numberOfQueuedJobs': '排队作业数',
    'pages.billing.cloudUserNum': '云上使用人数',
    'pages.billing.localUserNum': '本地使用人数',
    'pages.billing.assignError': '以下用户分配失败: ',

    'pages.billing.cpuCoreHours': 'CPU核时',
    'pages.billing.gpuCoreHours': 'GPU卡时',

    'pages.billing.coreUtilizationRate': '核心利用率',
    'pages.billing.billingSubject': '计费主体',
    'pages.billing.billingSubjectRequired': '计费主体为必填项',
    'pages.billing.runningJobs': '运行作业',
    'pages.billing.queuedJobs': '排队作业',
    'pages.billing.completedJobs': '已完成作业',
    'pages.billing.failedJobs': '失败作业',
    'pages.billing.cancelledJobs': '已取消作业',
    'pages.billing.timeoutJobs': '超时作业',
    'pages.billing.memoryJobs': '内存作业',
    'pages.billing.otherJobs': '其他作业',
};
