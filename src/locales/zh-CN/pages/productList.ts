export default {
    'pages.productList.create': '新建产品',
    'pages.productList.productID': '产品编号',
    'pages.productList.resourceAbbreviation': '资源简称',
    'pages.productList.resourceType': '资源类型',
    'pages.productList.resourceType.computing': '计算资源',
    'pages.productList.resourceType.network': '网络资源',
    'pages.productList.resourceType.storage': '存储资源',
    'pages.productList.resourceType.graphical': '图形资源',
    'pages.productList.resourceType.platform': '平台资源',
    'pages.productList.resourceType.software': '软件资源',
    'pages.productList.resourceType.edge': '边缘资源',
    'pages.productList.resourceType.technicalService': '技术服务',
    'pages.productList.resourceType.license': '软件许可',
    'pages.productList.resourceType.switch': '配件资源',
    'pages.productList.resourceType.softwareRental': '软件租赁',
    'pages.productList.billingMode': '计费模式',
    'pages.productList.productStatus': '产品状态',
    'pages.productList.productSaleStatusOn': '在售',
    'pages.productList.productSaleStatusWait': '待售',
    'pages.productList.productSaleStatusOff': '停售',
    'pages.productList.supportType': '使用类型',
    'pages.productList.supportTypeNormal': '通用支持',
    'pages.productList.supportTypeExclusive': '专属支持',
    'pages.productList.standardUnitPrice': '标准单价(元)',
    'pages.productList.billingUnit': '计费单位',
    'pages.productList.billingUnitKilometer': '公里',
    'pages.productList.billingUnitSessions': '并发会话',
    'pages.productList.billingUnitModule': '模块',
    'pages.productList.billingUnitBandWidth': '带宽',
    'pages.productList.billingUnitUnit': '台',
    'pages.productList.billingUnitPersonDay': '人天',
    'pages.productList.billingUnitPersonYear': '人年',
    'pages.productList.billingUnitUser': '并发用户',
    'pages.productList.billingUnitTB': 'TB',
    'pages.productList.billingUnitPB': 'PB',
    'pages.productList.billingQueue': '计费队列',
    'pages.productList.billingresourceType': '计费资源类型',
    'pages.productList.successfullyEdit': '修改成功',
};
