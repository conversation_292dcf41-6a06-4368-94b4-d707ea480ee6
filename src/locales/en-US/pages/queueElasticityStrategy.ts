export default {
    'pages.queueElasticityStrategy.originalCluster': 'Original Cluster',
    'pages.queueElasticityStrategy.targetCluster': 'Target Cluster',
    'pages.queueElasticityStrategy.originalQueue': 'Original Queue',
    'pages.queueElasticityStrategy.targetQueue': 'Target Queue',
    'pages.queueElasticityStrategy.originalRegion': 'Original Region',
    'pages.queueElasticityStrategy.targetRegion': 'Target Region',
    'pages.queueElasticityStrategy.elasticityStrategy': 'Elasticity Strategy',
    'pages.queueElasticityStrategy.cpuRatio': 'CPU Ratio',
    'pages.queueElasticityStrategy.fullNodeByCore': 'Full node by core',
    'pages.queueElasticityStrategy.fullNodeByNode': 'Full node by node',
};
