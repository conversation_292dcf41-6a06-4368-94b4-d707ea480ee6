export default {
    'pages.billing.inquiryTime': 'inquiry time',
    'pages.billing.makeEnquiries': 'Query',
    'pages.billing.readingData': 'reading data...',
    'pages.billing.nuclearTimeStatistics': 'Core Hour Statistics',
    'pages.billing.coreStatistics': 'Core statistics',
    'pages.billing.accountStatistics': 'Account Statistics',
    'pages.billing.jobUsage': 'Job Statistics',
    'pages.billing.applicationStatistics': 'Application Statistics',
    'pages.billing.activeUserUsage': 'Active User Statistics',
    'pages.billing.departmentalStatistics': 'Departmental Statistics',
    'pages.billing.departmentalStatisticsBusinessUnit': 'Business Unit Statistics',
    'pages.billing.jobList': 'Job List',
    'pages.billing.jobDuration': 'Job Duration Analysis',
    'pages.billing.zoneClusterQueues': 'Queue Name',
    'pages.billing.softwares': 'Application Software',
    'pages.billing.uid': 'Account',
    'pages.billing.jobTimeUnit': 'Statistics Granularity',
    'pages.billing.userGroup': 'User Group',
    'pages.billing.workingTime': 'working day time',
    'pages.billing.workingStart': 'working day start time',
    'pages.billing.workingEnd': 'working day start time',
    'pages.billing.cpuTime': 'Core Hour',
    'pages.billing.runCpuTime': 'Running core hours',
    'pages.billing.queueLimit': 'Insufficient queue',
    'pages.billing.usedQuota': 'Used Quota',
    'pages.billing.quota': 'Quota',
    'pages.billing.personalLimit': 'Insufficient quota',
    'pages.billing.licenseLimit': 'Insufficient license',
    'pages.billing.resourceLimit': 'Insufficient resource',
    'pages.billing.licenseOver': 'Excessive license',
    'pages.billing.jobOver': 'Excessive job',
    'pages.billing.otherLimit': 'Others',
    'pages.billing.amountTo': 'Total',
    'pages.billing.accountNumber': 'Account',
    'pages.billing.jobCheckTime': 'Job core hour',
    'pages.billing.jobNum': 'Job Qty.',
    'pages.billing.noDataAvailable': 'no data available',
    'pages.billing.accountName': 'Account Name',
    'pages.billing.groupName': 'Group Name',
    'pages.billing.whenFixingCore': 'Fixed Core Hour',
    'pages.billing.totalElasticityIntervalBillingCpuTime': 'Flexible Core Hour',
    'pages.billing.avgIntervalRunTime': 'Average Job Duration',
    'pages.billing.maxIntervalRunTime': 'Max. Job Duration',
    'pages.billing.avgIntervalPendTime': 'Average Pending Time',
    'pages.billing.maxIntervalPendTime': 'Max. Pending Time',
    'pages.billing.billingCpuTimePercent': 'Core Hour Ratio',
    'pages.billing.usedCpuTime': 'Used core hours',
    'pages.billing.totalCpuTime': 'Total core hours',
    'pages.billing.utilizationRate': 'Utilization rate',
    'pages.billing.exportAccountStatistics': 'Export',
    'pages.billing.exportError': 'export error',
    'pages.billing.exportApplicationStatistics': 'Export',
    'pages.billing.queueStatistics': 'Queue Statistics',
    'pages.billing.processStatistics': 'Process Statistics',
    'pages.billing.versionStatistics': 'Version Statistics',
    'pages.billing.total': 'Total',
    'pages.billing.other': 'Others',
    'pages.billing.softwareVersion': 'Software Version',
    'pages.billing.departmentName': 'department name',
    'pages.billing.departmentRoot': 'Department Root',
    'pages.billing.pleaseEnterJobId': 'please enter jobId',
    'pages.billing.billingTypeAll': 'all',
    'pages.billing.billingTypeYear': 'Yearly',
    'pages.billing.billingTypeMonth': 'Monthly',
    'pages.billing.billingTypeOnDemand': 'On demand',
    'pages.billing.billingTypeNoCharge': 'Free of charge',
    'pages.billing.billingTypeNeverExpired': 'Never Expired License',
    'pages.billing.cloudJobDataStatisticsExport': 'cloud job data statistics export',
    'pages.billing.pleaseSelectTheExportPeriod': 'Select Time Segment',
    'pages.billing.downloadingData': 'downloading data...',
    'pages.billing.timePeriod': 'time period',
    'pages.billing.week': 'week',
    'pages.billing.month': 'month',
    'pages.billing.custom': 'custom',
    'pages.billing.exportRange': 'export range',
    'pages.billing.queueAlias': 'Queue Alias',
    'pages.billing.billingStartTime': 'Billing Start Time',
    'pages.billing.billingEndTime': 'Billing End Time',
    'pages.billing.startTime': 'Start Time',
    'pages.billing.chargingTime': 'Billing Duration',
    'pages.billing.gpuTime': 'Core Hour/GPU',
    'pages.billing.queuingTime': 'Pending Time',
    'pages.billing.queueReason': 'Pending Reason',
    'pages.billing.supercomputerAccount': 'Supercomputer Account',
    'pages.billing.endTime': 'End Time',
    'pages.billing.statisticalType': 'Statistical Type',
    'pages.billing.chargeRecord': 'charge record',
    'pages.billing.operatorUserName': 'Operator Username',
    'pages.billing.rechargeObject': 'Top-Up Object',
    'pages.billing.rechargeObjectType': 'Top-Up Type',
    'pages.billing.rechargeAmount': 'Top-Up Amount',
    'pages.billing.rechargeTime': 'Top-Up Time',
    'pages.billing.rechargeMsg': 'Top-Up Note',
    'pages.billing.resourceZoon': 'Resource Region',
    'pages.billing.hardwarePlatform': 'Hardware platform',
    'pages.billing.indivual': 'Unit',
    'pages.billing.billingFreeCpuTime': 'Free Core Hour',
    'pages.billing.intervalFreeTime': 'Free Duration',
    'pages.billing.FreeTimePercentage': 'Free Duration Ratio',
    'pages.billing.allCpuTime': 'all cpu time',
    'pages.billing.allJob': 'all job',
    'pages.billing.fullCpuTime': 'full cpu time',
    'pages.billing.clusterCpusLimit': 'Core upper limit',
    'pages.billing.clusterGpusLimit': 'GPU upper limit',
    'pages.billing.3DVNC': '3DVNC Utilization',
    'pages.billing.3DVNCcpuTime': '3DVNC Total number of core hours',
    'pages.billing.3DVNfreeCpuTime': '3DVNC Idle core time ratio',
    'pages.billing.3DVNfreeCpuTimePercentage': '3DVNC Total number of idle cores',
    'pages.billing.platformStatistics': 'Platform Statistics',
    'pages.billing.queueType': 'Queue Type',
    'pages.billing.projectUsage': 'Project statistics',

    'pages.billing.resourceCapsByPartition': 'Resource caps (by partition)',
    'pages.billing.resourceQueuing': 'Resource queuing',
    'pages.billing.quotaQueuing': 'Quota queuing',
    'pages.billing.licenseQueue': 'License queue',
    'pages.billing.exceptionQueuing': 'Exception queuing',
    'pages.billing.otherQueues': 'Other queues',
    'pages.billing.exclusiveQueues': 'Exclusive queues',
    'pages.billing.numberOfJobsRunning': 'Number of jobs running',
    'pages.billing.numberOfQueuedJobs': 'Number of queued jobs',
    'pages.billing.cloudUserNum': 'Cloud user',
    'pages.billing.localUserNum': 'Local User',
    'pages.billing.assignError': 'The following user assignments failed: ',

    'pages.billing.cpuCoreHours': 'CPU Core Hours',
    'pages.billing.gpuCoreHours': 'GPU Core Hours',

    'pages.billing.coreUtilizationRate': 'Core Utilization Rate',
    'pages.billing.billingSubject': 'Billing Subject',
    'pages.billing.billingSubjectRequired': 'Billing Subject is required',
    'pages.billing.runningJobs': 'Running Jobs',
    'pages.billing.queuedJobs': 'Queued Jobs',
    'pages.billing.completedJobs': 'Completed Jobs',
    'pages.billing.failedJobs': 'Failed Jobs',
    'pages.billing.cancelledJobs': 'Cancelled Jobs',
    'pages.billing.timeoutJobs': 'Timeout Jobs',
    'pages.billing.memoryJobs': 'Memory Jobs',
    'pages.billing.otherJobs': 'Other Jobs',
};
