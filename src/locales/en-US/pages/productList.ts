export default {
    'pages.productList.create': 'Create Product',
    'pages.productList.productID': 'Product ID',
    'pages.productList.resourceAbbreviation': 'Resource Abbreviation',
    'pages.productList.resourceType': 'Resource Type',
    'pages.productList.resourceType.computing': 'Computing Resource',
    'pages.productList.resourceType.network': 'Network Resource',
    'pages.productList.resourceType.storage': 'Storage Resource',
    'pages.productList.resourceType.graphical': 'Graphical Resource',
    'pages.productList.resourceType.platform': 'Platform Resource',
    'pages.productList.resourceType.software': 'Software Resource',
    'pages.productList.resourceType.edge': 'Edge Resource',
    'pages.productList.resourceType.technicalService': 'Technical Service',
    'pages.productList.resourceType.license': 'Software License',
    'pages.productList.resourceType.switch': 'Accessory Resource',
    'pages.productList.resourceType.softwareRental': 'Software Rental',
    'pages.productList.billingMode': 'Billing Mode',
    'pages.productList.productStatus': 'Product Status',
    'pages.productList.productSaleStatusOn': 'On Sale',
    'pages.productList.productSaleStatusWait': 'Waiting for sale',
    'pages.productList.productSaleStatusOff': 'Off Sale',
    'pages.productList.supportType': 'Type of Use',
    'pages.productList.supportTypeNormal': 'General Support',
    'pages.productList.supportTypeExclusive': 'Exclusive Support',
    'pages.productList.standardUnitPrice': 'Standard Unit Price(Yuan)',
    'pages.productList.billingUnit': 'Billing Unit',
    'pages.productList.billingUnitKilometer': 'Kilometer',
    'pages.productList.billingUnitSessions': 'Concurrent Sessions',
    'pages.productList.billingUnitModule': 'Module',
    'pages.productList.billingUnitBandWidth': 'Bandwidth',
    'pages.productList.billingUnitUnit': 'Unit',
    'pages.productList.billingUnitPersonDay': 'Person Day',
    'pages.productList.billingUnitPersonYear': 'Person Year',
    'pages.productList.billingUnitUser': 'Concurrent User',
    'pages.productList.billingUnitTB': 'TB',
    'pages.productList.billingUnitPB': 'PB',
    'pages.productList.billingQueue': 'Billing Queue',
    'pages.productList.billingresourceType': 'Billing Resource Type',
    'pages.productList.successfullyEdit': 'Successfully Edit',
};
