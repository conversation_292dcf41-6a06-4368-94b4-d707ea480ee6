import { getIntl, getLocale } from '@umijs/max';

const intl = getIntl(getLocale());

export function fileTransferStatusEnum() {
    return {
        CANCEL: intl.formatMessage({ id: 'component.modal.cancel' }),
        DONE: intl.formatMessage({ id: 'pages.appGuide.accomplish' }),
        FAIL: intl.formatMessage({ id: 'pages.log.operationResultFail' }),
        INIT: intl.formatMessage({ id: 'enum.jobStatus.prep' }),
        RUN: intl.formatMessage({ id: 'pages.searchTable.nameStatus.running' }),
    };
}
