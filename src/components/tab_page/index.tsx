import { Tabs } from '@/components/control_tab';
import styles from './index.less';

export default function TabsPage({ tabs }: { tabs: any[] }) {
    if (!tabs || tabs.length === 0) {
        return <div>No tabs available</div>;
    }

    return (
        <div className={styles.monitors}>
            <div className="tab-panel">
                <Tabs type="card" destroyInactiveTabPane items={tabs} />
            </div>
        </div>
    );
}
