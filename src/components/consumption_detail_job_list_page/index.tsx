import { useEffect, useRef, useState } from 'react';
import { useIntl } from '@umijs/max';
import SearchTablePage from '@/components/search_table_page';
import { ActionType, ProFormInstance } from '@ant-design/pro-components';
import {
    getAdminBillingBillingAccountStatisticsJobListBill,
    getAdminBillingBillingAccountStatisticsJobListBillExport,
} from '@/services/api/admin_billing_billing_account_statistics';
import { getAdminBillingBillingAccount } from '@/services/api/admin_billing_account';
import { isArray } from 'lodash-es';
import dayjs from 'dayjs';
import TimeSelector from '@/components/time_selector2';
import { createBillingTypeEnum } from '@/utils/columns_enum';
import { createZoneNameValueEnum } from '@/valueEnum/zone_name';
import { timeFormat } from '@/utils/time_format';
import AppSelector from '../app_icon_manage/app_selector';
import { getAdminQueueInfoList } from '@/services/api/admin_queue_info';
import { Button, message } from 'antd';
import { downLoadBlob } from '../billing_center/helper';

export const ConSumptionDetailJobListPage = () => {
    const intl = useIntl();
    const formRef = useRef<ProFormInstance>();
    const tableActionRef = useRef<ActionType>();
    const [initParamsValue, setInitParamsValue] = useState({
        billingAccountId: undefined,
    });
    const [zoneNameValueEnum, setZoneNameValueEnum] = useState([]);
    const [billingAccountsState, setBillingAccounts] = useState([]);

    const initSearch = async () => {
        const { data: billingAccounts } = await getAdminBillingBillingAccount({
            size: 99999,
        });
        setBillingAccounts(billingAccounts);
        if (isArray(billingAccounts)) {
            formRef.current.setFieldsValue({
                billingAccountId: billingAccounts[0]?.id,
            });
            await setInitParamsValue({
                billingAccountId: billingAccounts[0]?.id,
            });
        }
        tableActionRef.current?.reloadAndRest?.();
    };

    useEffect(() => {
        initSearch();
        createZoneNameValueEnum().then((r) => {
            setZoneNameValueEnum(r);
        });
    }, []);

    return (
        <SearchTablePage
            searchProps={{
                formRef,
                submitter: {
                    onReset: async () => {
                        if (isArray(billingAccountsState)) {
                            formRef.current.setFieldsValue({
                                billingAccountId: billingAccountsState[0]?.id,
                            });
                        }
                    },
                    render: (_, doms) => {
                        return [
                            ...doms,
                            <Button
                                onClick={async () => {
                                    const values = await formRef.current?.getFieldsFormatValue();
                                    const res =
                                        await getAdminBillingBillingAccountStatisticsJobListBillExport(
                                            {
                                                ...values,
                                            },
                                        );
                                    try {
                                        downLoadBlob(res.headers['content-disposition'], res.data);
                                    } catch {
                                        message.error(
                                            intl.formatMessage({ id: 'pages.billing.exportError' }),
                                        );
                                    }
                                }}
                            >
                                {intl.formatMessage({ id: 'component.table.export' })}
                            </Button>,
                        ];
                    },
                },
                columns: [
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.billingSubject' }),
                        dataIndex: 'billingAccountId',
                        fieldProps: {
                            allowClear: false,
                            showSearch: true,
                        },
                        formItemProps: {
                            rules: [
                                {
                                    required: true,
                                    message: intl.formatMessage({
                                        id: 'pages.billing.billingSubjectRequired',
                                    }),
                                },
                            ],
                        },
                        request: async () => {
                            const { data: billingAccounts } = await getAdminBillingBillingAccount({
                                size: 99999,
                            });
                            return billingAccounts.map((b) => {
                                return {
                                    value: b.id,
                                    label: b.name,
                                };
                            });
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.monitorsJobs.statisticalRange' }),
                        dataIndex: 'rangeTime',
                        transform: (rangeTime) => {
                            return {
                                intervalStartTimeStart: rangeTime[0],
                                intervalStartTimeEnd: rangeTime[1],
                            };
                        },
                        renderFormItem: () => (
                            <TimeSelector
                                defaultStartTime={dayjs().startOf('year')}
                                defaultEndTime={dayjs().endOf('year')}
                                pickType="month"
                                displayType="onlyDate"
                                selectType="custom"
                            />
                        ),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.log.jobId' }),
                        dataIndex: 'rawJobId',
                    },
                    {
                        title: intl.formatMessage({ id: 'menu.organization.user' }),
                        dataIndex: 'uids',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.appCode' }),
                        dataIndex: 'appCodes',
                        formItemProps: {
                            style: {
                                width: 200,
                            },
                        },
                        renderFormItem: () => (
                            <AppSelector
                                fieldProps={{ mode: 'multiple', maxTagCount: 0 }}
                                type="search"
                                valueProp="appCode"
                            />
                        ),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.resourceQueueResource.queueName' }),
                        dataIndex: 'zoneClusterQueue',
                        width: 250,
                        fieldProps: {
                            mode: 'multiple',
                            maxTagCount: 0,
                        },
                        request: async () => {
                            const { data: queues } = await getAdminQueueInfoList({
                                size: 99999,
                            });
                            return queues.map((queue) => ({
                                label: `${queue.zone}/${queue.cluster}/${queue.queue}`,
                                value: `${queue.zone}:${queue.cluster}:${queue.queue}`,
                            }));
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.billingType' }),
                        dataIndex: 'expenseTypes',
                        fieldProps: {
                            mode: 'multiple',
                            showSearch: true,
                            maxTagCount: 0,
                        },
                        valueType: 'select',
                        request: () => createBillingTypeEnum(),
                    },
                ],
                onFinish: async () => {
                    return await tableActionRef.current?.reloadAndRest?.();
                },
            }}
            tableProps={{
                actionRef: tableActionRef,
                columns: [
                    {
                        title: intl.formatMessage({ id: 'pages.log.jobId' }),
                        dataIndex: 'rawJobId',
                        align: 'right',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.zone' }),
                        dataIndex: 'zone',
                        renderText: (zone: string) => {
                            const zoneName = zoneNameValueEnum?.find((item) => item.value === zone);
                            return zoneName ? zoneName.label : zone;
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.appCode' }),
                        dataIndex: 'software',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.resourceQueueResource.queueName' }),
                        dataIndex: 'queue',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.billingStartTime' }),
                        dataIndex: 'billStartTime',
                        valueType: 'dateTime',
                        align: 'center',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.billingEndTime' }),
                        dataIndex: 'billEndTime',
                        valueType: 'dateTime',
                        align: 'center',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.slots' }),
                        dataIndex: 'ncpus',
                        align: 'right',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.order.Ngpus' }),
                        dataIndex: 'ngpus',
                        align: 'right',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.chargingTime' }),
                        dataIndex: 'billRuntime',
                        align: 'right',
                        renderText: (billRuntime) => timeFormat(billRuntime, 'milliseconds'),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.appIcon.cpuTime' }),
                        dataIndex: 'cpuTime',
                        valueType: 'digit',
                        align: 'right',
                        renderText: (cpuTime) => (cpuTime / 3600 / 1000).toFixed(2),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.gpuTime' }),
                        dataIndex: 'gpuTime',
                        valueType: 'digit',
                        align: 'right',
                        fieldProps: {
                            parser: 2,
                        },
                        renderText: (gpuTime) => (gpuTime / 3600 / 1000).toFixed(2),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.billAmount' }),
                        dataIndex: 'cost',
                        valueType: 'money',
                        align: 'right',
                        renderText: (_, r) => {
                            if (r.queueType === 'CPU') {
                                return r.cpuCost / 100;
                            }
                            if (r.queueType === 'GPU') {
                                return r.gpuCost / 100;
                            }
                            return r.cpuCost;
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.billingType' }),
                        dataIndex: 'expenseType',
                        fieldProps: {
                            mode: 'multiple',
                            showSearch: true,
                            maxTagCount: 0,
                        },
                        valueType: 'select',
                        request: () => createBillingTypeEnum(),
                    },
                    {
                        title: intl.formatMessage({ id: 'menu.organization.user' }),
                        dataIndex: 'uid',
                    },
                ],
                request: async ({ pageSize: size, current: page }) => {
                    const values = formRef.current?.getFieldsFormatValue?.();
                    if (!values.billingAccountId) {
                        return {
                            success: true,
                            data: [],
                            total: 0,
                        };
                    }
                    const { data, total } =
                        await getAdminBillingBillingAccountStatisticsJobListBill({
                            ...initParamsValue,
                            ...values,
                            size,
                            page,
                        });
                    return {
                        success: true,
                        data,
                        total,
                    };
                },
            }}
        />
    );
};
