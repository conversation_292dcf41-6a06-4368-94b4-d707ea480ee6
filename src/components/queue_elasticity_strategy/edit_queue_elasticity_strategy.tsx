import { formItemLayout } from '@/utils/form_layout';
import { BetaSchemaForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { createQueueElasticityStrategyColumns } from './get_columns';

import {
    postAdminAppQueueElasticityStrategy,
    putAdminAppQueueElasticityStrategy,
} from '@/services/api/admin_app_queue_elasticity_strategy';
import type { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import type { MutableRefObject } from 'react';
import type { EditQueueElasticityStrategyRefType } from './types';

const EditQueueElasticityStrategy = ({
    actions,
    tableActions,
}: {
    actions: EditQueueElasticityStrategyRefType;
    tableActions: MutableRefObject<ActionType | undefined>;
}) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [info, setInfo] = useState<API.IApiResGetAdminAppQueueElasticityStrategyList>();
    const formRef = useRef<ProFormInstance>();
    Object.assign(actions, {
        changeModal: async (row: API.IApiResGetAdminAppQueueElasticityStrategyList) => {
            await setOpen(true);
            setInfo(row);
            formRef.current?.setFieldsValue?.({ ...row, cpuRatio: row?.cpuRatio * 100 });
        },
    });

    return (
        <BetaSchemaForm
            title={
                info?.id
                    ? intl.formatMessage({ id: 'component.operate.update' })
                    : intl.formatMessage({ id: 'component.operate.create' })
            }
            {...formItemLayout}
            formRef={formRef}
            visible={open}
            onFinish={async () => {
                const formValues = formRef.current?.getFieldsFormatValue?.();
                if (info?.id) {
                    await putAdminAppQueueElasticityStrategy({
                        ...info,
                        ...formValues,
                    });
                } else {
                    await postAdminAppQueueElasticityStrategy(formValues);
                }
                setOpen(false);
                tableActions?.current?.reloadAndRest?.();
            }}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                    setInfo(undefined);
                    formRef.current?.resetFields();
                },
            }}
            layoutType="ModalForm"
            layout="horizontal"
            columns={createQueueElasticityStrategyColumns(formRef.current)}
        />
    );
};

export default EditQueueElasticityStrategy;
