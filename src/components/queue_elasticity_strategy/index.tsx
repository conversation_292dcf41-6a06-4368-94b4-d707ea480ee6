import { useIntl } from '@umijs/max';
import { useRef } from 'react';
import { Button } from 'antd';
import SearchTablePage from '../search_table_page';
import { searchColumns, tableColumns } from './get_columns';
import EditQueueElasticityStrategy from './edit_queue_elasticity_strategy';
import { getAdminAppQueueElasticityStrategyList } from '@/services/api/admin_app_queue_elasticity_strategy';

import type { EditQueueElasticityStrategyRefType } from './types';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';

const QueueElasticityStrategy = () => {
    const intl = useIntl();
    const searchRef = useRef<ProFormInstance<API.IApiReqGetAdminAppQueueElasticityStrategyList>>();
    const tableActionsRef = useRef<ActionType>();
    const editQueueElasticityStrategyRef = useRef<EditQueueElasticityStrategyRefType>({});
    const modalActions = {
        editQueueElasticityStrategyRef,
        tableActionsRef,
    };

    return (
        <>
            <SearchTablePage
                searchProps={{
                    formRef: searchRef,
                    onFinish: async () => {
                        await tableActionsRef.current?.reloadAndRest?.();
                    },
                    columns: searchColumns(),
                }}
                tableProps={{
                    actionRef: tableActionsRef,
                    toolBarRender: () => [
                        <Button
                            key="create"
                            type="primary"
                            onClick={() => editQueueElasticityStrategyRef.current.changeModal?.()}
                        >
                            {intl.formatMessage({ id: 'component.operate.create' })}
                        </Button>,
                    ],
                    request: async ({ pageSize: size, current: page }) => {
                        const formValues = searchRef.current!.getFieldsFormatValue!();
                        const { data, total } = await getAdminAppQueueElasticityStrategyList({
                            size,
                            page,
                            ...formValues,
                        });
                        return {
                            success: true,
                            data,
                            total,
                        };
                    },
                    columns: tableColumns(modalActions),
                }}
            />
            <EditQueueElasticityStrategy
                actions={editQueueElasticityStrategyRef.current}
                tableActions={tableActionsRef}
            />
        </>
    );
};

export default QueueElasticityStrategy;
