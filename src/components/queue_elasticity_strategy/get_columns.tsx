import type {
    ActionType,
    ProColumns,
    ProFormColumnsType,
    ProFormInstance,
} from '@ant-design/pro-components';
import AppSelector from '../app_icon_manage/app_selector';
import {
    createAsyncAppVersion,
    createAsyncClusterEnum,
    createAsyncQueuesEnum,
    createAsyncZonesEnum,
    createQueueCpuConvertStrategy,
} from '@/utils/columns_enum';
import type { EditQueueElasticityStrategyRefType } from './types';
import type { MutableRefObject } from 'react';
import { Modal } from 'antd';
import { deleteAdminAppQueueElasticityStrategy } from '@/services/api/admin_app_queue_elasticity_strategy';

import { getIntl, getLocale } from '@umijs/max';

const intl = getIntl(getLocale());

export const searchColumns =
    (): ProFormColumnsType<API.IApiReqGetAdminAppQueueElasticityStrategyList>[] => {
        return [
            {
                title: intl.formatMessage({ id: 'pages.organization.user.appName' }),
                dataIndex: 'appCode',
                formItemProps: {
                    style: {
                        width: 330,
                    },
                },
                renderFormItem: () => (
                    <AppSelector valueProp="appCode" fieldProps={{ showSearch: true }} />
                ),
            },
        ];
    };

export const tableColumns = (modalActions: {
    editQueueElasticityStrategyRef: MutableRefObject<EditQueueElasticityStrategyRefType>;
    tableActionsRef: React.MutableRefObject<ActionType | undefined>;
}): ProColumns<API.IApiResGetAdminAppQueueElasticityStrategyList>[] => {
    return [
        {
            title: intl.formatMessage({ id: 'pages.resourceQueueResource.applicationName' }),
            dataIndex: 'appCode',
        },
        {
            title: intl.formatMessage({ id: 'pages.resourceQueueResource.appVersion' }),
            dataIndex: 'appVersion',
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.originalCluster' }),
            dataIndex: 'origCluster',
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.targetCluster' }),
            dataIndex: 'destCluster',
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.originalRegion' }),
            dataIndex: 'origZone',
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.targetRegion' }),
            dataIndex: 'destZone',
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.originalQueue' }),
            dataIndex: 'origQueue',
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.targetQueue' }),
            dataIndex: 'destQueue',
        },

        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.elasticityStrategy' }),
            dataIndex: 'queueCpuConvertStrategy',
            valueType: 'select',
            request: () => createQueueCpuConvertStrategy(),
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.cpuRatio' }),
            dataIndex: 'cpuRatio',
            valueType: 'percent',
            renderText: (cpuRatio) => cpuRatio * 100,
        },
        {
            title: intl.formatMessage({ id: 'component.operate' }),
            valueType: 'option',
            render: (_, row) => {
                return [
                    <a
                        key="edit"
                        onClick={async () => {
                            await modalActions.editQueueElasticityStrategyRef.current.changeModal?.(
                                row,
                            );
                        }}
                    >
                        {intl.formatMessage({ id: 'component.operate.update' })}
                    </a>,
                    <a
                        key="delete"
                        onClick={async () => {
                            Modal.confirm({
                                title: intl.formatMessage({
                                    id: 'component.areYouSureYouWantToDeleteIt',
                                }),
                                onOk: async () => {
                                    await deleteAdminAppQueueElasticityStrategy({ id: row.id });
                                    modalActions.tableActionsRef.current?.reloadAndRest?.();
                                },
                            });
                        }}
                    >
                        {intl.formatMessage({ id: 'component.operate.delete' })}
                    </a>,
                ];
            },
        },
    ];
};

export const createQueueElasticityStrategyColumns = (
    formRefCurrent?: ProFormInstance,
): ProFormColumnsType<API.IApiReqGetAdminAppQueueElasticityStrategyList>[] => {
    return [
        {
            title: intl.formatMessage({ id: 'pages.organization.user.appName' }),
            dataIndex: 'appCode',
            formItemProps: {
                rules: [{ required: true }],
            },
            fieldProps: {
                onChange: async () => {
                    await formRefCurrent?.setFieldValue('appVersion', undefined);
                },
            },
            renderFormItem: () => (
                <AppSelector valueProp="appCode" fieldProps={{ showSearch: true }} />
            ),
        },
        {
            valueType: 'dependency',
            name: ['appCode'],
            columns: ({ appCode }) => {
                return appCode
                    ? [
                          {
                              title: intl.formatMessage({
                                  id: 'pages.resourceQueueResource.appVersion',
                              }),
                              dependencies: ['appCode'],
                              dataIndex: 'appVersion',
                              fieldProps: {
                                  allowClear: true,
                              },
                              request: async () => {
                                  return await createAsyncAppVersion(appCode);
                              },
                          },
                      ]
                    : [];
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.originalRegion' }),
            dataIndex: 'origZone',
            formItemProps: {
                rules: [{ required: true }],
            },
            fieldProps: {
                onChange: async () => {
                    await formRefCurrent?.setFieldValue('origCluster', undefined);
                    await formRefCurrent?.setFieldValue('origQueue', undefined);
                },
            },
            request: createAsyncZonesEnum,
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.originalCluster' }),
            dataIndex: 'origCluster',
            formItemProps: {
                rules: [{ required: true }],
            },
            fieldProps: {
                onChange: async () => {
                    await formRefCurrent?.setFieldValue('origQueue', undefined);
                },
            },
            dependencies: ['origZone'],
            request: async ({ origZone }) => {
                return await createAsyncClusterEnum(origZone);
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.originalQueue' }),
            dataIndex: 'origQueue',
            formItemProps: {
                rules: [{ required: true }],
            },
            dependencies: ['origZone', 'origCluster'],
            request: async ({ origZone, origCluster }) => {
                return await createAsyncQueuesEnum({
                    zone: origZone,
                    cluster: origCluster,
                });
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.targetRegion' }),
            dataIndex: 'destZone',
            formItemProps: {
                rules: [{ required: true }],
            },
            fieldProps: {
                onChange: async () => {
                    await formRefCurrent?.setFieldValue('destCluster', undefined);
                    await formRefCurrent?.setFieldValue('destQueue', undefined);
                },
            },
            request: createAsyncZonesEnum,
        },

        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.targetCluster' }),
            dataIndex: 'destCluster',
            formItemProps: {
                rules: [{ required: true }],
            },
            fieldProps: {
                onChange: async () => {
                    await formRefCurrent?.setFieldValue('destQueue', undefined);
                },
            },
            dependencies: ['destZone'],
            request: async ({ destZone }) => {
                return await createAsyncClusterEnum(destZone);
            },
        },

        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.targetQueue' }),
            dataIndex: 'destQueue',
            formItemProps: {
                rules: [{ required: true }],
            },
            dependencies: ['destZone', 'destCluster'],
            request: async ({ destZone, destCluster }) => {
                return await createAsyncQueuesEnum({ zone: destZone, cluster: destCluster });
            },
        },

        {
            title: intl.formatMessage({ id: 'pages.queueElasticityStrategy.elasticityStrategy' }),
            dataIndex: 'queueCpuConvertStrategy',
            formItemProps: {
                rules: [{ required: true }],
            },
            valueType: 'select',
            request: async () => {
                return await createQueueCpuConvertStrategy();
            },
        },
        {
            valueType: 'dependency',
            name: ['queueCpuConvertStrategy'],
            columns: ({ queueCpuConvertStrategy }) => {
                if (queueCpuConvertStrategy === 'RATIO_BY_CPU') {
                    return [
                        {
                            title: intl.formatMessage({
                                id: 'pages.queueElasticityStrategy.cpuRatio',
                            }),
                            dataIndex: 'cpuRatio',
                            valueType: 'percent',
                            width: '100%',
                            transform: (cpuRatio) => {
                                return {
                                    cpuRatio: cpuRatio / 100,
                                };
                            },
                            fieldProps: {
                                min: 0.01,
                                addonAfter: '%',
                                precision: 2,
                            },
                        },
                    ];
                }
                return [];
            },
        },
    ];
};
