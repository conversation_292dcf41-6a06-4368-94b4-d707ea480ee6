.applicationStatistics :global {
	.search {
		min-height: 45px;
		margin-bottom: 20px;
		padding: 10px 10px 10px 10px;
		border: 1px solid #dfdfdf;
		border-radius: 4px;
		.ant-space-align-center {
			align-items: baseline;
		}
	}
	.chart-box {
		position: relative;
		display: flex;
		width: 100%;
		height: calc(100vh - 260px);
		padding: 10px 10px 0 10px;
		border: 1px solid #dfdfdf;
		border-radius: 4px;
		.ant-spin-nested-loading,
		.ant-spin-container {
			display: flex;
			flex: 1;
			.ant-empty {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
			}
		}
		.chart-container {
			display: flex;
			flex: 1;
			padding: 0 20px;
			.chart {
				flex: 1;
			}
		}
		.table-container {
			display: flex;
			flex: 1;
			flex-direction: column;
			max-width: 50%;
			padding: 0 5px;
			.ant-table {
				width: 100%;
			}
			.ant-spin-nested-loading,
			.ant-spin-container {
				width: 100%;
			}
			.software-table {
				flex: 1;
				overflow: auto;
			}
			.queue-table {
				flex: 1;
				overflow: auto;
			}
			.process-table{
				flex: 1;
				overflow: auto;
			}
			.total-row {
				color: #1890ff;
				font-weight: 600;
			}
		}
	}
}