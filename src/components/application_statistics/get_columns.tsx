import React from 'react';

import { getIntl, getLocale } from '@umijs/max';

import TimeSelector from '@/components/time_selector';
import { getAdminBillingBillingAccount } from '@/services/api/admin_billing_account';

import type { ProFormColumnsType, ProFormInstance } from '@ant-design/pro-components';

const intl = getIntl(getLocale());

export function getAppStatisticsSearch(
    formRef: React.MutableRefObject<ProFormInstance | undefined>,
): ProFormColumnsType[] {
    return [
        {
            title: intl.formatMessage({
                id: 'pages.billing.billingSubject',
            }),
            dataIndex: 'billingAccountId',
            fieldProps: {
                showSearch: true,
                allowClear: false,
            },
            formItemProps: {
                rules: [
                    {
                        required: true,
                        message: intl.formatMessage({
                            id: 'pages.billing.billingSubjectRequired',
                        }),
                    },
                ],
            },
            request: async () => {
                const { data: billingAccounts } = await getAdminBillingBillingAccount({
                    size: 99999,
                });
                return billingAccounts.map((b) => {
                    return {
                        value: b.id,
                        label: b.name,
                    };
                });
            },
        },
        {
            title: intl.formatMessage({
                id: 'pages.monitorsJobs.statisticalRange',
            }),
            dataIndex: 'selectTimeRange',
            transform: (selectTimeRange) => {
                return {
                    intervalStartTimeStart: selectTimeRange[0],
                    intervalStartTimeEnd: selectTimeRange[1],
                };
            },
            renderFormItem: () => <TimeSelector selectType="month" />,
        },
    ];
}
