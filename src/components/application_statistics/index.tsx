import 'echarts/lib/chart/bar';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/dataZoom';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/title';
import 'echarts/lib/component/toolbox';
import 'echarts/lib/component/tooltip';
import { useCallback, useEffect, useRef, useState } from 'react';

import { ReloadOutlined } from '@ant-design/icons';
import { BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Empty, Spin } from 'antd';
import { isArray } from 'lodash-es';

import {
    getBillingStatisticsCpuSoftware,
    getBillingStatisticsCpuQueue,
    getBillingStatisticsNcpu,
} from '@/services/api';
import { getAdminBillingBillingAccount } from '@/services/api/admin_billing_account';
import { getAppStatisticsSearch } from './get_columns';
import { calcCpuTime } from '@/utils/utils';

import styles from './index.less';
import Echarts from '../para_echarts';
import moneyFormatter, { splitNum, calcTotalDataTransFormer } from '../billing_center/helper';

import type { ProFormInstance } from '@ant-design/pro-components';

export default function ApplicationStatistics() {
    const intl = useIntl();
    const contentRef = useRef<HTMLDivElement>();
    const formRef = useRef<ProFormInstance>();
    const [chartData, setChartData] = useState<any>();
    const [softwareTableData, setSoftwareTableData] = useState<any[]>();
    const [queueTableData, setQueueTableData] = useState<any[]>();
    const [ncpuTableData, setNcpuTableData] = useState<any[]>();
    const [loading, setLoading] = useState<boolean>(false);
    const [max, setMax] = useState<number>(0);
    const [cpuTimeInterval, setCpuTimeInterval] = useState<number>(256);
    const [initParamsValue, setInitParamsValue] = useState({
        billingAccountId: undefined,
    });
    const softwareTableRef = useRef<HTMLDivElement>(null);
    const queueTableRef = useRef<HTMLDivElement>(null);
    const ncpuTableRef = useRef<HTMLDivElement>(null);

    const initBillingAccounts = async () => {
        const { data: billingAccounts } = await getAdminBillingBillingAccount({
            size: 99999,
        });
        if (isArray(billingAccounts) && formRef.current) {
            formRef.current.setFieldsValue({
                billingAccountId: billingAccounts[0]?.id,
            });
            await setInitParamsValue({
                billingAccountId: billingAccounts[0]?.id,
            });
        }
    };

    const fetchData = useCallback(async () => {
        setLoading(true);
        const values = formRef.current?.getFieldsFormatValue?.() || {};
        if (!values.billingAccountId) {
            setLoading(false);
            return {
                chartData: null,
                softwareTableData: [],
                queueTableData: [],
                ncpuTableData: [],
            };
        }

        try {
            const argument = {
                ...initParamsValue,
                ...values,
            };

            const [softwareRes, queueRes, ncpuRes] = await Promise.all([
                getBillingStatisticsCpuSoftware(argument),
                getBillingStatisticsCpuQueue(argument),
                getBillingStatisticsNcpu(argument),
            ]);

            let lmax = 0;
            const softwareData = softwareRes || [];
            const queueData = queueRes || [];
            const ncpuResData = ncpuRes || [];

            const x: any[] = [];
            const series: any = {
                totalIntervalBillingCpuTime: [],
                jobNum: [],
            };

            softwareData.slice(0, 6).forEach((item: any) => {
                x.push(item.software || 'Unknown');
                const cpuTime = item.totalBillingCpuTime || 0;
                series.totalIntervalBillingCpuTime.push(cpuTime);
                if (cpuTime > lmax) {
                    lmax = cpuTime;
                }
                series.jobNum.push(item.jobNum || 0);
            });

            const step = splitNum(lmax / (3600000 * 256));
            setMax(lmax);
            const lcpuTimeInterval = step * 256;
            setCpuTimeInterval(lcpuTimeInterval);

            setLoading(false);
            return {
                chartData: { x, series },
                softwareTableData: calcTotalDataTransFormer(softwareData),
                queueTableData: calcTotalDataTransFormer(queueData),
                ncpuTableData: calcTotalDataTransFormer(ncpuResData),
            };
        } catch (error) {
            console.error('获取数据失败:', error);
            setLoading(false);
            return {
                chartData: null,
                softwareTableData: [],
                queueTableData: [],
                ncpuTableData: [],
            };
        }
    }, [initParamsValue]);

    useEffect(() => {
        initBillingAccounts();
    }, []);

    useEffect(() => {
        if (initParamsValue.billingAccountId) {
            fetchData().then((res) => {
                setChartData(res.chartData);
                setSoftwareTableData(res.softwareTableData);
                setQueueTableData(res.queueTableData);
                setNcpuTableData(res.ncpuTableData);
            });
        }
    }, [fetchData, initParamsValue]);

    return (
        <div className={styles.applicationStatistics}>
            <div className="search">
                <BetaSchemaForm
                    formRef={formRef}
                    layout="inline"
                    layoutType="Form"
                    columns={getAppStatisticsSearch(formRef)}
                    submitter={{
                        render: () => [
                            <Button
                                key="search"
                                type="primary"
                                icon={<ReloadOutlined />}
                                onClick={async () => {
                                    const res = await fetchData();
                                    setChartData(res.chartData);
                                    setSoftwareTableData(res.softwareTableData);
                                    setQueueTableData(res.queueTableData);
                                }}
                                disabled={loading}
                            >
                                {intl.formatMessage({
                                    id: 'pages.billing.makeEnquiries',
                                })}
                            </Button>,
                        ],
                    }}
                />
            </div>
            <div className="chart-box">
                <div className="chart-container" ref={contentRef as any}>
                    <Spin spinning={loading}>
                        {chartData && chartData.x.length > 0 ? (
                            <Echarts
                                className="chart"
                                option={{
                                    legend: {
                                        top: 40,
                                    },
                                    tooltip: {
                                        trigger: 'axis',
                                        backgroundColor: '#fff',
                                        formatter: (data) => {
                                            if (!Array.isArray(data)) {
                                                return '';
                                            }
                                            let res = `${intl.formatMessage({
                                                id: 'pages.billing.softwares',
                                            })}: ${data[0].name}<br/>`;
                                            data.forEach((item) => {
                                                if (
                                                    item.seriesName ===
                                                    intl.formatMessage({
                                                        id: 'pages.billing.jobCheckTime',
                                                    })
                                                ) {
                                                    res += `${item.marker}${
                                                        item.seriesName
                                                    } : ${moneyFormatter(
                                                        calcCpuTime(item.data as number),
                                                        2,
                                                    )}<br/>`;
                                                } else {
                                                    res += `${item.marker}${item.seriesName} : ${item.data}个<br/>`;
                                                }
                                            });
                                            return res;
                                        },
                                        axisPointer: {
                                            snap: true,
                                            type: 'shadow',
                                        },
                                    },
                                    toolbox: {
                                        showTitle: false,
                                        feature: {
                                            saveAsImage: {
                                                show: true,
                                            },
                                        },
                                    },
                                    xAxis: {
                                        type: 'category',
                                        data: chartData && chartData.x,
                                    },
                                    yAxis: [
                                        {
                                            type: 'value',
                                            name: intl.formatMessage({
                                                id: 'pages.billing.jobCheckTime',
                                            }),
                                            axisLabel: {
                                                formatter: (value: number) => {
                                                    return `${moneyFormatter(
                                                        calcCpuTime(value),
                                                        0,
                                                    )}`;
                                                },
                                            },
                                            max: () => {
                                                const val = calcCpuTime(max);
                                                const interval = Math.ceil(val / cpuTimeInterval);
                                                if (max === 0) {
                                                    return 1024 * 60 * 60 * 1000;
                                                }
                                                return interval * cpuTimeInterval * 60 * 60 * 1000;
                                            },
                                            interval: cpuTimeInterval * 60 * 60 * 1000,
                                        },
                                        {
                                            type: 'value',
                                            name: intl.formatMessage({
                                                id: 'pages.billing.jobNum',
                                            }),
                                            alignTicks: true,
                                            axisLabel: {
                                                formatter: (value: number) => {
                                                    return `${value}`;
                                                },
                                            },
                                        },
                                    ],
                                    series: [
                                        {
                                            name: intl.formatMessage({
                                                id: 'pages.billing.jobCheckTime',
                                            }),
                                            type: 'bar',
                                            barMaxWidth: 40,
                                            data:
                                                chartData &&
                                                chartData.series.totalIntervalBillingCpuTime,
                                        },
                                        {
                                            name: intl.formatMessage({
                                                id: 'pages.billing.jobNum',
                                            }),
                                            type: 'bar',
                                            barMaxWidth: 40,
                                            yAxisIndex: 1,
                                            data: chartData && chartData.series.jobNum,
                                        },
                                    ],
                                }}
                            />
                        ) : (
                            <Empty
                                description={intl.formatMessage({
                                    id: 'pages.billing.noDataAvailable',
                                })}
                            />
                        )}
                    </Spin>
                </div>
                <div className="table-container">
                    <div className="software-table" ref={softwareTableRef}>
                        <ProTable
                            rowKey="software"
                            columns={[
                                {
                                    title: intl.formatMessage({ id: 'pages.job.appCode' }),
                                    dataIndex: 'software',
                                    ellipsis: true,
                                    width: 100,
                                },
                                {
                                    title: intl.formatMessage({ id: 'pages.billing.jobCheckTime' }),
                                    dataIndex: 'totalBillingCpuTime',
                                    align: 'right',
                                    width: 80,
                                    render: (totalBillingCpuTime: number) =>
                                        moneyFormatter(calcCpuTime(totalBillingCpuTime), 2),
                                },
                                {
                                    title: intl.formatMessage({ id: 'pages.billing.jobNum' }),
                                    dataIndex: 'jobNum',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                },
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.avgIntervalRunTime',
                                    }),
                                    dataIndex: 'avgIntervalRunTime',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                    fieldProps: {
                                        precision: 2,
                                    },
                                    renderText: (_, r) => r.avgIntervalRunTime / 1000 / 3600,
                                },
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.avgIntervalPendTime',
                                    }),
                                    dataIndex: 'avgIntervalPendTime',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                    fieldProps: {
                                        precision: 2,
                                    },
                                    renderText: (_, r) => r.avgIntervalPendTime / 1000 / 3600,
                                },
                            ]}
                            dataSource={softwareTableData}
                            search={false}
                            scroll={{
                                x: 500,
                                y: Number(softwareTableRef.current?.clientHeight) - 55,
                            }}
                            pagination={false}
                            toolBarRender={false}
                            options={false}
                            headerTitle={intl.formatMessage({ id: 'pages.billing.softwares' })}
                        />
                    </div>
                    <div className="queue-table" ref={queueTableRef}>
                        <ProTable
                            rowKey="zoneClusterQueue"
                            columns={[
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.zoneClusterQueues',
                                    }),
                                    ellipsis: true,
                                    dataIndex: 'zoneClusterQueue',
                                    align: 'left',
                                    width: 100,
                                },
                                {
                                    title: intl.formatMessage({ id: 'pages.billing.jobCheckTime' }),
                                    dataIndex: 'totalBillingCpuTime',
                                    align: 'right',
                                    width: 80,
                                    render: (totalBillingCpuTime: number) =>
                                        moneyFormatter(calcCpuTime(totalBillingCpuTime), 2),
                                },
                                {
                                    title: intl.formatMessage({ id: 'pages.billing.jobNum' }),
                                    dataIndex: 'jobNum',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                },
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.avgIntervalRunTime',
                                    }),
                                    dataIndex: 'avgIntervalRunTime',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                    fieldProps: {
                                        precision: 2,
                                    },
                                    renderText: (_, r) => r.avgIntervalRunTime / 1000 / 3600,
                                },
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.avgIntervalPendTime',
                                    }),
                                    dataIndex: 'avgIntervalPendTime',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                    fieldProps: {
                                        precision: 2,
                                    },
                                    renderText: (_, r) => r.avgIntervalPendTime / 1000 / 3600,
                                },
                            ]}
                            dataSource={queueTableData}
                            search={false}
                            scroll={{
                                x: 500,
                                y: Number(queueTableRef.current?.clientHeight) - 55,
                            }}
                            pagination={false}
                            toolBarRender={false}
                            options={false}
                            headerTitle={intl.formatMessage({
                                id: 'pages.billing.zoneClusterQueues',
                            })}
                        />
                    </div>
                    <div className="process-table" ref={ncpuTableRef}>
                        <ProTable
                            rowKey="zoneClusterQueue"
                            columns={[
                                {
                                    title: intl.formatMessage({ id: 'pages.job.slots' }),
                                    dataIndex: 'ncpus',
                                    ellipsis: true,
                                    width: 100,
                                },
                                {
                                    title: intl.formatMessage({ id: 'pages.billing.jobCheckTime' }),
                                    dataIndex: 'totalBillingCpuTime',
                                    align: 'right',
                                    width: 80,
                                    render: (totalBillingCpuTime: number) =>
                                        moneyFormatter(calcCpuTime(totalBillingCpuTime), 2),
                                },
                                {
                                    title: intl.formatMessage({ id: 'pages.billing.jobNum' }),
                                    dataIndex: 'jobNum',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                },
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.avgIntervalRunTime',
                                    }),
                                    dataIndex: 'avgIntervalRunTime',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                    fieldProps: {
                                        precision: 2,
                                    },
                                    renderText: (_, r) => r.avgIntervalRunTime / 1000 / 3600,
                                },
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.billing.avgIntervalPendTime',
                                    }),
                                    dataIndex: 'avgIntervalPendTime',
                                    align: 'right',
                                    valueType: 'digit',
                                    width: 80,
                                    fieldProps: {
                                        precision: 2,
                                    },
                                    renderText: (_, r) => r.avgIntervalPendTime / 1000 / 3600,
                                },
                            ]}
                            dataSource={ncpuTableData}
                            search={false}
                            scroll={{
                                x: 500,
                                y: Number(queueTableRef.current?.clientHeight) - 55,
                            }}
                            pagination={false}
                            toolBarRender={false}
                            options={false}
                            headerTitle={intl.formatMessage({
                                id: 'pages.billing.zoneClusterQueues',
                            })}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
