import React from 'react';

import { getIntl, getLocale } from '@umijs/max';
import { Radio } from 'antd';
import dayjs from 'dayjs';

import TimeSelector from '@/components/time_selector';
import { getAdminUser } from '@/services/api';
import { getAdminBillingBillingAccount } from '@/services/api/admin_billing_account';
import { createAsyncAppEnum, createAsyncZoneClusterQueue } from '@/utils/columns_enum';

import type { ProFormColumnsType, ProFormInstance } from '@ant-design/pro-components';

const intl = getIntl(getLocale());

export function getCpuTimeStatisticsSearch(
    formRef: React.MutableRefObject<
        ProFormInstance<API.IReqGetBillingStatisticsChartCpuTime> | undefined
    >,
): ProFormColumnsType<API.IReqGetBillingStatisticsChartCpuTime>[] {
    return [
        {
            title: intl.formatMessage({
                id: 'pages.billing.billingSubject',
            }),
            dataIndex: 'billingAccountId',
            fieldProps: {
                showSearch: true,
                allowClear: false,
            },
            formItemProps: {
                rules: [
                    {
                        required: true,
                        message: intl.formatMessage({
                            id: 'pages.billing.billingSubjectRequired',
                        }),
                    },
                ],
            },
            request: async () => {
                const { data: billingAccounts } = await getAdminBillingBillingAccount({
                    size: 99999,
                });
                return billingAccounts.map((b) => {
                    return {
                        value: b.id,
                        label: b.name,
                    };
                });
            },
        },
        {
            title: intl.formatMessage({
                id: 'pages.monitorsJobs.statisticalRange',
            }),
            dataIndex: 'selectTimeRange',
            transform: (selectTimeRange) => {
                return {
                    intervalStartTimeStart: selectTimeRange[0],
                    intervalStartTimeEnd: selectTimeRange[1],
                };
            },
            renderFormItem: () => <TimeSelector selectType="month" />,
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.jobTimeUnit' }),
            dataIndex: 'jobTimeUnit',
            initialValue: 'DAYS',
            valueType: 'select',
            fieldProps: {
                options: [
                    {
                        label: intl.formatMessage({ id: 'component.tips.day' }),
                        value: 'DAYS',
                    },
                    {
                        label: intl.formatMessage({ id: 'component.tips.hours' }),
                        value: 'HOURS',
                    },
                    {
                        label: intl.formatMessage({ id: 'component.tips.months' }),
                        value: 'MONTHS',
                    },
                ],
                onChange: (e: React.SetStateAction<string>) => {
                    if (e !== 'HOURS') {
                        formRef.current?.setFieldValue('workingType', [0, 1, 2, 3, 4, 5, 6]);
                        formRef.current?.setFieldValue('workingStart', 0);
                        formRef.current?.setFieldValue('workingEnd', 23);
                    }
                },
            },
        },
        {
            valueType: 'dependency',
            name: ['jobTimeUnit'],
            columns: ({ jobTimeUnit }) => {
                if (jobTimeUnit === 'HOURS') {
                    return [
                        {
                            title: intl.formatMessage({
                                id: 'pages.billing.workingStart',
                            }),
                            dataIndex: 'workingStart',
                            valueType: 'digit',
                            initialValue: 0,
                        },
                        {
                            title: intl.formatMessage({
                                id: 'pages.billing.workingEnd',
                            }),
                            dataIndex: 'workingEnd',
                            valueType: 'digit',
                            initialValue: 23,
                        },
                        {
                            title: intl.formatMessage({
                                id: 'pages.billing.workingTime',
                            }),
                            valueType: 'select',
                            dataIndex: 'week',
                            initialValue: [0, 1, 2, 3, 4, 5, 6],
                            fieldProps: {
                                mode: 'multiple',
                            },
                            request: async () => {
                                return dayjs.weekdays(true).map((v, i) => {
                                    return {
                                        value: i,
                                        label: v,
                                    };
                                });
                            },
                        },
                    ];
                }
                return [];
            },
        },
        {
            title: intl.formatMessage({
                id: 'pages.organizationGroup.isRecursive',
            }),
            dataIndex: 'recursive',
            initialValue: true,
            renderFormItem: () => {
                return (
                    <Radio.Group>
                        <Radio value={true}>
                            {intl.formatMessage({ id: 'pages.appIcon.recursive' })}
                        </Radio>
                        <Radio value={false}>
                            {intl.formatMessage({ id: 'pages.appIcon.notRecursive' })}
                        </Radio>
                    </Radio.Group>
                );
            },
        },
        {
            title: intl.formatMessage({
                id: 'menu.organization.user',
            }),
            dataIndex: 'uids',
            fieldProps: {
                showSearch: true,
                mode: 'multiple',
            },
            request: async () => {
                const { data: users } = await getAdminUser({
                    userScope: 'ALL',
                    size: 99999,
                });
                return users.map((user) => {
                    return {
                        label: user.uid,
                        value: user.uid,
                    };
                });
            },
        },
        {
            title: intl.formatMessage({
                id: 'pages.billing.zoneClusterQueues',
            }),
            dataIndex: 'zoneClusterQueues',
            fieldProps: {
                mode: 'multiple',
                maxTagCount: 0,
            },
            request: createAsyncZoneClusterQueue,
        },
        {
            title: intl.formatMessage({
                id: 'pages.billing.softwares',
            }),
            dataIndex: 'softwares',
            fieldProps: {
                mode: 'multiple',
                maxTagCount: 0,
            },
            request: createAsyncAppEnum,
        },
    ];
}
