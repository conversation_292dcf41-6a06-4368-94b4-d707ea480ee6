import { useEffect, useRef, useState } from 'react';
import { useIntl } from '@umijs/max';
import SearchTablePage from '@/components/search_table_page';
import { ActionType, ProFormInstance } from '@ant-design/pro-components';
import TimeSelector from '../time_selector2';
import {
    getAdminBillingBillingAccountStatisticsOrderBillOverview,
    getAdminBillingBillingAccountStatisticsOrderBillOverviewExport,
} from '@/services/api/admin_billing_billing_account_statistics';
import { getAdminBillingBillingAccount } from '@/services/api/admin_billing_account';
import { isArray } from 'lodash-es';
import { Button, message } from 'antd';
import { downLoadBlob } from '../billing_center/helper';
import {
    createBillingResourcePriceTypeEnum,
    createBillingTypeEnum,
    createBillingUnitTypeEnum,
} from '@/utils/columns_enum';
import dayjs from 'dayjs';

export const ConsumptionDetailPage = () => {
    const intl = useIntl();
    const formRef = useRef<ProFormInstance>();
    const tableActionRef = useRef<ActionType>();
    const [initParamsValue, setInitParamsValue] = useState({
        billingAccountId: undefined,
    });
    const [billingAccountsState, setBillingAccounts] = useState([]);
    const initSearch = async () => {
        const { data: billingAccounts } = await getAdminBillingBillingAccount({
            size: 99999,
        });
        setBillingAccounts(billingAccounts);
        if (isArray(billingAccounts) && formRef.current) {
            formRef.current.setFieldsValue({
                billingAccountId: billingAccounts[0]?.id,
            });
            await setInitParamsValue({
                billingAccountId: billingAccounts[0]?.id,
            });
        }
        tableActionRef.current?.reloadAndRest?.();
    };

    useEffect(() => {
        initSearch();
    }, []);

    return (
        <SearchTablePage
            searchProps={{
                formRef,
                submitter: {
                    onReset: async () => {
                        if (isArray(billingAccountsState)) {
                            formRef.current?.setFieldsValue({
                                billingAccountId: billingAccountsState[0]?.id,
                            });
                        }
                    },
                    render: (_, doms) => {
                        return [
                            ...doms,
                            <Button
                                onClick={async () => {
                                    const values = await formRef.current?.getFieldsFormatValue();
                                    const res =
                                        await getAdminBillingBillingAccountStatisticsOrderBillOverviewExport(
                                            values,
                                        );
                                    try {
                                        downLoadBlob(res.headers['content-disposition'], res.data);
                                    } catch {
                                        message.error(
                                            intl.formatMessage({ id: 'pages.billing.exportError' }),
                                        );
                                    }
                                }}
                            >
                                {intl.formatMessage({ id: 'component.table.export' })}
                            </Button>,
                        ];
                    },
                },
                columns: [
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.billingSubject' }),
                        dataIndex: 'billingAccountId',
                        fieldProps: {
                            showSearch: true,
                            allowClear: false,
                        },
                        formItemProps: {
                            rules: [
                                {
                                    required: true,
                                    message: intl.formatMessage({
                                        id: 'pages.billing.billingSubjectRequired',
                                    }),
                                },
                            ],
                        },
                        request: async () => {
                            const { data: billingAccounts } = await getAdminBillingBillingAccount({
                                size: 99999,
                            });
                            return billingAccounts.map((b) => {
                                return {
                                    value: b.id,
                                    label: b.name,
                                };
                            });
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.monitorsJobs.time' }),
                        dataIndex: 'rangeTime',
                        valueType: 'dateRange',
                        transform: (rangeTime) => {
                            return {
                                intervalStartTimeStart: rangeTime?.[0],
                                intervalStartTimeEnd: rangeTime?.[1],
                            };
                        },
                        renderFormItem: () => (
                            <TimeSelector
                                displayType="onlyDate"
                                options={['custom']}
                                selectType="custom"
                                range
                                pickType="month"
                            />
                        ),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.orderNumber' }),
                        dataIndex: 'orderNoSearch',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.machineHours.resourceType' }),
                        dataIndex: 'resourcePricingTypes',
                        fieldProps: {
                            mode: 'multiple',
                            showSearch: true,
                            maxTagCount: 0,
                        },
                        valueType: 'select',
                        request: () => createBillingResourcePriceTypeEnum(),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.job.billingType' }),
                        dataIndex: 'expenseTypes',
                        fieldProps: {
                            mode: 'multiple',
                            showSearch: true,
                            maxTagCount: 0,
                        },
                        valueType: 'select',
                        request: () => createBillingTypeEnum(),
                    },
                ],
                onFinish: async () => {
                    return await tableActionRef.current?.reloadAndRest?.();
                },
            }}
            tableProps={{
                actionRef: tableActionRef,
                request: async ({ pageSize: size, current: page }) => {
                    const values = formRef.current?.getFieldsFormatValue?.();
                    if (!values.billingAccountId) {
                        return {
                            success: true,
                            data: [],
                            total: 0,
                        };
                    }
                    const { data, total } =
                        await getAdminBillingBillingAccountStatisticsOrderBillOverview({
                            ...initParamsValue,
                            ...values,
                            page,
                            size,
                        });
                    return {
                        success: true,
                        data,
                        total,
                    };
                },
                columns: [
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.billCycle' }),
                        dataIndex: 'billTime',
                        align: 'center',
                        valueType: 'date',
                        fieldProps: {
                            format: 'YYYY-MM',
                        },
                    },
                    {
                        title: '订单编号',
                        dataIndex: 'orderNo',
                        align: 'center',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.sku' }),
                        dataIndex: 'sku',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.resourceType' }),
                        dataIndex: 'resourcePricingType',
                        valueType: 'select',
                        request: () => createBillingResourcePriceTypeEnum(),
                    },
                    {
                        title: intl.formatMessage({
                            id: 'pages.searchTable.updateForm.schedulingPeriod.timeLabel',
                        }),
                        dataIndex: 'startTime',
                        align: 'center',
                        renderText: (_, r) =>
                            r.orderType === 'DEFAULT'
                                ? '-'
                                : dayjs(r.startTime).format('YYYY/MM/DD'),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.endTime' }),
                        dataIndex: 'endTime',
                        align: 'center',
                        renderText: (_, r) =>
                            r.orderType === 'DEFAULT' ? '-' : dayjs(r.endTime).format('YYYY/MM/DD'),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.storage.unitOfCharging' }),
                        dataIndex: 'unit',
                        valueType: 'select',
                        request: async () => createBillingUnitTypeEnum(),
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.billingMode' }),
                        dataIndex: 'expenseType',
                        valueEnum: {
                            MONTH: '包月',
                            YEAR: '包年',
                            NEED: '按需',
                        },
                    },
                    {
                        title: intl.formatMessage({
                            id: 'pages.consumptionDetail.productQuantity',
                        }),
                        dataIndex: 'quantity',
                        valueType: 'digit',
                        align: 'right',
                    },
                    {
                        title: intl.formatMessage({ id: 'component.remarks' }),
                        dataIndex: 'remark',
                    },
                    {
                        title: intl.formatMessage({
                            id: 'pages.consumptionDetail.billingQuantity',
                        }),
                        dataIndex: 'billQuantity',
                        align: 'right',
                        valueType: 'digit',
                        renderText: (billQuantity) => billQuantity.toFixed(2),
                    },
                    {
                        title: intl.formatMessage({
                            id: 'pages.consumptionDetail.discountUnitPrice',
                        }),
                        dataIndex: 'discountPrice',
                        valueType: 'money',
                        align: 'right',
                        renderText: (text) => text / 100,
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.consumptionDetail.billAmount' }),
                        dataIndex: 'amount',
                        valueType: 'money',
                        align: 'right',
                        renderText: (text) => text / 100,
                    },
                ],
            }}
        />
    );
};
