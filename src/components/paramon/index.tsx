import { useEffect, useRef, useState } from 'react';
import { Select, Slider, Spin } from 'antd';
import { useIntl } from '@umijs/max';
import dayjs from 'dayjs';

import { apiDataToDataItems } from './model';
import ParamonRenderHtml from './render/html';
import { useRequest } from 'ahooks';
import { getAdminParamonNodeMonitor, getAdminZoneList } from '@/services/api';

import './index.less';

export default function Paramon() {
    const intl = useIntl();
    const canvasRef = useRef<HTMLDivElement>(null);
    const paramonCanvasCoreRef = useRef<ParamonRenderHtml>();
    const [cluster, setCluster] = useState('');
    const [loading, setLoading] = useState(false);
    const [noData, setNoData] = useState(false);
    const [lastUpdateTime, setLastUpdateTime] = useState('');
    const [scale, setScale] = useState(50);

    const { data: clusterOptions } = useRequest(async () => {
        const response = await getAdminZoneList({
            size: 999,
        });
        return response.data.map((item) => ({
            value: item.zoneCode,
            label: item.zoneName,
        }));
    });

    useRequest(
        async () => {
            if (!cluster) return;

            const response = await getAdminParamonNodeMonitor({
                cluster,
            });

            setLoading(false);
            setNoData(response.length === 0);
            if (response.length > 0) {
                setLastUpdateTime(dayjs(response[0].value[0] * 1000).format('YYYY-MM-DD HH:mm:ss'));
            } else {
                setLastUpdateTime('');
            }

            const paramonCanvasCore = paramonCanvasCoreRef.current;
            if (!paramonCanvasCore) return;

            paramonCanvasCore.setData(apiDataToDataItems(response));
        },
        {
            // ready: false,
            refreshDeps: [cluster],
            debounceWait: 100,
            // 轮询间隔
            pollingInterval: 2000,
        },
    );

    useEffect(() => {
        const canvasEl = canvasRef.current;
        if (!canvasEl) return;

        if (!paramonCanvasCoreRef.current) {
            paramonCanvasCoreRef.current = new ParamonRenderHtml(canvasEl);
            // @ts-ignore
            window.paramonCanvasCore = paramonCanvasCoreRef.current;
        }

        const paramonCanvasCore = paramonCanvasCoreRef.current;
        // paramonCanvasCore._debugShow();
        paramonCanvasCore.setScale(scale);
    }, [canvasRef]);

    return (
        <div className="paramon">
            <div className="paramon-ctl">
                <div className="paramon-ctl-item">
                    <div>集群选择：</div>
                    <Select
                        style={{ width: 120 }}
                        options={clusterOptions}
                        onChange={(val) => {
                            setCluster(val);
                            setLoading(true);
                        }}
                    />
                </div>
                <div className="paramon-ctl-item">
                    <div>缩放：</div>
                    <Slider
                        style={{ width: 200 }}
                        step={0.5}
                        min={14}
                        max={100}
                        value={scale}
                        onChange={(val) => {
                            setScale(val);
                            if (paramonCanvasCoreRef.current) {
                                paramonCanvasCoreRef.current.setScale(val);
                            }
                        }}
                    />
                </div>
                {lastUpdateTime && (
                    <div className="paramon-ctl-last-update-time">
                        最后更新时间：{lastUpdateTime}
                    </div>
                )}
            </div>
            <Spin spinning={loading}>
                {noData ? intl.formatMessage({ id: 'pages.monitorsJobs.noData' }) : ''}
            </Spin>
            <div className="paramon-canvas" ref={canvasRef}></div>
        </div>
    );
}
