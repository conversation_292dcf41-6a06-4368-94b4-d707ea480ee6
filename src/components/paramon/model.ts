import { gb100, mb100, mb120, mb1500 } from './consts';

export class DataItem {
    nodeName: string;

    gflops?: number;
    cpuUsed?: number;
    memoryUsed?: number;
    memoryRead?: number;
    memoryWrite?: number;
    memoryRW?: number;
    nfsRead?: number;
    nfsWrite?: number;
    diskRead?: number;
    diskWrite?: number;
    netSend?: number;
    netReceive?: number;
    ibSend?: number;
    ibReceive?: number;

    error?: {
        cpuUsed: boolean;
        memoryUsed: boolean;
        memoryRead: boolean;
        memoryWrite: boolean;
        nfsRead: boolean;
        nfsWrite: boolean;
        diskRead: boolean;
        diskWrite: boolean;
        netSend: boolean;
        netReceive: boolean;
        ibSend: boolean;
        ibReceive: boolean;
    };

    static fromJson(json: Record<string, any>) {
        return {
            nodeName: json['nodeName'] ?? '',
            cpuUsed: json['cpuUsed'] ?? 0,
            swapUsed: json['swapUsed'] ?? 0,
            memoryUsed: json['memoryUsed'] ?? 0,
            memoryRead: json['memoryRead'] ?? 0,
            memoryWrite: json['memoryWrite'] ?? 0,
            nfsRead: json['nfsRead'] ?? 0,
            nfsWrite: json['nfsWrite'] ?? 0,
            diskRead: json['diskRead'] ?? 0,
            diskWrite: json['diskWrite'] ?? 0,
            netSend: json['netSend'] ?? 0,
            netReceive: json['netReceive'] ?? 0,
            ibSend: json['ibSend'] ?? 0,
            ibReceive: json['ibReceive'] ?? 0,
        };
    }

    static random() {
        return {
            nodeName: 'node-' + Math.floor(Math.random() * 100),

            cpuUsed: Math.floor(Math.random() * 100),
            swapUsed: Math.floor(Math.random() * 100),
            memoryUsed: Math.floor(Math.random() * 100),
            memoryRead: Math.floor(Math.random() * gb100),
            memoryWrite: Math.floor(Math.random() * gb100),
            nfsRead: Math.floor(Math.random() * mb100),
            nfsWrite: Math.floor(Math.random() * mb100),
            diskRead: Math.floor(Math.random() * mb100),
            diskWrite: Math.floor(Math.random() * mb100),
            netSend: Math.floor(Math.random() * mb120),
            netReceive: Math.floor(Math.random() * mb120),
            ibSend: Math.floor(Math.random() * mb1500),
            ibReceive: Math.floor(Math.random() * mb1500),

            error: {
                cpuUsed: Math.random() > 0.7,
                memoryUsed: Math.random() > 0.7,
                memoryRead: Math.random() > 0.7,
                memoryWrite: Math.random() > 0.7,
                nfsRead: Math.random() > 0.7,
                nfsWrite: Math.random() > 0.7,
                diskRead: Math.random() > 0.7,
                diskWrite: Math.random() > 0.7,
                netSend: Math.random() > 0.7,
                netReceive: Math.random() > 0.7,
                ibSend: Math.random() > 0.7,
                ibReceive: Math.random() > 0.7,
            },
        };
    }
}

const keyMap = {
    gflops: 'gflops',
    cpuutil: 'cpuUsed',
    memthr: 'memoryRead',
    memthw: 'memoryWrite',
    memthrw: 'memoryRW',
    memutil: 'memoryUsed',
    ethtrans: 'netSend',
    ethrecei: 'netReceive',
    diskrw: 'diskWrite',
    diskro: 'diskRead',
    ibtrans: 'ibSend',
    ibrecei: 'ibReceive',
    nfsro: 'nfsRead',
    nfsrw: 'nfsWrite',
};

const valueParserMap = {
    gflops: parseInt,
    cpuutil: parseFloat,
    memutil: parseFloat,
    memthw: parseInt,
    memthr: parseInt,
    memthrw: parseInt,
    ibrecei: parseInt,
    ibtrans: parseInt,
    ethrecei: parseInt,
    ethtrans: parseInt,
    diskrw: parseInt,
    diskro: parseInt,
    nfsro: parseInt,
    nfsrw: parseInt,
};

export function apiDataToDataItems(json: API.IApiResAdminParamonMetricModel[]) {
    const dataMap = json.reduce((acc, cur) => {
        const metric = cur.metric;
        if (!acc[metric.instance]) {
            acc[metric.instance] = {
                nodeName: metric.instance,
            };
        }
        if (keyMap[metric.type] && valueParserMap[metric.type]) {
            acc[metric.instance][keyMap[metric.type]] = valueParserMap[metric.type](cur.value[1]);
        }
        return acc;
    }, {});
    return Object.values(dataMap) as DataItem[];
}
