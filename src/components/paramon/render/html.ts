import { bgColor, borderColor, errorColor } from '../consts';
import ParamonCore from '../core';

import type { DataItem } from '../model';

interface IPosition {
    x: number;
    y: number;
}

export default class ParamonRenderHtml extends ParamonCore {
    private _containerEl: HTMLElement;
    private _viewportEl: HTMLElement;
    private _nodeDetailEl: HTMLElement;
    private _prevContentHeight: number;
    private _prevViewportWidth: number;
    private _prevViewportHeight: number;
    private _prevViewportY: number;
    private _prevData: any[];
    private _prevViewportFirstDataIndex: number;
    private _prevViewportLastDataIndex: number;
    private _pre_debug_show: boolean;
    private _viewData?: DataItem[];
    constructor(el: HTMLDivElement, option?) {
        super(el, option);

        this._initDom();
    }

    dispose() {
        this.el.removeEventListener('scroll', this._scrollEventHandler);

        this._viewportEl.innerHTML = '';
        this._viewportEl = null;
        this._containerEl = null;

        super.dispose();
    }

    private _initDom() {
        const styleEl = document.createElement('style');
        styleEl.innerHTML = `
            .paramon-root {
                position: relative;
            }
            .paramon-container {
                display: block;
                position: relative;
                top: 0;
            }
            .paramon-viewport {
                display: block;
                position: absolute;
                overflow: hidden;
                width: 100%;
            }
            .paramon-node-detail {
                position: absolute;
                display: none;
                white-space: nowrap;
                background-color: #fff;
                padding: 4px;
                border-radius: 5px;
                box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.3);
            }
            .paramon-node-detail-item {
                display: flex;
                justify-content: space-between;
                padding: 4px 8px;
            }
            .paramon-node-detail-item-label {
                padding-right: 20px;
            }
            .paramon-node-detail-item-value {
                font-weight: bold;
            }
            .paramon-node {
                display: block;
                box-sizing: border-box;
                position: absolute;
                border: 1px solid ${borderColor};
                background-color: ${bgColor};
                color: #fff;
            }
            .paramon-node:hover {
                box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.3);
                border-color: #1890ff;
                //transform: scale(1.05, 1.05);
            }
            .paramon-indicator {
                display: block;
                box-sizing: border-box;
                position: absolute;
                border: 1px solid ${borderColor};
            }
            .paramon-indicator-value {
                display: block;
                box-sizing: border-box;
                position: absolute;
                transition-duration: .5s;
                transition-timing-function: ease-in-out;
            }
            .paramon-node-name {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
            }
            .paramon-node-debug {
                display: block;
                position: absolute;
            }
        `;

        const rootEl = document.createElement('paramon-root');
        rootEl.className = 'paramon-root';

        const containerEl = document.createElement('paramon-container');
        containerEl.className = 'paramon-container';

        const viewportEl = document.createElement('paramon-viewport');
        viewportEl.className = 'paramon-viewport';

        const nodeDetailEl = document.createElement('paramon-node-detail');
        nodeDetailEl.className = 'paramon-node-detail';

        const shwdowRoot = this.el.attachShadow({ mode: 'open' });
        shwdowRoot.appendChild(rootEl);
        rootEl.appendChild(styleEl);
        rootEl.appendChild(containerEl);
        rootEl.appendChild(nodeDetailEl);
        containerEl.appendChild(viewportEl);

        this._containerEl = containerEl;
        this._viewportEl = viewportEl;
        this._nodeDetailEl = nodeDetailEl;

        // addEventListener
        this._scrollEventHandler = this._scrollEventHandler.bind(this);
        this.el.addEventListener('scroll', this._scrollEventHandler);
        this.el.addEventListener('scroll', this._scrollEventHandler);
    }

    protected _render() {
        if (!(Array.isArray(this.data) && this.el)) return;

        // 计算后的布局没有变化、以及数据没有变化时，可以不重新渲染
        if (
            this._debug_show === this._pre_debug_show &&
            this.contentHeight === this._prevContentHeight &&
            this.viewportRect.width === this._prevViewportWidth &&
            this.viewportRect.height === this._prevViewportHeight &&
            this.viewportRect.y === this._prevViewportY &&
            this.data === this._prevData &&
            this.viewportFirstDataIndex === this._prevViewportFirstDataIndex &&
            this.viewportLastDataIndex === this._prevViewportLastDataIndex
        ) {
            return;
        }

        if (this._debug_show) {
            console.log(
                'ParamonRenderHtml render viewData range',
                this.viewportFirstDataIndex,
                this.viewportLastDataIndex,
            );
        }

        if (this._debug_no_render) return;

        const startTime = performance.now();
        const containerEl = this._containerEl;
        containerEl.style.height = `${this.contentHeight}px`;

        const viewportEl = this._viewportEl;
        const viewportElStyle = viewportEl.style;

        viewportElStyle.width = `${this.viewportRect.width}px`;
        viewportElStyle.height = `${this.viewportRect.height}px`;
        viewportElStyle.top = `${this.viewportRect.y}px`;

        const { nodeWidth, nodeHeight, nodePositions } = this.nodesLayout;
        const viewData = this.data.slice(this.viewportFirstDataIndex, this.viewportLastDataIndex);
        this._viewData = viewData;

        const childLen = viewportEl.children.length;
        if (childLen > viewData.length) {
            for (let i = viewData.length; i < childLen; i++) {
                (viewportEl.children.item(i) as HTMLElement).style.display = 'none';
                // 两种方式都可以，但是第一种会占用更多内存，第二种会消耗更多时间
                // 如果发现内存DOM节点过多，导致卡，则换成第二种
                // (viewportEl.children.item(viewData.length) as HTMLElement).style.display = 'none';
            }
        }

        viewData.forEach((dataItem, index) => {
            const rect = nodePositions[index];
            const nodeEl = this._getNodeEl(viewportEl, index);
            const nodeElStyle = nodeEl.style;

            nodeElStyle.display = 'block';
            nodeElStyle.width = nodeWidth + 'px';
            nodeElStyle.height = nodeHeight + 'px';
            nodeElStyle.left = rect.x + 'px';
            nodeElStyle.top = rect.y + 'px';

            // render node content
            this.indicatorConfigEntries.forEach(
                (
                    [indicatorKey, { color, boxRect, valueReverse, valueVertical, valueMax }],
                    index1,
                ) => {
                    const { indEl, valueEl } = this._getIndicatorEl(nodeEl, index1);
                    const indElStyle = indEl.style;
                    indElStyle.left = boxRect.x + 'px';
                    indElStyle.top = boxRect.y + 'px';
                    indElStyle.width = boxRect.width + 'px';
                    indElStyle.height = boxRect.height + 'px';

                    const valueElStyle = valueEl.style;
                    valueElStyle.backgroundColor = color;
                    if (dataItem.error && dataItem.error[indicatorKey]) {
                        valueElStyle.backgroundColor = errorColor;
                    }

                    const valuePercent =
                        Math.min(1, (dataItem[indicatorKey] ?? 0) / valueMax) * 100 + '%';
                    if (valueVertical) {
                        valueElStyle.width = boxRect.width - 2 + 'px';
                        valueElStyle.height = valuePercent;
                        valueElStyle.transitionProperty = 'height';
                    } else {
                        valueElStyle.width = valuePercent;
                        valueElStyle.height = boxRect.height - 2 + 'px';
                        valueElStyle.transitionProperty = 'width';
                    }
                    if (valueReverse) {
                        valueElStyle.right = '0px';
                        valueElStyle.bottom = '0px';
                    } else {
                        valueElStyle.left = '0px';
                        valueElStyle.top = '0px';
                    }
                },
            );

            this._getNameEl(nodeEl).innerHTML = dataItem.nodeName;

            if (this._debug_show) {
                this._getDebugEl(nodeEl).innerHTML = this.viewportFirstDataIndex + index + '';
            }
        });

        this._prevContentHeight = this.contentHeight;
        this._prevViewportWidth = this.viewportRect.width;
        this._prevViewportHeight = this.viewportRect.height;
        this._prevViewportY = this.viewportRect.y;
        this._prevData = this.data;
        this._prevViewportFirstDataIndex = this.viewportFirstDataIndex;
        this._prevViewportLastDataIndex = this.viewportLastDataIndex;
        this._pre_debug_show = this._debug_show;

        if (this._debug_show) {
            console.log('ParamonRenderHtml render cost', performance.now() - startTime, 'ms');
        }
    }

    private _renderNodeDetail(dataItem: DataItem, position: IPosition) {
        const nodeEl = this._nodeDetailEl;
        const nodeElStyle = nodeEl.style;

        nodeElStyle.display = 'block';
        nodeElStyle.left = position.x + 'px';
        nodeElStyle.top = position.y + 'px';
        const innerHTMLs = [
            `<div class="paramon-node-detail-item">
                <span class="paramon-node-detail-item-label">${dataItem.nodeName}</span>
            </div class="paramon-node-detail-item-value">`,
        ];
        this.tooltipIndicatorConfigEntries.forEach(([indicatorKey, { title, valueFormatter }]) => {
            innerHTMLs.push(`<div class="paramon-node-detail-item">
                    <span class="paramon-node-detail-item-label">${title}:</span>
                    <span class="paramon-node-detail-item-value">${valueFormatter(dataItem[indicatorKey])}</span>
                </div>`);
        });
        nodeEl.innerHTML = innerHTMLs.join('');
    }

    private _scrollEventHandler(evt: Event) {
        this.setScrollTop((evt.target as HTMLDivElement).scrollTop);
    }

    private _getNodeEl(viewportEl: HTMLElement, index: number) {
        let nodeEl = viewportEl.children.item(index) as HTMLElement;
        if (!nodeEl) {
            nodeEl = document.createElement('paramon-node');
            nodeEl.className = 'paramon-node';
            nodeEl.addEventListener(
                'pointermove',
                this._nodePointerMoveEventHandler.bind(this, index),
            );
            viewportEl.appendChild(nodeEl);
            nodeEl.addEventListener(
                'pointerleave',
                this._nodePointerLeaveEventHandler.bind(this, index),
            );
            viewportEl.appendChild(nodeEl);
        }
        return nodeEl;
    }

    private _getIndicatorEl(nodeEl: HTMLElement, index: number) {
        const indClassName = 'paramon-indicator';
        const valClassName = 'paramon-indicator-value';
        let indEl = nodeEl.children.item(index) as HTMLElement;
        let valueEl: HTMLElement;
        if (!indEl) {
            indEl = document.createElement('paramon-indicator');
            indEl.className = indClassName;

            valueEl = document.createElement('div');
            valueEl.className = valClassName;

            indEl.appendChild(valueEl);
            nodeEl.appendChild(indEl);
        } else {
            valueEl = indEl.children.item(0) as HTMLElement;
        }
        return { indEl, valueEl };
    }

    private _getNameEl(nodeEl: HTMLElement) {
        const className = 'paramon-node-name';
        let nameEl = nodeEl.getElementsByClassName(className)[0] as HTMLElement;
        if (!nameEl) {
            nameEl = document.createElement('div');
            nameEl.className = className;
            nodeEl.appendChild(nameEl);
        }
        return nameEl;
    }

    private _getDebugEl(nodeEl: HTMLElement) {
        const className = 'paramon-node-debug';
        let debugEl = nodeEl.getElementsByClassName(className)[0] as HTMLElement;
        if (!debugEl) {
            debugEl = document.createElement('div');
            debugEl.className = className;
            nodeEl.appendChild(debugEl);
        }
        return debugEl;
    }

    private _nodePointerMoveEventHandler(index: number, evt: PointerEvent) {
        if (this._viewData && this._viewData[index]) {
            const nodeDetailEl = this._nodeDetailEl;
            const nodeDetailElStyle = nodeDetailEl.style;
            const containerRect = this.el.getBoundingClientRect();
            const relativeX = evt.clientX - containerRect.x;
            const relativeY = evt.clientY - containerRect.y;

            // 优先显示在右下
            let x = relativeX + 20;
            let y = relativeY + 20 + this.el.scrollTop;

            // 尝试第一次渲染
            nodeDetailElStyle.visibility = 'hidden';
            this._renderNodeDetail(this._viewData[index], {
                x,
                y,
            });

            // 检查是否超出容器边界，并修正渲染位置
            const nodeDetailRect = nodeDetailEl.getBoundingClientRect();
            if (x + nodeDetailRect.width >= containerRect.width) {
                x = Math.max(0, relativeX - 20 - nodeDetailRect.width);
            }
            if (y + nodeDetailRect.height >= containerRect.height + this.el.scrollTop) {
                y = Math.max(0, relativeY - 20 - nodeDetailRect.height + this.el.scrollTop);
            }
            nodeDetailElStyle.left = x + 'px';
            nodeDetailElStyle.top = y + 'px';
            nodeDetailElStyle.visibility = 'visible';
        }
    }

    private _nodePointerLeaveEventHandler(index: number) {
        if (this._viewData && this._viewData[index]) {
            this._nodeDetailEl.style.display = 'none';
        }
    }
}
