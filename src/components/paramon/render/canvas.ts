import { bgColor, borderColor, scrollBarWidth, scrollBarColor } from '../consts';
import ParamonCore from '../core';

export default class ParamonRenderCanvas extends ParamonCore {
    private _ctx: CanvasRenderingContext2D;
    private _pointerdown: boolean;
    private scrollBarHeight: number;

    constructor(el: HTMLCanvasElement, option?) {
        super(el, option);

        this._ctx = el.getContext('2d');

        // addEventListener
        this._wheelEventHandler = this._wheelEventHandler.bind(this);
        this._pointerdownEventHandler = this._pointerdownEventHandler.bind(this);
        this._pointerupEventHandler = this._pointerupEventHandler.bind(this);
        this._pointermoveEventHandler = this._pointermoveEventHandler.bind(this);
        el.addEventListener('wheel', this._wheelEventHandler);
        el.addEventListener('pointerdown', this._pointerdownEventHandler);
        el.addEventListener('pointerup', this._pointerupEventHandler);
        el.addEventListener('pointermove', this._pointermoveEventHandler);
    }

    dispose() {
        this.el.removeEventListener('wheel', this._wheelEventHandler);
        this.el.removeEventListener('pointerdown', this._pointerdownEventHandler);
        this.el.removeEventListener('pointerup', this._pointerupEventHandler);
        this.el.removeEventListener('pointermove', this._pointermoveEventHandler);
        this._ctx = null;

        super.dispose();
    }

    protected _caculateNodesLayout() {
        super._caculateNodesLayout();
        this.scrollBarHeight = (this.height * this.height) / this.contentHeight;
    }

    protected _wheelEventHandler(evt: WheelEvent) {
        console.log('_wheelEventHandler', evt);
        this.setScrollTop(
            Math.min(this.height - this.scrollBarHeight, Math.max(0, this.scrollTop + evt.deltaY)),
        );
    }

    protected _pointerdownEventHandler(evt: PointerEvent) {
        this._pointerdown = true;
        console.log('_pointerdownEventHandler', evt);
    }

    protected _pointerupEventHandler(evt: PointerEvent) {
        this._pointerdown = false;
        console.log('_pointerupEventHandler', evt);
    }

    protected _pointermoveEventHandler(evt: PointerEvent) {
        if (!this._pointerdown) return;
        console.log('_pointermoveEventHandler', evt);
    }

    protected _render() {
        if (!(Array.isArray(this.data) && this.data.length > 0 && this.el && this._ctx)) return;

        const ctx = this._ctx;
        if (this.contentHeight > this.height) {
            // render scroll bar
            this._renderScrollBar();
        }

        const { nodeWidth, nodeHeight, nodePositions } = this.nodesLayout;
        this.data.forEach((_, index) => {
            const rect = nodePositions[index];
            const dataItem = this.data[index];
            ctx.save();

            // render node box
            ctx.strokeStyle = borderColor;
            ctx.fillStyle = bgColor;
            ctx.translate(rect.x, rect.y);
            ctx.strokeRect(0, 0, nodeWidth, nodeHeight);
            ctx.fillRect(0, 0, nodeWidth, nodeHeight);

            // render node content
            this.indicatorConfigEntries.forEach(
                ([
                    indicatorKey,
                    { color, boxRect, valueRect, valueReverse, valueVertical, valueMax },
                ]) => {
                    ctx.fillStyle = color;
                    ctx.strokeRect(boxRect.x, boxRect.y, boxRect.width, boxRect.height);
                    const valuePercent = Math.min(1, (dataItem[indicatorKey] ?? 0) / valueMax);
                    if (valueVertical) {
                        valueRect.width = boxRect.width;
                        valueRect.height = valuePercent * boxRect.height;
                    } else {
                        valueRect.width = valuePercent * boxRect.width;
                        valueRect.height = boxRect.height;
                    }
                    valueRect.x = boxRect.x;
                    valueRect.y = boxRect.y;
                    if (valueReverse) {
                        valueRect.x = boxRect.x + boxRect.width;
                        valueRect.y = boxRect.y + boxRect.height;
                        valueRect.width = -valueRect.width;
                        valueRect.height = -valueRect.height;
                    }
                    ctx.fillRect(valueRect.x, valueRect.y, valueRect.width, valueRect.height);
                },
            );

            ctx.restore();
        });
    }

    protected _renderScrollBar() {
        const ctx = this._ctx;

        ctx.fillStyle = scrollBarColor;
        ctx.clearRect(this.width, 0, -scrollBarWidth, this.height);
        if (ctx.roundRect) {
            ctx.beginPath();
            ctx.roundRect(this.width, this.scrollTop, -scrollBarWidth, this.scrollBarHeight, 10);
            ctx.fill();
            ctx.closePath();
        } else {
            ctx.fillRect(this.width, this.scrollTop, -scrollBarWidth, this.scrollBarHeight);
        }
    }
}
