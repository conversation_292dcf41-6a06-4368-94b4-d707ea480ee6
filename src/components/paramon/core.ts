import { throttle } from 'lodash-es';
import {
    gb100,
    mb100,
    mb120,
    mb1500,
    gflopsColor,
    cpuColor,
    diskReadColor,
    diskWriteColor,
    memoryColor,
    memoryReadColor,
    netReceiveColor,
    netSendColor,
} from './consts';
import type { DataItem } from './model';

interface IOption {
    observeResize: boolean;
}

type IIndicatorKey = Exclude<keyof DataItem, 'nodeName' | 'error'>;

interface IIndicatorConfig {
    hiddenInChart?: boolean;
    hiddenInTooltip?: boolean;
    title?: string;
    /** 显示的颜色 */
    color: string;
    /** 容器 矩形 */
    boxRect: Rect;
    /** 值 矩形 */
    valueRect: Rect;
    /** 值 是否垂直方向 */
    valueVertical?: boolean;
    /** 值 是否反转顺序，默认情况下，以左上角为原点来绘制，反转后，以右下角为原点来绘制 */
    valueReverse?: boolean;
    /** 值 最大值，用来计算百分比 */
    valueMax: number;
    valueFormatter: (value?: string | number) => string;
}

interface IPosition {
    x: number;
    y: number;
}

class Rect {
    x: number;
    y: number;
    width: number;
    height: number;

    constructor(x: number, y: number, width: number, height: number) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
}

function mbValuesFormatter(value: number | undefined) {
    // @ts-ignore
    const valNum = parseInt(value);
    return Number.isNaN(valNum) ? '' : (valNum / 1048576).toFixed(2);
}

function gbValuesFormatter(value: number | undefined) {
    // @ts-ignore
    const valNum = parseInt(value);
    return Number.isNaN(valNum) ? '' : (valNum / 1073741824).toFixed(2);
}

function usageValueFormatter(value: number | undefined) {
    // @ts-ignore
    const valNum = parseFloat(value);
    return Number.isNaN(valNum) ? '' : valNum.toFixed(2);
}

export default class ParamonCore {
    protected _debug_no_render = false;
    protected _debug_show = false;
    protected el: HTMLCanvasElement | HTMLDivElement;
    protected width: number;
    protected height: number;
    protected contentHeight: number;
    protected scrollTop = 0;
    protected option: IOption;
    protected resizeObserver?: ResizeObserver;
    protected data?: DataItem[];
    protected scale: number = 100;
    protected nodesLayout = {
        nodeWidth: 0,
        nodeHeight: 0,
        nodePositions: [] as IPosition[],
    };
    protected viewportRect = new Rect(0, 0, 0, 0);
    protected viewportFirstDataIndex = 0;
    protected viewportLastDataIndex = 0;
    private _preScrollTop: number = 0;
    protected _throttleRender = throttle(this._render, 33, { leading: false });
    protected _throttleLayoutAndRender = throttle(this._layoutAndRender, 33, { leading: false });
    protected indicatorConfigRecord: Record<IIndicatorKey, IIndicatorConfig> = {
        gflops: {
            hiddenInChart: true,
            title: 'Gflops',
            color: gflopsColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: 0,
            valueFormatter: gbValuesFormatter,
        },
        cpuUsed: {
            color: cpuColor,
            title: 'CPU(all)%',
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: 100,
            valueVertical: true,
            valueReverse: true,
            valueFormatter: usageValueFormatter,
        },
        memoryUsed: {
            title: 'Memory%',
            color: memoryColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: 100,
            valueVertical: true,
            valueReverse: true,
            valueFormatter: usageValueFormatter,
        },
        memoryRead: {
            hiddenInTooltip: true,
            color: memoryReadColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: gb100,
            valueVertical: true,
            valueReverse: false,
            valueFormatter: gbValuesFormatter,
        },
        memoryWrite: {
            hiddenInTooltip: true,
            color: memoryColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: gb100,
            valueVertical: true,
            valueReverse: true,
            valueFormatter: gbValuesFormatter,
        },
        memoryRW: {
            hiddenInChart: true,
            title: 'MemRW (GB/s)',
            color: memoryColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: gb100,
            valueFormatter: gbValuesFormatter,
        },
        nfsRead: {
            title: 'NFS Read (MB/s)',
            color: diskReadColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb100,
            valueVertical: false,
            valueReverse: true,
            valueFormatter: mbValuesFormatter,
        },
        nfsWrite: {
            title: 'NFS Write (MB/s)',
            color: diskWriteColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb100,
            valueVertical: false,
            valueReverse: false,
            valueFormatter: mbValuesFormatter,
        },
        diskRead: {
            title: 'Disk Read (MB/s)',
            color: diskReadColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb100,
            valueVertical: false,
            valueReverse: true,
            valueFormatter: mbValuesFormatter,
        },
        diskWrite: {
            title: 'Disk Write (MB/s)',
            color: diskWriteColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb100,
            valueVertical: false,
            valueReverse: false,
            valueFormatter: mbValuesFormatter,
        },
        netSend: {
            title: 'Net Send (MB/s)',
            color: netSendColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb120,
            valueVertical: true,
            valueReverse: true,
            valueFormatter: mbValuesFormatter,
        },
        netReceive: {
            title: 'Net Recv (MB/s)',
            color: netReceiveColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb120,
            valueVertical: true,
            valueReverse: false,
            valueFormatter: mbValuesFormatter,
        },
        ibSend: {
            title: 'IB Send (MB/s)',
            color: netSendColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb1500,
            valueVertical: true,
            valueReverse: true,
            valueFormatter: mbValuesFormatter,
        },
        ibReceive: {
            title: 'IB Recv (MB/s)',
            color: netReceiveColor,
            boxRect: new Rect(0, 0, 0, 0),
            valueRect: new Rect(0, 0, 0, 0),
            valueMax: mb1500,
            valueVertical: true,
            valueReverse: false,
            valueFormatter: mbValuesFormatter,
        },
    };
    // @ts-ignore
    // 图表中的指标
    protected indicatorConfigEntries: [IIndicatorKey, IIndicatorConfig][] =
        Object.entries<IIndicatorConfig>(this.indicatorConfigRecord).filter(
            (item) => item[1].hiddenInChart !== true,
        );

    // @ts-ignore
    // Tooltip中的指标
    protected tooltipIndicatorConfigEntries: [IIndicatorKey, IIndicatorConfig][] =
        Object.entries<IIndicatorConfig>(this.indicatorConfigRecord).filter(
            (item) => item[1].hiddenInTooltip !== true,
        );

    constructor(el: HTMLCanvasElement | HTMLDivElement, option?: IOption) {
        this.option = {
            observeResize: true,
            ...option,
        };

        const rect = el.getBoundingClientRect();
        this.el = el;
        this.resize(rect.width, rect.height);

        if (this.option.observeResize) {
            const resizeObserver = new ResizeObserver((entries) => {
                for (const entry of entries) {
                    if (entry.target === el && entry.contentRect) {
                        this.resize(entry.contentRect.width, entry.contentRect.height);
                    }
                }
            });
            resizeObserver.observe(el);
            this.resizeObserver = resizeObserver;
        }
    }

    dispose() {
        this.el = null;
        this.option = null;
        if (this.resizeObserver != null) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
    }

    resize(width: number, height: number) {
        if (this.width === width && this.height === height) return;

        this.width = width;
        this.height = height;

        if (this.data) {
            this._throttleLayoutAndRender();
        }
    }

    setData(data: DataItem[]) {
        this.data = data;
        this._throttleLayoutAndRender();
    }

    setScale(scale: number) {
        this.scale = scale;
        this._throttleLayoutAndRender();
    }

    setScrollTop(top: number) {
        this.scrollTop = top;
        this._throttleLayoutAndRender();
    }

    _debugStopRender() {
        this._debug_no_render = true;
    }

    _debugStartRender() {
        this._debug_no_render = false;
    }

    _debugShow() {
        this._debug_show = true;
        this._render();
    }

    protected _layoutAndRender() {
        if (!(Array.isArray(this.data) && this.el)) return;

        if (this.el instanceof HTMLCanvasElement) {
            this.el.width = this.width;
            this.el.height = this.height;
        }

        this._caculateNodesLayout();
        this._render();
    }

    protected _caculateNodesLayout() {
        const startTime = performance.now();
        const layoutOption = {
            maxCrossAxisExtent: 4 * this.scale,
            padding: 20,
            mainAxisSpacing: 10,
            crossAxisSpacing: 10,
            childAspectRatio: 1.62,
            minColumnWidth: 56,
        };
        const nodesBoxWidth = this.width - 2 * layoutOption.padding + layoutOption.mainAxisSpacing;
        const columnNum = Math.ceil(
            nodesBoxWidth /
                (Math.max(layoutOption.minColumnWidth, layoutOption.maxCrossAxisExtent) +
                    layoutOption.mainAxisSpacing),
        );
        const nodeWidth = Math.floor(nodesBoxWidth / columnNum - layoutOption.mainAxisSpacing);
        const nodeHeight = Math.floor(nodeWidth / layoutOption.childAspectRatio);
        const rowHeight = nodeHeight + layoutOption.crossAxisSpacing;
        const rowNum = Math.ceil(this.data.length / columnNum);

        const viewportRect = this.viewportRect;

        this.contentHeight = rowNum * rowHeight + 2 * layoutOption.padding;

        const viewportRowNum = Math.floor((this.height * 2) / rowHeight);
        const viewportRowsHeight = rowHeight * viewportRowNum;
        viewportRect.width = this.width;
        viewportRect.height = Math.min(
            viewportRowsHeight + 2 * layoutOption.padding,
            this.contentHeight,
        );

        // 更新viewportRect.y
        const scrollTopFirstRowIndex = Math.floor(
            Math.max(0, this.scrollTop - layoutOption.padding) / rowHeight,
        );
        const scrollTopFirstDataIndex = scrollTopFirstRowIndex * columnNum;
        const scrollTopFirstNodeY =
            Math.floor(scrollTopFirstDataIndex / columnNum) *
            (nodeHeight + layoutOption.crossAxisSpacing);
        const maxViewportY = this.contentHeight - viewportRect.height;

        if (this.scrollTop !== this._preScrollTop) {
            if (this.scrollTop > this._preScrollTop) {
                // scroll down
                if (this.scrollTop + this.height >= viewportRect.y + viewportRowsHeight) {
                    viewportRect.y = Math.min(scrollTopFirstNodeY, maxViewportY);
                }
            } else {
                // scroll up
                if (viewportRect.y >= this.scrollTop - layoutOption.padding) {
                    viewportRect.y = Math.max(
                        0,
                        this.scrollTop - viewportRowsHeight + this.height - layoutOption.padding,
                    );
                }
            }
            this._preScrollTop = this.scrollTop;
        } else {
            // 修复滚动条在底部时，屏幕由小变大，导致的底部一片白屏
            viewportRect.y = Math.min(viewportRect.y, maxViewportY);
        }

        const firstRowIndex = Math.floor(viewportRect.y / rowHeight);
        const lastRowIndex = firstRowIndex + viewportRowNum;
        this.viewportFirstDataIndex = firstRowIndex * columnNum;
        this.viewportLastDataIndex = Math.min(lastRowIndex * columnNum, this.data.length);

        const nodePositions = this.nodesLayout.nodePositions;
        for (
            let index = 0;
            index < this.viewportLastDataIndex - this.viewportFirstDataIndex;
            index++
        ) {
            const x =
                (index % columnNum) * (nodeWidth + layoutOption.mainAxisSpacing) +
                layoutOption.padding;
            const y =
                Math.floor(index / columnNum) * (nodeHeight + layoutOption.crossAxisSpacing) +
                layoutOption.padding;
            if (!nodePositions[index]) {
                nodePositions[index] = {
                    x,
                    y,
                };
            } else {
                nodePositions[index].x = x;
                nodePositions[index].y = y;
            }
        }

        this._caculateIndicatorsLayout({ width: nodeWidth - 2, height: nodeHeight - 2 });

        this.nodesLayout.nodeWidth = nodeWidth;
        this.nodesLayout.nodeHeight = nodeHeight;

        if (this._debug_show) {
            console.log(
                'ParamonCore relayout',
                'width',
                this.width,
                'height',
                this.height,
                'scale',
                this.scale,
            );
            console.log(
                'ParamonCore relayout',
                'nodeWidth',
                nodeWidth,
                'nodeHeight',
                nodeHeight,
                'rowHeight',
                rowHeight,
                'columnNum',
                columnNum,
                'rowNum',
                rowNum,
                'contentHeight',
                this.contentHeight,
            );
            console.log(
                'ParamonCore relayout',
                'viewportRect.width',
                viewportRect.width,
                'viewportRect.height',
                viewportRect.height,
                'viewportRect.y',
                viewportRect.y,
            );
            console.log(
                'ParamonCore relayout',
                'scrollTopFirstRowIndex',
                scrollTopFirstRowIndex,
                'scrollTopFirstDataIndex',
                scrollTopFirstDataIndex,
                'viewportFirstRowIndex',
                firstRowIndex,
                'viewportLastRowIndex',
                lastRowIndex,
                'viewportFirstDataIndex',
                this.viewportFirstDataIndex,
                'viewportLastDataIndex',
                this.viewportLastDataIndex,
            );
            console.log('ParamonCore relayout cost', performance.now() - startTime, 'ms');
        }
    }

    protected _caculateIndicatorsLayout(size: { width: number; height: number }) {
        this.indicatorConfigEntries.forEach(([indicatorKey, { boxRect }]) => {
            switch (indicatorKey as IIndicatorKey) {
                case 'cpuUsed':
                    boxRect.x = size.width * 0.2;
                    boxRect.width = size.width * 0.6;
                    boxRect.height = size.height * 0.75;
                    break;
                case 'memoryUsed':
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.75;
                    break;
                case 'memoryRead':
                    boxRect.x = size.width * 0.1;
                    boxRect.y = size.height * 0.375;
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.375;
                    break;
                case 'memoryWrite':
                    boxRect.x = size.width * 0.1;
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.375;
                    break;
                case 'diskRead':
                    boxRect.y = size.height * 0.75;
                    boxRect.width = size.width * 0.4;
                    boxRect.height = size.height * 0.125;
                    break;
                case 'diskWrite':
                    boxRect.x = size.width * 0.4;
                    boxRect.y = size.height * 0.75;
                    boxRect.width = size.width * 0.4;
                    boxRect.height = size.height * 0.125;
                    break;
                case 'nfsRead':
                    boxRect.y = size.height * 0.875;
                    boxRect.width = size.width * 0.4;
                    boxRect.height = size.height * 0.125;
                    break;
                case 'nfsWrite':
                    boxRect.x = size.width * 0.4;
                    boxRect.y = size.height * 0.875;
                    boxRect.width = size.width * 0.4;
                    boxRect.height = size.height * 0.125;
                    break;
                case 'netSend':
                    boxRect.x = size.width * 0.8;
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.5;
                    break;
                case 'netReceive':
                    boxRect.x = size.width * 0.8;
                    boxRect.y = size.height * 0.5;
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.5;
                    break;
                case 'ibSend':
                    boxRect.x = size.width * 0.9;
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.5;
                    break;
                case 'ibReceive':
                    boxRect.x = size.width * 0.9;
                    boxRect.y = size.height * 0.5;
                    boxRect.width = size.width * 0.1;
                    boxRect.height = size.height * 0.5;
                    break;
            }
        });
    }

    protected _render() {
        throw new Error('need implement _render method');
    }
}
