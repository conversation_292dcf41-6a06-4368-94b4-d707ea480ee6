/// 100MB 常量
export const mb100 = 100 * 1024 * 1024;

/// 120MB 常量
export const mb120 = 120 * 1024 * 1024;

/// 1500MB 常量
export const mb1500 = 1500 * 1024 * 1024;

/// 100GB 常量
export const gb100 = mb100 * 1024;

export const scrollBarWidth = 10;

export const scrollBarColor = 'rgb(178, 178, 178)';

export const bgColor = 'rgb(0, 0, 0)';

export const borderColor = 'rgb(158, 158, 158)';

export const errorColor = 'rgb(255, 0, 0)';

export const gflopsColor = 'rgb(0, 255, 0)';

export const cpuColor = 'rgb(0, 128, 0)';

export const swapColor = 'rgb(255, 0, 0)';

export const memoryColor = 'rgb(128, 128, 0)';

export const memoryReadColor = 'rgb(170, 170, 0)';

export const diskReadColor = 'rgb(255, 128, 0)';

export const diskWriteColor = 'rgb(0, 128, 255)';

export const netReceiveColor = 'rgb(0, 255, 255)';

export const netSendColor = 'rgb(255, 0, 255)';
