import { createAsyncClusterEnum, createAsyncZonesEnum } from '@/utils/columns_enum';
import type { ActionType, ProFormColumnsType } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import type { BindPasPlatformRef, DetailDrawerRef } from './types';
import { getIntl, getLocale } from '@umijs/max';
import { Button, message } from 'antd';
import {
    getAdminMonitorNodeStatusNameList,
    postAdminZoneClusterCommandNodeOff,
    postAdminZoneClusterCommandNodeOn,
} from '@/services/api/admin_monitors';
import { createZoneNameValueEnum } from '@/valueEnum/zone_name';
import { isNull } from 'lodash-es';

const intl = getIntl(getLocale());

const zoneNameValueEnum = await createZoneNameValueEnum();

export const getSearchColumns = (): ProFormColumnsType<API.IApiReqGetAdminMonitorNodePage>[] => {
    return [
        {
            title: intl.formatMessage({ id: 'menu.resource.zone' }),
            dataIndex: 'zoneCode',
            fieldProps: {
                showSearch: true,
            },
            request: createAsyncZonesEnum,
        },
        {
            title: intl.formatMessage({ id: 'menu.resource.cluster' }),
            dataIndex: 'clusterCode',
            dependencies: ['zoneCode'],
            fieldProps: {
                showSearch: true,
            },
            request: async ({ zoneCode }) => createAsyncClusterEnum(zoneCode),
        },
        {
            title: intl.formatMessage({ id: 'menu.resource.node' }),
            dataIndex: 'nodeNameSearch',
        },
        {
            title: intl.formatMessage({ id: 'pages.resourceQueue.terrace' }),
            dataIndex: 'platformSearch',
        },
        {
            title: intl.formatMessage({ id: 'component.state' }),
            dataIndex: 'state',
            width: 120,
            request: async () => {
                const res = await getAdminMonitorNodeStatusNameList();
                return res.map((item) => ({
                    label: item,
                    value: item,
                }));
            },
        },
    ];
};

export function getColumns(
    tableActionsRef: React.MutableRefObject<ActionType | undefined>,
    modalActions: React.MutableRefObject<{
        detailDrawerRef: React.MutableRefObject<DetailDrawerRef>;
        bindPasPlatformRef: React.MutableRefObject<BindPasPlatformRef>;
    }>,
): ProColumns<API.IAdminMonitorNode>[] {
    const columns: ProColumns<API.IAdminMonitorNode>[] = [
        {
            title: intl.formatMessage({ id: 'menu.resource.zone' }),
            renderText: (zone, row) => {
                const zoneName = zoneNameValueEnum?.find((item) => item.value === row.zone);
                return zoneName ? zoneName.label : zone;
            },
        },
        {
            title: intl.formatMessage({ id: 'menu.resource.node' }),
            dataIndex: 'name',
        },
        {
            title: intl.formatMessage({ id: 'component.state' }),
            dataIndex: 'state',
        },
        {
            title: intl.formatMessage({ id: 'enum.userSecurityLevel.core' }),
            align: 'right',
            dataIndex: 'cores',
        },
        {
            title: `${intl.formatMessage({ id: 'pages.storage.storage' })}(GB)`,
            dataIndex: 'availableMem',
            align: 'right',
            render: (text, row) => (
                <span>
                    {(row.availableMem / (1024 * 1024 * 1024)).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                    })}
                </span>
            ),
        },
        {
            title: intl.formatMessage({ id: 'pages.resourceQueue.terrace' }),
            dataIndex: 'platform',
        },
        {
            title: intl.formatMessage({ id: 'menu.resource.queue' }),
            dataIndex: 'queue',
        },
        {
            title: 'IB Switch',
            dataIndex: 'ibsw',
        },
        {
            title: `${intl.formatMessage({ id: 'pages.log.userId' })}/${intl.formatMessage({
                id: 'pages.log.jobId',
            })}`,
            dataIndex: 'jobids',
            render: (text, row) => (
                <a
                    onClick={() => {
                        modalActions.current.detailDrawerRef.current.setVisible?.(true, {
                            rowJobId: row.jobids[0],
                            zone: row.zone,
                            cluster: row.cluster,
                        });
                    }}
                >
                    {row.jobids.join('/')}
                </a>
            ),
        },
        {
            title: 'Pas Platform',
            dataIndex: 'pasPlatforms',
            hideInTable: !globalConfig.USE_PAS_PLATFORM,
            renderText: (pasPlatforms) => (isNull(pasPlatforms) ? '-' : pasPlatforms.join(',')),
        },
        {
            title: intl.formatMessage({ id: 'component.operate' }),
            valueType: 'option',
            render: (_, row) => {
                const actions = [
                    <Button
                        type="link"
                        onClick={async () => {
                            await postAdminZoneClusterCommandNodeOff({
                                clusterCode: row.cluster,
                                nodeName: row.name,
                                zoneCode: row.zone,
                            })
                                .then(() => {
                                    message.success(
                                        intl.formatMessage({ id: 'pages.resourceNode.offSuccess' }),
                                    );
                                })
                                .catch(() => {
                                    message.error(
                                        intl.formatMessage({ id: 'pages.resourceNode.offError' }),
                                    );
                                });
                        }}
                    >
                        {intl.formatMessage({ id: 'pages.resourceNode.off' })}
                    </Button>,
                ];
                if (!['FAIL', 'UNKNOWN'].includes(row.state)) {
                    // TODO 这两个状态可以执行开机 (具体逻辑hmj也不知道)
                    actions.push(
                        <Button
                            type="link"
                            onClick={async () => {
                                await postAdminZoneClusterCommandNodeOn({
                                    clusterCode: row.cluster,
                                    nodeName: row.name,
                                    zoneCode: row.zone,
                                })
                                    .then(() => {
                                        message.success(
                                            intl.formatMessage({
                                                id: 'pages.resourceNode.onSuccess',
                                            }),
                                        );
                                    })
                                    .catch(() => {
                                        message.error(
                                            intl.formatMessage({
                                                id: 'pages.resourceNode.onError',
                                            }),
                                        );
                                    });
                            }}
                        >
                            {intl.formatMessage({ id: 'pages.resourceNode.on' })}
                        </Button>,
                    );
                }

                if (row.cluster === 'LOCAL' && globalConfig.USE_PAS_PLATFORM) {
                    actions.push(
                        <a
                            onClick={() => {
                                modalActions.current.bindPasPlatformRef.current.changeModal?.(row);
                            }}
                        >
                            {intl.formatMessage({ id: 'pages.resourceLicense.bindPasPlatform' })}
                        </a>,
                    );
                }
                return actions;
            },
        },
    ];
    return columns;
}
