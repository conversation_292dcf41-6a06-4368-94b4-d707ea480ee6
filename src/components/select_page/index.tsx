import { Select, type SelectProps } from 'antd';
import { useEffect, useState } from 'react';

export interface SelectPageProps extends SelectProps {
    request?: (
        params?: API.IApiReqPageParams,
    ) => Promise<{ options: Record<string, any>[]; total: number }>;
}

/**
 * 
 * 示例
 * ```tsx
 * renderFormItem: () => <SelectPage request={async(params?:API.IApiReqPageParams)=>{
                const {data:users,total} = await getAdminUser({
                    userScope: 'ALL',
                    ...params,
                })
                return {
                    options:users.map(u=>({label: u.uid,value: u.uid})),
                    total,
                }
            }} />,
```
 */
export const SelectPage: React.FC<SelectPageProps> = ({ request, ...props }) => {
    const [page, setPage] = useState(1);
    const [options, setOptions] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [total, setTotal] = useState(0);

    const handlePopupScroll = async (e: React.UIEvent<HTMLDivElement>) => {
        const { currentTarget } = e;
        if (
            !loading &&
            currentTarget.scrollTop + currentTarget.clientHeight === currentTarget.scrollHeight
        ) {
            setLoading(true);
            if (request && options.length < total) {
                const { options: _options, total } = await request({ page });
                setOptions([...options, ..._options]);
                setPage(page + 1);
                setTotal(total);
            }
            setLoading(false);
        }
    };

    useEffect(() => {
        if (request) {
            request({ page: 1 }).then((r) => {
                setOptions([...r.options]);
                setPage(2);
                setTotal(r.total);
            });
        }
    }, [request]);

    return (
        <Select
            mode="tags"
            maxTagCount={0}
            style={{ minWidth: '100px' }}
            options={options}
            onPopupScroll={handlePopupScroll}
            loading={loading}
            {...props}
        />
    );
};
