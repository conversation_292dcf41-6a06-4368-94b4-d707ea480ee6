import type { ProColumns } from '@ant-design/pro-components';

import { getIntl, getLocale } from '@umijs/max';

const intl = getIntl(getLocale());

export const getColumns = (): ProColumns<API.IApiResAdminBillingResourcePriceModel>[] => {
    return [
        {
            title: '序列号',
            dataIndex: 'id',
            align: 'right',
        },
        {
            title: 'SKU',
            dataIndex: 'sku',
            key: 'sku',
            sorter: true,
        },
        // {
        //     title: intl.formatMessage({ id: 'pages.productList.resourceAbbreviation' }),
        //     dataIndex: 'name',
        //     key: 'name',
        //     sorter: true,
        // },
        {
            title: intl.formatMessage({ id: 'pages.productList.resourceType' }),
            dataIndex: 'type',
            key: 'type',
            sorter: true,
            align: 'center',
            valueEnum: {
                COMPUTING: {
                    text: intl.formatMessage({ id: 'pages.productList.resourceType.computing' }),
                },
                STORAGE: {
                    text: intl.formatMessage({ id: 'pages.productList.resourceType.storage' }),
                },
                // NETWORK: {
                //     text: intl.formatMessage({ id: 'pages.productList.resourceType.network' }),
                // },
                // GRAPHICAL: {
                //     text: intl.formatMessage({ id: 'pages.productList.resourceType.graphical' }),
                // },
                // PLATFORM: {
                //     text: intl.formatMessage({ id: 'pages.productList.resourceType.platform' }),
                // },
                // SOFTWARE: {
                //     text: intl.formatMessage({ id: 'pages.productList.resourceType.software' }),
                // },
                // EDGE: {
                //     text: intl.formatMessage({ id: 'pages.productList.resourceType.edge' }),
                // },
                // TECHNICAL_SERVICE: {
                //     text: intl.formatMessage({
                //         id: 'pages.productList.resourceType.technicalService',
                //     }),
                // },
                // LICENSE: {
                //     text: intl.formatMessage({
                //         id: 'pages.productList.resourceType.license',
                //     }),
                // },
                // SWITCH: {
                //     text: intl.formatMessage({
                //         id: 'pages.productList.resourceType.switch',
                //     }),
                // },
                // SOFTWARE_RENTAL: {
                //     text: intl.formatMessage({
                //         id: 'pages.productList.resourceType.softwareRental',
                //     }),
                // },
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.productList.billingMode' }),
            dataIndex: 'expenseType',
            key: 'expenseType',
            sorter: true,
            align: 'center',
            valueEnum: {
                YEAR: {
                    text: intl.formatMessage({ id: 'pages.billing.billingTypeYear' }),
                },
                MONTH: {
                    text: intl.formatMessage({ id: 'pages.billing.billingTypeMonth' }),
                },
                NEED: {
                    text: intl.formatMessage({ id: 'pages.billing.billingTypeOnDemand' }),
                },
                // FOREVER_LICENSE: {
                //     text: intl.formatMessage({ id: 'pages.billing.billingTypeNeverExpired' }),
                // },
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.productList.standardUnitPrice' }),
            dataIndex: 'price',
            key: 'price',
            valueType: 'money',
            align: 'right',
            sorter: true,
            renderText: (value) => value / 100,
        },
        {
            title: intl.formatMessage({ id: 'pages.productList.billingUnit' }),
            dataIndex: 'unit',
            key: 'unit',
            sorter: true,
            valueEnum: {
                CORE_HOURS: {
                    text: intl.formatMessage({ id: 'pages.billing.cpuTime' }),
                },
                CARD_HOURS: {
                    text: intl.formatMessage({ id: 'pages.billing.gpuTime' }),
                },
                GIGABYTE: {
                    text: 'GB',
                },
                TERABYTE: {
                    text: intl.formatMessage({ id: 'pages.productList.billingUnitTB' }),
                },
                PETABYTE: {
                    text: intl.formatMessage({ id: 'pages.productList.billingUnitPB' }),
                },
                // MONTH: {
                //     text: intl.formatMessage({ id: 'pages.billing.month' }),
                // },
                // YEAR: {
                //     text: intl.formatMessage({ id: 'component.tips.year' }),
                // },
                // KILOMETER: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitKilometer' }),
                // },
                // CONCURRENT_SESSIONS: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitSessions' }),
                // },
                // NODE: {
                //     text: intl.formatMessage({ id: 'pages.job.nodesName' }),
                // },
                // MODULE: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitModule' }),
                // },
                // APPLICATION_SOFTWARE: {
                //     text: intl.formatMessage({ id: 'pages.billing.softwares' }),
                // },
                // BANDWIDTH: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitBandWidth' }),
                // },
                // UNIT: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitUnit' }),
                // },
                // PERSON_DAY: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitPersonDay' }),
                // },
                // PERSON_YEAR: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitPersonYear' }),
                // },
                // CONCURRENT_USERS: {
                //     text: intl.formatMessage({ id: 'pages.productList.billingUnitUser' }),
                // },
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.productList.productStatus' }),
            dataIndex: 'saleStatus',
            key: 'saleStatus',
            sorter: true,
            align: 'center',
            valueEnum: {
                ON: {
                    text: intl.formatMessage({ id: 'pages.productList.productSaleStatusOn' }),
                    status: 'Success',
                },
                WAIT: {
                    text: intl.formatMessage({ id: 'pages.productList.productSaleStatusWait' }),
                    status: 'Warning',
                },
                OFF: {
                    text: intl.formatMessage({ id: 'pages.productList.productSaleStatusOff' }),
                    status: 'Error',
                },
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.projectManage.description' }),
            dataIndex: 'remark',
            key: 'remark',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.productList.billingQueue' }),
            dataIndex: 'queue',
            key: 'queueId',
            renderText: (_, record) =>
                record.queue ? `${record.zone}/${record.cluster}/${record.queue}` : '-',
        },
    ];
};
