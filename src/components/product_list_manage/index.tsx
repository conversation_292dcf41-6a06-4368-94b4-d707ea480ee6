import { Button, Modal } from 'antd';
import { useRef } from 'react';

import {
    deleteAdminBillingResourcePrice,
    getAdminBillingResourcePriceExport,
    getAdminBillingResourcePriceList,
} from '@/services/api/admin_billing_resource_price';
import { getColumns } from './get_columns';

import { useIntl } from '@umijs/max';
import SearchTablePage from '../search_table_page';

import type { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { ModalActions } from './type';
import { DeleteOutlined, EditOutlined, ExportOutlined, ProductOutlined } from '@ant-design/icons';
import { CreateProductModal } from './create_product_modal';
import {
    createBillingResourcePriceTypeEnum,
    createBillingTypeEnum,
    createBillingSaleStatusEnum,
} from '@/utils/columns_enum';
import { downLoadBlob } from '../billing_center/helper';

export default function ProductListManage() {
    const intl = useIntl();
    const searchFromRef = useRef<ProFormInstance<API.IApiReqDataGetResourcePriceList>>();
    const tableActionsRef = useRef<ActionType>();
    const modalActions = useRef<ModalActions>({
        createProduct: {},
    });

    return (
        <>
            <SearchTablePage<
                API.IApiResAdminBillingResourcePriceModel,
                API.IApiReqDataGetResourcePriceList
            >
                rightMenu
                searchProps={{
                    layout: 'inline',
                    formRef: searchFromRef,
                    columns: [
                        // {
                        //     title: intl.formatMessage({
                        //         id: 'pages.productList.resourceAbbreviation',
                        //     }),
                        //     dataIndex: 'nameSearch',
                        // },
                        {
                            title: intl.formatMessage({ id: 'pages.productList.resourceType' }),
                            dataIndex: 'type',
                            valueType: 'select',
                            request: () => createBillingResourcePriceTypeEnum(),
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.productList.billingMode' }),
                            dataIndex: 'expenseType',
                            valueType: 'select',
                            request: () => createBillingTypeEnum(),
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.productList.productStatus' }),
                            dataIndex: 'saleStatus',
                            valueType: 'select',
                            request: () => createBillingSaleStatusEnum(),
                        },
                    ],
                    onFinish: async () => {
                        tableActionsRef.current?.reloadAndRest?.();
                    },
                    submitter: {
                        render: (_, dom) => {
                            return [
                                ...dom,
                                <Button
                                    type="primary"
                                    icon={<ExportOutlined />}
                                    onClick={async () => {
                                        const formValue =
                                            searchFromRef.current?.getFieldsFormatValue?.();
                                        const res = await getAdminBillingResourcePriceExport({
                                            ...formValue,
                                        });
                                        downLoadBlob(res.headers['content-disposition'], res.data);
                                    }}
                                >
                                    {intl.formatMessage({ id: 'component.table.export' })}
                                </Button>,
                            ];
                        },
                    },
                }}
                tableProps={{
                    toolBarRender: () => [
                        <Button
                            type="primary"
                            icon={<ProductOutlined />}
                            onClick={() => {
                                modalActions.current?.createProduct?.changeModal?.();
                            }}
                        >
                            {intl.formatMessage({ id: 'pages.productList.create' })}
                        </Button>,
                    ],
                    actionRef: tableActionsRef,
                    cellActions: (record) => [
                        {
                            key: 'edit',
                            icon: <EditOutlined />,
                            label: intl.formatMessage({ id: 'component.operate.edit' }),
                            onClick: (row) => {
                                // 将 price 从分转换元，传给 modal
                                const rowInFen = {
                                    ...row,
                                    price: row.price / 100,
                                };
                                modalActions.current?.createProduct?.changeModal?.(rowInFen);
                            },
                        },
                        {
                            key: 'delete',
                            icon: <DeleteOutlined />,
                            label: intl.formatMessage({ id: 'component.operate.delete' }),
                            onClick: (row) => {
                                Modal.confirm({
                                    title: intl.formatMessage({ id: 'component.operate.delete' }),
                                    content: intl.formatMessage({
                                        id: 'component.areYouSureYouWantToDeleteIt',
                                    }),
                                    onOk: async () => {
                                        await deleteAdminBillingResourcePrice(row.id);
                                        tableActionsRef.current?.reloadAndRest?.();
                                    },
                                });
                            },
                        },
                    ],
                    request: async (params, sorter) => {
                        console.log('sorter:', sorter);
                        console.log('sorter.field:', sorter.field);
                        console.log('sorter.order:', sorter.order);
                        let orderBy = 'createdDate';
                        let order: 'ASC' | 'DESC' = 'DESC';
                        if (typeof sorter === 'object') {
                            // 查找排序字段和顺序
                            Object.keys(sorter).forEach((key) => {
                                if (sorter[key] && ['ascend', 'descend'].includes(sorter[key])) {
                                    orderBy = key;
                                    order = sorter[key] === 'ascend' ? 'ASC' : 'DESC';
                                }
                            });
                        }
                        const { current, pageSize } = params;
                        const formValue = searchFromRef.current?.getFieldsFormatValue?.();
                        const res = await getAdminBillingResourcePriceList({
                            page: current,
                            size: pageSize,
                            orderBy,
                            order,
                            ...formValue,
                        });
                        return {
                            success: true,
                            data: res.data,
                            current: res.page,
                            pageSize: res.size,
                            total: res.total,
                        };
                    },
                    columns: getColumns(),
                }}
            />
            <CreateProductModal modalActions={modalActions} tableActions={tableActionsRef} />
        </>
    );
}
