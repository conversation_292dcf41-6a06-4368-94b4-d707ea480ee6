import { MutableRefObject, useRef, useState } from 'react';

import type { ModalActions } from './type';
import { useIntl } from '@umijs/max';
import { ActionType, BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import {
    createBillingQueueEnum,
    createBillingResourcePriceTypeEnum,
    createBillingUnitTypeEnum,
} from '@/utils/columns_enum';
import {
    postAdminBillingResourcePrice,
    putAdminBillingResourcePrice,
} from '@/services/api/admin_billing_resource_price';
import { message } from 'antd';

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
    },
};

export function CreateProductModal({
    modalActions,
    tableActions,
}: {
    modalActions: MutableRefObject<ModalActions | undefined>;
    tableActions: MutableRefObject<ActionType | undefined>;
}) {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [info, setInfo] = useState<API.IApiResAdminBillingResourcePriceModel | undefined>();
    const [isUnitManuallySet, setIsUnitManuallySet] = useState(false); // 新增状态变量
    const [isNEEDManuallySet, setIsNEEDManuallySet] = useState(false); // 新增状态变量

    const formRef = useRef<ProFormInstance>();

    if (modalActions.current) {
        Object.assign(modalActions.current.createProduct, {
            changeModal: async (product?: API.IApiResAdminBillingResourcePriceModel) => {
                setInfo(product);
                setIsUnitManuallySet(false); // 打开模态框时重置标志
                setIsNEEDManuallySet(false); // 打开模态框时重置标志
                await setOpen(true);
                await formRef.current?.setFieldsValue({
                    ...product,
                });
                if (product && product.zone && product.cluster && product.queue) {
                    await formRef.current?.setFieldsValue({
                        queueId: `${product?.zone}/${product?.cluster}/${product?.queue}`,
                    });
                }
            },
        });
    }

    const onFormSubmit = async () => {
        const data = await formRef.current?.getFieldsFormatValue?.();
        if (info?.id) {
            await putAdminBillingResourcePrice(data);
            message.success(intl.formatMessage({ id: 'pages.productList.successfullyEdit' }));
        } else {
            await postAdminBillingResourcePrice(data);
            message.success(
                intl.formatMessage({ id: 'pages.permissionMenuManage.successfullyCreated' }),
            );
        }
        await tableActions.current?.reloadAndRest?.();
        setOpen(false);
    };

    if (!open) {
        return null;
    }

    return (
        <>
            <BetaSchemaForm
                {...formItemLayout}
                title={
                    info?.id
                        ? intl.formatMessage({ id: 'component.operate.edit' })
                        : intl.formatMessage({ id: 'component.operate.create' })
                }
                open={open}
                formRef={formRef}
                layoutType="ModalForm"
                layout="horizontal"
                onFinish={async () => {
                    return onFormSubmit();
                }}
                modalProps={{
                    width: 560,
                    onCancel: async () => {
                        await formRef.current?.resetFields();
                        await setOpen(false);
                    },
                }}
                columns={[
                    {
                        dataIndex: 'id',
                        formItemProps: {
                            hidden: true,
                        },
                    },
                    {
                        title: 'SKU',
                        dataIndex: 'sku',
                        formItemProps: {
                            rules: [{ required: true }],
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.productList.resourceType' }),
                        dataIndex: 'type',
                        key: 'type',
                        valueType: 'select',
                        formItemProps: {
                            rules: [{ required: true }],
                        },
                        fieldProps: {
                            onChange: async () => {
                                formRef.current.setFieldValue('expenseType', undefined);
                            },
                        },
                        request: () => createBillingResourcePriceTypeEnum(),
                    },
                    // {
                    //     title: intl.formatMessage({ id: 'pages.productList.resourceAbbreviation' }),
                    //     dataIndex: 'name',
                    //     key: 'name',
                    // },
                    {
                        valueType: 'dependency',
                        name: ['type'],
                        columns: ({ type }) => {
                            if (type === 'COMPUTING') {
                                return [
                                    {
                                        title: intl.formatMessage({
                                            id: 'pages.productList.billingresourceType',
                                        }),
                                        dataIndex: 'queueType',
                                        key: 'queueType',
                                        valueType: 'radio',
                                        formItemProps: {
                                            rules: [{ required: true }],
                                        },
                                        fieldProps: {
                                            options: [
                                                {
                                                    value: 'GPU',
                                                    label: 'GPU',
                                                },
                                                {
                                                    value: 'CPU',
                                                    label: 'CPU',
                                                },
                                            ],
                                        },
                                    },
                                ];
                            } else {
                                return [];
                            }
                        },
                    },
                    {
                        valueType: 'dependency',
                        name: ['type'],
                        columns: ({ type }) => {
                            if (type === 'COMPUTING') {
                                return [
                                    {
                                        title: intl.formatMessage({
                                            id: 'pages.productList.billingMode',
                                        }),
                                        dataIndex: 'expenseType',
                                        key: 'expenseType',
                                        valueType: 'radio',
                                        formItemProps: {
                                            rules: [{ required: true }],
                                        },
                                        fieldProps: {
                                            options: [
                                                {
                                                    value: 'YEAR',
                                                    label: intl.formatMessage({
                                                        id: 'pages.billing.billingTypeYear',
                                                    }),
                                                },
                                                {
                                                    value: 'MONTH',
                                                    label: intl.formatMessage({
                                                        id: 'pages.billing.billingTypeMonth',
                                                    }),
                                                },
                                                {
                                                    value: 'NEED',
                                                    label: intl.formatMessage({
                                                        id: 'pages.billing.billingTypeOnDemand',
                                                    }),
                                                },
                                            ],
                                            // 当用户手动选择 NEED 时，标记为手动设置, 以便区分编辑时的自动设置
                                            onChange: (e) => {
                                                if (e.target.value === 'NEED') {
                                                    setIsNEEDManuallySet(true);
                                                }
                                            },
                                        },
                                    },
                                ];
                            }
                            if (type === 'STORAGE') {
                                return [
                                    {
                                        title: intl.formatMessage({
                                            id: 'pages.productList.billingMode',
                                        }),
                                        dataIndex: 'expenseType',
                                        key: 'expenseType',
                                        valueType: 'radio',
                                        formItemProps: {
                                            rules: [{ required: true }],
                                        },
                                        fieldProps: {
                                            options: [
                                                {
                                                    value: 'YEAR',
                                                    label: intl.formatMessage({
                                                        id: 'pages.billing.billingTypeYear',
                                                    }),
                                                },
                                                {
                                                    value: 'MONTH',
                                                    label: intl.formatMessage({
                                                        id: 'pages.billing.billingTypeMonth',
                                                    }),
                                                },
                                            ],
                                            // 当用户手动选择 NEED 时，标记为手动设置, 以便区分编辑时的自动设置
                                            onChange: (e) => {
                                                if (e.target.value === 'NEED') {
                                                    setIsNEEDManuallySet(true);
                                                }
                                            },
                                        },
                                    },
                                ];
                            }
                            return [];
                        },
                    },
                    {
                        valueType: 'dependency',
                        name: ['type', 'expenseType', 'queueType'],
                        columns: ({ type, expenseType, queueType }) => {
                            if (type === 'COMPUTING' && expenseType === 'NEED' && queueType) {
                                return [
                                    {
                                        title: intl.formatMessage({
                                            id: 'pages.productList.billingQueue',
                                        }),
                                        dataIndex: 'queueId',
                                        dependencies: ['queueType'],
                                        valueType: 'select',
                                        fieldProps: {
                                            showSearch: true,
                                        },
                                        formItemProps: {
                                            rules: [{ required: true }],
                                        },
                                        request: () => createBillingQueueEnum(queueType),
                                        transform: (value) => {
                                            const [zone, cluster, queue] = value.split('/');
                                            return { zone, cluster, queue };
                                        },
                                    },
                                ];
                            } else {
                                return [];
                            }
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.productList.standardUnitPrice' }),
                        dataIndex: 'price',
                        key: 'price',
                        valueType: 'money',
                        width: '100%',
                        formItemProps: {
                            rules: [{ required: true }],
                        },
                        transform: (value) => {
                            return {
                                price: value * 100,
                            };
                        },
                        fieldProps: {
                            precision: 4,
                            min: 0,
                        },
                    },
                    {
                        valueType: 'dependency',
                        name: ['type', 'expenseType'],
                        columns: ({ type, expenseType }) => {
                            // if (expenseType === 'YEAR') {
                            //     formRef.current?.setFieldsValue({
                            //         unit: 'YEAR',
                            //     });
                            //     // 标记为自动更新，方便再选择expenseType 为 'NEED'时，清空unit
                            //     setIsUnitManuallySet(false);
                            // } else if (expenseType === 'MONTH') {
                            //     formRef.current?.setFieldsValue({
                            //         unit: 'MONTH',
                            //     });
                            //     // 标记为自动更新，方便再选择expenseType 为 'NEED'时，清空unit
                            //     setIsUnitManuallySet(false);
                            // } else if (
                            //     expenseType === 'NEED' &&
                            //     !isUnitManuallySet &&
                            //     isNEEDManuallySet
                            // ) {
                            //     formRef.current?.setFieldsValue({
                            //         unit: '',
                            //     });
                            // }
                            return [
                                {
                                    title: intl.formatMessage({
                                        id: 'pages.productList.billingUnit',
                                    }),
                                    dataIndex: 'unit',
                                    dependencies: ['type', 'expenseType'],
                                    key: 'unit',
                                    valueType: 'select',
                                    formItemProps: {
                                        rules: [{ required: true }],
                                        // 当用户手动选择 unit 时，标记为手动设置
                                        onSelect: () => {
                                            setIsUnitManuallySet(true);
                                        },
                                    },
                                    request: () => createBillingUnitTypeEnum(type, expenseType),
                                },
                            ];
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.productList.productStatus' }),
                        dataIndex: 'saleStatus',
                        valueType: 'radio',
                        formItemProps: {
                            rules: [
                                {
                                    required: true,
                                    message: '请选择产品状态',
                                },
                            ],
                        },
                        fieldProps: {
                            options: [
                                {
                                    value: 'ON',
                                    label: intl.formatMessage({
                                        id: 'pages.productList.productSaleStatusOn',
                                    }),
                                },
                                {
                                    value: 'WAIT',
                                    label: intl.formatMessage({
                                        id: 'pages.productList.productSaleStatusWait',
                                    }),
                                },
                                {
                                    value: 'OFF',
                                    label: intl.formatMessage({
                                        id: 'pages.productList.productSaleStatusOff',
                                    }),
                                },
                            ],
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.projectManage.description' }),
                        dataIndex: 'remark',
                        valueType: 'textarea',
                    },
                ]}
            />
        </>
    );
}
