import {
    deleteAdminAccountAssign,
    getAdminAccountProjectAssignable,
    getAdminAccountProjectAssignableList,
} from '@/services/api';
import { BetaSchemaForm, type ProFormInstance } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, message, Modal, Tag } from 'antd';
import { useMemo, useRef, useState } from 'react';
import SearchTablePage from '../search_table_page';

export function AssignToProjectTabPane({
    sshAccountId,
    onOk,
}: {
    sshAccountId: number;
    onOk: (data?: Record<string, number[] | string[]>) => Promise<boolean>;
}) {
    const intl = useIntl();
    const columns = useMemo(getColumns, []);
    const [visible, setVisible] = useState<boolean>(false);
    const searchFromRef = useRef<ProFormInstance<API.IReqGetAdminAccountUserAssignableList>>();
    const tableActionsRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();

    function getColumns(): ProColumns[] {
        return [
            {
                title: 'ID',
                dataIndex: 'id',
            },
            {
                title: intl.formatMessage({ id: 'pages.machineHours.project' }),
                dataIndex: 'level1',
            },
            {
                title: intl.formatMessage({ id: 'component.state' }),
                key: 'status',
                dataIndex: 'status',
                width: 120,
                hideInSearch: true,
                render: (text, record) => {
                    const color = record.status ? 'processing' : 'error';
                    const info = record.status
                        ? intl.formatMessage({ id: 'component.assigned' })
                        : intl.formatMessage({ id: 'component.disabled' });
                    return <Tag color={color}>{info}</Tag>;
                },
            },
            {
                title: intl.formatMessage({ id: 'component.operate' }),
                valueType: 'option',
                render: (_, record) => [
                    <a
                        key="del"
                        onClick={async () => {
                            await onOk({ level1s: [record.level1] });
                            await tableActionsRef.current?.reloadAndRest?.();
                            message.success(
                                intl.formatMessage({ id: 'component.successfulAssignment' }),
                            );
                        }}
                        // @ts-ignore
                        disabled={record.status}
                    >
                        {intl.formatMessage({ id: 'component.distribute' })}
                    </a>,
                    <a
                        key="del"
                        onClick={async () => {
                            await onOk({ blackLevel1s: [record.level1] });
                            await tableActionsRef.current?.reloadAndRest?.();
                            message.success(
                                intl.formatMessage({ id: 'component.disabledSuccessfully' }),
                            );
                        }}
                        // @ts-ignore
                        disabled={!record.status}
                    >
                        {intl.formatMessage({ id: 'component.disable' })}
                    </a>,
                    <a
                        key="del"
                        style={{ color: 'red' }}
                        onClick={() => {
                            Modal.confirm({
                                content: intl.formatMessage({
                                    id: 'component.areYouSureYouWantToDeleteIt',
                                }),
                                autoFocusButton: 'cancel',
                                okButtonProps: { danger: true },
                                okText: intl.formatMessage({ id: 'component.modal.ok' }),
                                cancelText: intl.formatMessage({ id: 'component.modal.cancel' }),
                                onOk: async () => {
                                    await deleteAdminAccountAssign({
                                        level1PublicScAccounts: record.id,
                                    });
                                    await onOk();
                                    await tableActionsRef.current?.reloadAndRest?.();
                                },
                            });
                        }}
                    >
                        {intl.formatMessage({ id: 'component.operate.delete' })}
                    </a>,
                ],
            },
        ];
    }

    return (
        <>
            <SearchTablePage<
                API.IResGetAdminAccountProjectAssignableList,
                API.IReqGetAdminAccountUserAssignableList
            >
                searchProps={{
                    formRef: searchFromRef,
                    columns: [
                        {
                            dataIndex: 'sshAccountId',
                            initialValue: sshAccountId,
                            formItemProps: {
                                hidden: true,
                            },
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.machineHours.project' }),
                            dataIndex: 'level1Search',
                        },
                        {
                            title: intl.formatMessage({ id: 'component.state' }),
                            dataIndex: 'status',
                            request: async () => {
                                return [
                                    {
                                        label: (
                                            <Tag color={'processing'}>
                                                {intl.formatMessage({ id: 'component.assigned' })}
                                            </Tag>
                                        ),
                                        value: 'true',
                                    },
                                    {
                                        label: (
                                            <Tag color={'error'}>
                                                {intl.formatMessage({ id: 'component.disabled' })}
                                            </Tag>
                                        ),
                                        value: 'false',
                                    },
                                ];
                            },
                        },
                    ],
                    onFinish: async () => {
                        tableActionsRef.current?.reloadAndRest?.();
                    },
                }}
                tableProps={{
                    toolBarRender: () => [
                        <Button
                            type="primary"
                            onClick={() => {
                                searchFromRef.current.resetFields();
                                setVisible(true);
                            }}
                        >
                            {intl.formatMessage({ id: 'pages.resourceQueue.assignProject' })}
                        </Button>,
                    ],
                    actionRef: tableActionsRef,
                    request: async ({ pageSize, current }) => {
                        const formValue = searchFromRef.current?.getFieldsFormatValue?.();
                        const { data, size, page, total } =
                            await getAdminAccountProjectAssignableList({
                                ...formValue,
                                size: pageSize,
                                page: current,
                            });
                        return {
                            success: true,
                            data,
                            pageSize: size,
                            current: page,
                            total,
                        };
                    },
                    columns,
                    pagination: {
                        defaultPageSize: 10,
                    },
                }}
            />
            <BetaSchemaForm
                formRef={formRef}
                title={intl.formatMessage({ id: 'component.distribute' })}
                width={450}
                open={visible}
                layoutType="ModalForm"
                layout="horizontal"
                modalProps={{
                    destroyOnClose: true,
                    onCancel: () => setVisible(false),
                }}
                onFinish={async (values) => {
                    const params = await formRef.current?.validateFields();
                    await onOk(params);
                    setVisible(false);
                    await tableActionsRef.current?.reloadAndRest?.();
                }}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'pages.machineHours.project' }),
                        dataIndex: 'level1s',
                        fieldProps: {
                            mode: 'multiple',
                            showSearch: true,
                            maxTagCount: 0,
                        },
                        request: async () => {
                            const projects = await getAdminAccountProjectAssignable({
                                sshAccountId,
                            });
                            return projects.map((p) => ({
                                label: p,
                                value: p,
                            }));
                        },
                    },
                ]}
            />
        </>
    );
}
