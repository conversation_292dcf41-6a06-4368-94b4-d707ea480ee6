import Echarts from '@/components/para_echarts';
import { CheckOutlined, CloseOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import React, { useRef } from 'react';

import type { IEchartProps } from '@/components/para_echarts';
import type { ItemType } from 'antd/es/menu/interface';
import type {
    ProFormColumnsType,
    ProFormInstance,
    ProTableProps,
    SubmitterProps,
} from '@ant-design/pro-components';

import { Dropdown, Empty, Spin } from 'antd';
import styles from './index.less';
import { isUndefined } from 'lodash-es';

export const SearchEchartTablePage = <
    TableDataType extends Record<string, any>,
    ParamsType extends Record<string, any>,
    ValueType = 'text',
>({
    tableProps = {
        headerActions: () => [],
        cellActions: () => [],
    },
    echartProps,
    searchProps,
    style,
    status = {
        chartsIsNull: false,
        loading: false,
    },
    border = true,
    rightMenu = false,
}: {
    tableProps: ProTableProps<TableDataType, Record<string, any>, ValueType> & {
        headerActions?: (row: TableDataType) => {
            label: React.ReactNode;
            key: string;
            disabled?: boolean;
            onClick?: (row: TableDataType) => void;
        }[];
        cellActions?: (row: TableDataType) => {
            label: React.ReactNode;
            key: string;
            disabled?: boolean;
            icon?: React.ReactNode;
            onClick?: (row: TableDataType) => void;
        }[];
    };
    echartProps: IEchartProps;
    searchProps: {
        formRef: React.MutableRefObject<ProFormInstance<ParamsType> | undefined>;
        columns: ProFormColumnsType<ParamsType, ValueType>[];
        onFinish: (values: ParamsType) => Promise<void>;
        submitter?: SubmitterProps<{ form?: ProFormInstance<ParamsType> | undefined }>;
    };
    style?: React.CSSProperties;
    status?: {
        chartsIsNull?: boolean;
        loading?: boolean;
    };
    border?: boolean;
    rightMenu?: boolean;
}) => {
    const intl = useIntl();
    const contextMenuRow = useRef<any>({});
    return (
        <div
            className={`${border && styles.search_echart_page_border} ${styles.search_echart_page}`}
            style={style}
        >
            <div className="search_box">
                <BetaSchemaForm
                    layout="inline"
                    layoutType="Form"
                    {...searchProps}
                    submitter={{
                        searchConfig: {
                            resetText: intl.formatMessage({ id: 'pages.monitorsJobs.reset' }),
                            submitText: intl.formatMessage({
                                id: 'pages.billing.makeEnquiries',
                            }),
                        },
                        submitButtonProps: {
                            icon: <SearchOutlined />,
                        },
                        resetButtonProps: {
                            icon: <ReloadOutlined />,
                        },
                        ...searchProps.submitter,
                    }}
                />
            </div>
            <div className="echart_box">
                <Spin spinning={status?.loading}>
                    {status.chartsIsNull ? (
                        <Empty
                            className="chart noDataAvailable"
                            description={intl.formatMessage({
                                id: 'pages.billing.noDataAvailable',
                            })}
                        />
                    ) : (
                        <Echarts
                            className="chart"
                            {...echartProps}
                            option={{
                                legend: {
                                    top: 20,
                                },
                                grid: {
                                    top: 60,
                                    left: 30,
                                    right: 50,
                                    bottom: 40,
                                    containLabel: true,
                                },
                                dataZoom: [
                                    {
                                        type: 'inside',
                                        start: 0,
                                        end: 100,
                                    },
                                ],
                                toolbox: {
                                    showTitle: false,
                                    feature: {
                                        saveAsImage: {
                                            show: globalConfig.ENABLE_EXPORT,
                                        },
                                    },
                                },
                                ...echartProps.option,
                            }}
                        />
                    )}
                </Spin>
            </div>
            <div className="table_box">
                <ProTable
                    onRow={
                        rightMenu
                            ? (record) => {
                                  return {
                                      onContextMenu: async () => {
                                          contextMenuRow.current = record;
                                      },
                                  };
                              }
                            : undefined
                    }
                    components={
                        rightMenu
                            ? {
                                  header: {
                                      row: (props: any) => {
                                          const items: ItemType[] = [
                                              {
                                                  key: '显示默认列',
                                                  label: '显示默认列',
                                              },
                                              {
                                                  key: '显示所有列',
                                                  label: '显示所有列',
                                              },
                                              {
                                                  key: '自适应列宽',
                                                  label: '自适应列宽',
                                              },
                                              {
                                                  type: 'divider',
                                              },
                                          ];
                                          tableProps.columns?.forEach((column) => {
                                              if (!column.dataIndex) return;
                                              const isColumnVisible = isUndefined(
                                                  tableProps?.columnsState?.value?.[
                                                      String(column.dataIndex)
                                                  ]?.show,
                                              )
                                                  ? true
                                                  : tableProps?.columnsState?.value?.[
                                                        String(column.dataIndex)
                                                    ]?.show;
                                              items.push({
                                                  key: column.dataIndex.toString(),
                                                  label: (
                                                      <div
                                                          onClick={() => {
                                                              tableProps.columnsState?.onChange?.({
                                                                  ...tableProps.columnsState?.value,
                                                                  [String(column.dataIndex)]: {
                                                                      show: !tableProps.columnsState
                                                                          ?.value?.[
                                                                          String(column.dataIndex)
                                                                      ]?.show,
                                                                  },
                                                              });
                                                          }}
                                                      >
                                                          {isColumnVisible ? (
                                                              <CheckOutlined
                                                                  style={{
                                                                      marginRight: 4,
                                                                  }}
                                                              />
                                                          ) : (
                                                              <CloseOutlined
                                                                  style={{
                                                                      color: 'transparent',
                                                                      marginRight: 4,
                                                                  }}
                                                              />
                                                          )}
                                                          {column.title}
                                                      </div>
                                                  ),
                                              });
                                          });

                                          return (
                                              <Dropdown
                                                  overlayStyle={{
                                                      height: 500,
                                                      overflow: 'scroll',
                                                  }}
                                                  menu={{
                                                      items,
                                                  }}
                                                  trigger={['contextMenu']}
                                              >
                                                  <tr {...props} />
                                              </Dropdown>
                                          );
                                      },
                                  },
                                  body: {
                                      row: (props: any) => (
                                          <Dropdown
                                              menu={{
                                                  items: tableProps
                                                      .cellActions?.(contextMenuRow.current)
                                                      ?.map((item) => {
                                                          if (!item) return null;
                                                          return {
                                                              ...item,
                                                              onClick: () => {
                                                                  if ('onClick' in item) {
                                                                      item.onClick?.(
                                                                          contextMenuRow.current,
                                                                      );
                                                                  }
                                                              },
                                                          };
                                                      }),
                                              }}
                                              destroyPopupOnHide
                                              trigger={['contextMenu']}
                                          >
                                              <tr {...props} />
                                          </Dropdown>
                                      ),
                                  },
                              }
                            : undefined
                    }
                    tableClassName="paa-pro-table"
                    rowClassName={(_, index) => (index % 2 === 0 ? 'evenRow' : 'oddRow')}
                    search={false}
                    scroll={{ x: 'max-content' }}
                    {...tableProps}
                />
            </div>
        </div>
    );
};
