.search_echart_page :global {
    height: 100%;
    background-color: #fff;
    height: 100%;
    .search_box {
        margin-bottom: 10px;
        padding: 15px 15px;
        padding-bottom: 0px;
        background-color: #fff;
        .ant-form-item {
            margin-bottom: 15px;
        }
        .ant-form-inline {
            align-items: baseline;
        }
        .ant-form > div:last-child {
            margin-bottom: 15px;
        }
    }
    .echart_box {
        .chart {
            width: 100%;
            height: 100%;
            min-height: calc(100vh - 400px);
        }
        .noDataAvailable {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            font-size: 20px;
            color: #999;
        }
    }

    .table_box {
        background-color: #fff;
        border-radius: 2px;
        margin-top: 15px;
        .ant-table-measure-row {
            td {
                padding: 0px !important;
            }
        }
        tr {
            border-color: inherit;
            td {
                border-right: 1px solid #dfdfdf;
                padding: 8px !important;
                &:last-child {
                    border-right: none;
                }
            }
            &:last-child {
                border-right: none;
            }
        }
        table {
            border-collapse: separate;
            button {
                height: 16px !important;
            }
        }
    }
    .ant-table-thead > tr > th {
        background: #fafafa;
        padding: 8px !important;
        border-right: 1px solid #dfdfdf;
        text-align: center !important;
        &:last-child {
            border-right: none;
        }
    }
    .oddRow > td {
        background-color: #f2f7fd !important;
    }
}

.search_echart_page_border :global {
    .search_box {
        border: 1px solid #dfdfdf;
        border-radius: 4px;
    }
    .echart_box {
        border: 1px solid #dfdfdf;
        border-radius: 4px;
    }
    .table_box {
        border: 1px solid #dfdfdf;
        border-radius: 4px;
        table {
            border: 1px solid #dfdfdf;
        }
    }
}
