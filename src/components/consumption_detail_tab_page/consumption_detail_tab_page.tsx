// export default CustomPayGroup;
import { useIntl } from '@umijs/max';
import TabsPage from '@/components/tab_page';
import { ConsumptionDetailPage } from '@/components/consumption_detail_page/consumption_detail_page';
import { ConSumptionDetailJobListPage } from '@/components/consumption_detail_job_list_page';
import CpuTimeStatistics from '@/components/cpu_time_statistics';
import ApplicationStatistics from '@/components/application_statistics';
const ConsumptionDetailTbaPage = () => {
    const intl = useIntl();

    const tabs = [
        {
            label: intl.formatMessage({ id: 'pages.consumptionDetail.tabTitle' }),
            key: 'Detail',
            children: <ConsumptionDetailPage />,
        },
        {
            label: intl.formatMessage({ id: 'pages.billing.jobList' }),
            key: 'JobList',
            children: <ConSumptionDetailJobListPage />,
        },
        {
            label: intl.formatMessage({ id: 'pages.billing.nuclearTimeStatistics' }),
            key: 'CpuTimeStatistics',
            children: <CpuTimeStatistics />,
        },
        {
            label: intl.formatMessage({ id: 'pages.billing.applicationStatistics' }),
            key: 'ApplicationStatistics',
            children: <ApplicationStatistics />,
        },
    ];
    return <TabsPage tabs={tabs} />;
};

export default ConsumptionDetailTbaPage;
