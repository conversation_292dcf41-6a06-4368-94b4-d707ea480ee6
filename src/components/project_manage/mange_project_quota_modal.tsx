import React, { useRef, useState } from 'react';
import { ActionType, BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { IAction } from './types';
import { getAdminProjectTimeQuota } from '@/services/api';

export const MangeProjectQuotaModal: React.FC<{
    action?: IAction;
    tableAction: React.MutableRefObject<ActionType | undefined>;
}> = ({ action, tableAction }) => {
    const formRef = useRef<ProFormInstance>();
    const [open, setOpen] = useState(false);

    if (action) {
        action.openModal = async (data) => {
            await setOpen(true);
            await formRef.current?.setFieldsValue({
                ...data,
                projectMonthCpuTimeQuota: data.projectMonthCpuTimeQuota / (1000 * 60 * 60),
                projectMonthGpuTimeQuota: data.projectMonthGpuTimeQuota / (1000 * 60 * 60),
                projectId: data.id,
            });
        };
    }

    return (
        <BetaSchemaForm
            title="项目配额"
            layout="horizontal"
            layoutType="ModalForm"
            open={open}
            formRef={formRef}
            onFinish={async (values) => {
                const putData = await formRef.current?.getFieldsFormatValue();
                await getAdminProjectTimeQuota(putData);
                setOpen(false);
                tableAction.current?.reload();
            }}
            columns={[
                {
                    title: '项目iD',
                    dataIndex: 'projectId',
                    formItemProps: {
                        hidden: true,
                    },
                },
                {
                    title: '核时配额',
                    dataIndex: 'projectMonthCpuTimeQuota',
                    valueType: 'digit',
                    width: '100%',
                    transform: (value) => {
                        return {
                            projectMonthCpuTimeQuota: value * 1000 * 60 * 60,
                        };
                    },
                },
                {
                    title: '卡时配额',
                    dataIndex: 'projectMonthGpuTimeQuota',
                    valueType: 'digit',
                    width: '100%',
                    transform: (value) => {
                        return {
                            projectMonthGpuTimeQuota: value * 1000 * 60 * 60,
                        };
                    },
                },
            ]}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                },
            }}
        />
    );
};
