import {
    ActionType,
    BetaSchemaForm,
    ProFormDigit,
    ProFormInstance,
    ProFormSelect,
    ProFormTimePicker,
} from '@ant-design/pro-components';
import { MutableRefObject, useRef, useState } from 'react';
import { IProjectModalActions } from './types';
import { formItemLayout } from '@/utils/form_layout';
import { useIntl } from '@umijs/max';
import { Button, Col, Form, Row } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { putAdminProject } from '@/services/api';
import dayjs, { Dayjs } from 'dayjs';
import { getAssignAppColumns } from '../app_icon_manage/get_columns';

export const UpdateFastTimeModal = ({
    tableActions,
    actions,
}: {
    tableActions: MutableRefObject<ActionType | undefined>;
    actions: MutableRefObject<IProjectModalActions>;
}) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [info, setInfo] = useState<API.IApiResProjectModel>();
    const formRef = useRef<ProFormInstance>();
    const [project, setProject] = useState<API.IApiResProjectModel>();
    Object.assign(actions.current.updateFastTime, {
        openModal: async (row: API.IApiResProjectModel) => {
            await setOpen(true);
            setProject(row);
            await formRef.current?.setFieldsValue({
                ...row,
                enableFastCalculationTime: [
                    dayjs().startOf('day').minute(row.enableFastCalculationTimeStart),
                    dayjs().startOf('day').minute(row.enableFastCalculationTimeEnd),
                ],
                appTimeSubmitStrategies: row.appTimeSubmitStrategies.map(
                    (v: { stime: number; etime: number; strategy: string; pcqm: number }) => {
                        return {
                            timeRange: [
                                dayjs().startOf('day').add(v.stime, 'minute'),
                                dayjs().startOf('day').add(v.etime, 'minute'),
                            ],
                            strategy: v.strategy,
                            pcqm: v.pcqm,
                        };
                    },
                ),
            });
            setInfo(row);
        },
    });

    const onSubmit = async () => {
        const data = formRef.current?.getFieldsFormatValue?.();
        if (!project?.id || !project.name) return;
        await putAdminProject({
            ...info,
            ...data,
            id: project.id,
            name: project.name,
            fastCalculationType: data.fastCalculationType,
            appTimeSubmitStrategies: data.appTimeSubmitStrategies.map(
                (v: { timeRange: [Dayjs, Dayjs]; strategy: string; pcqm: number }) => {
                    return {
                        ...v,
                        strategy: v.strategy === 'null' ? null : v.strategy,
                        stime: dayjs(v.timeRange[0]).diff(dayjs().startOf('day'), 'minute'),
                        etime: dayjs(v.timeRange[1]).diff(dayjs().startOf('day'), 'minute'),
                        pcqm: v.strategy === 'DEFAULT' ? v.pcqm : null,
                    };
                },
            ),
        });
        formRef.current?.resetFields();
        await setOpen(false);
        await tableActions.current?.reload();
    };

    return (
        <BetaSchemaForm
            width={1200}
            title={intl.formatMessage({ id: 'pages.appIcon.updateFastTime' })}
            {...formItemLayout}
            open={open}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                },
            }}
            layout="horizontal"
            layoutType="ModalForm"
            formRef={formRef}
            onFinish={onSubmit}
            columns={[
                ...getAssignAppColumns(),
                {
                    title: intl.formatMessage({
                        id: 'pages.appIcon.applySubmissionStrategy',
                    }),
                    dataIndex: 'appTimeSubmitStrategies',
                    initialValue: [],
                    hideInForm: !globalConfig.USE_APP_STRATEGY,
                    renderFormItem: () => {
                        return (
                            <Form.List name="appTimeSubmitStrategies">
                                {(fields, { add, remove }) => (
                                    <>
                                        {fields.map(({ key, name, ...f }) => {
                                            return (
                                                <Row gutter={16}>
                                                    <Col span={10}>
                                                        <ProFormTimePicker.RangePicker
                                                            formItemProps={{
                                                                labelCol: {
                                                                    span: 6,
                                                                },
                                                                wrapperCol: {
                                                                    span: 18,
                                                                },
                                                            }}
                                                            label={intl.formatMessage({
                                                                id: 'pages.announcement.effectiveAt',
                                                            })}
                                                            {...f}
                                                            name={[name, 'timeRange']}
                                                            rules={[{ required: true }]}
                                                            fieldProps={{
                                                                format: 'HH:mm',
                                                            }}
                                                        />
                                                    </Col>
                                                    <Col span={6}>
                                                        <ProFormSelect
                                                            labelCol={{
                                                                span: 7,
                                                            }}
                                                            label={intl.formatMessage({
                                                                id: 'pages.appIcon.strategy',
                                                            })}
                                                            {...f}
                                                            name={[name, 'strategy']}
                                                            fieldProps={{
                                                                allowClear: false,
                                                            }}
                                                            valueEnum={{
                                                                null: intl.formatMessage({
                                                                    id: 'pages.organization.user.allowDownload.null',
                                                                }),
                                                                DEFAULT: intl.formatMessage({
                                                                    id: 'pages.appIcon.default',
                                                                }),
                                                                FORCE_FREE_RESOURCE:
                                                                    intl.formatMessage({
                                                                        id: 'pages.appIcon.forceIdleCores',
                                                                    }),
                                                            }}
                                                        />
                                                    </Col>
                                                    {formRef?.current?.getFieldValue(
                                                        'appTimeSubmitStrategies',
                                                    )[key]?.strategy === 'DEFAULT' && (
                                                        <Col span={6}>
                                                            <ProFormDigit
                                                                dependencies={[
                                                                    'appTimeSubmitStrategies.strategy',
                                                                ]}
                                                                labelCol={{
                                                                    span: 8,
                                                                }}
                                                                label={intl.formatMessage({
                                                                    id: 'pages.billing.queuingTime',
                                                                })}
                                                                {...f}
                                                                name={[name, 'pcqm']}
                                                                fieldProps={{
                                                                    min: 0,
                                                                    precision: 0,
                                                                }}
                                                            />
                                                        </Col>
                                                    )}

                                                    <Col span={2}>
                                                        <DeleteOutlined
                                                            onClick={() => remove(name)}
                                                        />
                                                    </Col>
                                                </Row>
                                            );
                                        })}
                                        <Form.Item
                                            style={{
                                                display: 'flex',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <Button
                                                style={{
                                                    width: 400,
                                                }}
                                                onClick={() =>
                                                    add({
                                                        strategy: 'DEFAULT',
                                                        pcqm: 30,
                                                    })
                                                }
                                                block
                                            >
                                                {intl.formatMessage({
                                                    id: 'pages.systemOpenFileManage.addConfiguration',
                                                })}
                                            </Button>
                                        </Form.Item>
                                    </>
                                )}
                            </Form.List>
                        );
                    },
                },
            ]}
        />
    );
};
