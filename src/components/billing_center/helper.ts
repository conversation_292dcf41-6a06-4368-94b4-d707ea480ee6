import { cloneDeep } from 'lodash-es';

export function calcTotalDataTransFormer<T>(
    data: T[],
    options?: {
        keys?: string[];
        firstColumnKey?: string;
        firstColumnValue?: any;
    },
): T[] {
    const totalObj: any = {};
    const ldata = cloneDeep(data);
    ldata.forEach((item) => {
        for (const i in item) {
            if (typeof item[i] === 'number') {
                // 如果指定了 keys，只计算指定的字段；否则计算所有数值字段
                if (!options?.keys || options.keys.includes(i)) {
                    if (!totalObj[i]) {
                        totalObj[i] = 0;
                    }
                    totalObj[i] += item[i];
                }
            }
        }
    });

    // 设置第一列的 key 和 value
    if (options?.firstColumnKey && options?.firstColumnValue !== undefined) {
        totalObj[options.firstColumnKey] = options.firstColumnValue;
    }

    totalObj.isTotal = true;
    ldata.push(totalObj);
    return ldata;
}

export function splitNum(num: number) {
    let k = 1;

    while (num / k > 1000) {
        k *= 10;
    }
    num = num / k;
    if (num < 5) return 1 * k;
    if (num < 10) return 2 * k;
    if (num < 12) return Math.round(num / 5) * k;

    if (num <= 50) return Math.ceil(num / 5) * k;

    if (num <= 60) return 12 * k;
    if (num <= 75) return 15 * k;
    if (num <= 80) return 16 * k;
    if (num <= 90) return 18 * k;
    if (num <= 100) return 20 * k;
    if (num <= 125) return 25 * k;
    if (num <= 150) return 30 * k;
    if (num <= 160) return 32 * k;
    if (num <= 200) return 40 * k;
    if (num <= 250) return 50 * k;
    if (num <= 300) return 60 * k;
    if (num <= 350) return 70 * k;
    if (num <= 400) return 80 * k;
    if (num <= 450) return 90 * k;
    if (num <= 500) return 100 * k;
    if (num <= 600) return 120 * k;
    if (num <= 750) return 150 * k;
    if (num <= 800) return 160 * k;
    if (num <= 900) return 180 * k;
    if (num <= 1000) return 200 * k;
    return 10;
}

/**
 * 金额格式化
 * @param value 金额数值
 * @param precision 精度-显示小数点后到位数。
 */
export default function moneyFormatter(value: number, precision = 0): string {
    if (value === null || value === undefined || isNaN(value)) {
        return 'N/A';
    }
    return value.toFixed(precision).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');
}

export function calcJobNumInterval(jobNum: number[]) {
    let max = 0;
    jobNum.forEach((item) => (max = item > max ? item : max));
    return splitNum(max);
}

export function downLoadBlob(contentHeader: string | null, data: Blob) {
    if (!contentHeader) {
        throw new Error('missing content header');
    }
    const search = contentHeader?.match(/filename="(\S*)"/);
    let fileName = '';
    if (search && search?.length > 1) {
        fileName = decodeURIComponent(search[1]);
    } else {
        fileName = 'unknown.csv';
    }
    const downloadNode = document.createElement('a');
    downloadNode.download = fileName;
    downloadNode.style.display = 'none';
    downloadNode.target = '_blank';
    downloadNode.href = URL.createObjectURL(data);
    document.body.appendChild(downloadNode);
    downloadNode.click();
    URL.revokeObjectURL(downloadNode.href);
    document.body.removeChild(downloadNode);
}
