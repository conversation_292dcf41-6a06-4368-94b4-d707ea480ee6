import { DatePicker, Select } from 'antd';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import { useIntl } from '@umijs/max';
import { isNull } from 'lodash-es';

const { RangePicker } = DatePicker;

type ITimeType =
    | 'last_1d'
    | 'last_3d'
    | 'last_7d'
    | 'prev_1d'
    | 'prev_3d'
    | 'prev_7d'
    | 'this_week'
    | 'last_week'
    | 'this_month'
    | 'last_month'
    | 'custom';
type IPickType = 'date' | 'week' | 'month' | 'quarter' | 'year';

export default function TimeSelector({
    onChange,
    selectType = 'prev_1d',
    range = true,
    pickType = 'date',
    options = [
        'last_1d',
        'last_3d',
        'last_7d',
        'prev_1d',
        'prev_3d',
        'prev_7d',
        'this_week',
        'last_week',
        'this_month',
        'last_month',
        'custom',
    ],
    defaultStartTime,
    defaultEndTime,
    allowEmpty = [false, false],
    displayType = 'default',
}: {
    onChange?: (timeRange: [string, string]) => void;
    selectType?: ITimeType;
    range?: boolean;
    pickType?: IPickType;
    options?: ITimeType[];
    defaultStartTime?: Dayjs;
    defaultEndTime?: Dayjs;
    allowEmpty?: [boolean, boolean];
    displayType?: 'default' | 'onlyDate';
}) {
    const intl = useIntl();
    const radioOptions: { label: string; value: ITimeType }[] = [
        {
            label: intl.formatMessage({ id: 'pages.timeSelector.previousDay' }),
            value: 'prev_1d',
        },
        {
            label: intl.formatMessage({ id: 'pages.timeSelector.previous3Days' }),
            value: 'prev_3d',
        },
        {
            label: intl.formatMessage({ id: 'pages.timeSelector.previous7Days' }),
            value: 'prev_7d',
        },
        {
            label: intl.formatMessage({ id: 'pages.timeSelector.last24Hours' }),
            value: 'last_1d',
        },
        {
            label: intl.formatMessage({ id: 'pages.timeSelector.last3Days' }),
            value: 'last_3d',
        },
        {
            label: intl.formatMessage({ id: 'pages.timeSelector.last7Days' }),
            value: 'last_7d',
        },
        {
            label: intl.formatMessage({ id: 'pages.monitorsJobs.thisWeek' }),
            value: 'this_week',
        },
        {
            label: intl.formatMessage({ id: 'pages.monitorsJobs.lastWeek' }),
            value: 'last_week',
        },
        {
            label: intl.formatMessage({ id: 'pages.monitorsJobs.thisMonth' }),
            value: 'this_month',
        },
        {
            label: intl.formatMessage({ id: 'pages.monitorsJobs.lastMonth' }),
            value: 'last_month',
        },
        {
            label: intl.formatMessage({ id: 'pages.monitorsJobs.custom' }),
            value: 'custom',
        },
    ];
    const domRadioOptions = radioOptions.filter((item) => options.includes(item.value));
    const [timeTypeValue, setTimeTypeValue] = useState<ITimeType>(selectType);
    const [startTime, setStartTime] = useState<Dayjs>(() => {
        if (isNull(defaultStartTime)) {
            return null;
        }
        return defaultStartTime || dayjs().startOf(pickType);
    });
    const [endTime, setEndTime] = useState<Dayjs>(() => {
        if (isNull(defaultEndTime)) {
            return null;
        }
        return defaultEndTime || dayjs().endOf(pickType);
    });
    const [rangeTime, setRangeTime] = useState<Dayjs>(() => {
        return dayjs().startOf(pickType);
    });
    const getDateRange = (): [string, string] => {
        switch (timeTypeValue) {
            case 'prev_1d':
                return [dayjs().subtract(24, 'hour').toISOString(), dayjs().toISOString()];
            case 'prev_3d':
                return [dayjs().subtract(72, 'hour').toISOString(), dayjs().toISOString()];
            case 'prev_7d':
                return [dayjs().subtract(168, 'hour').toISOString(), dayjs().toISOString()];
            case 'last_1d':
                return [
                    dayjs().subtract(1, 'day').startOf('day').toISOString(),
                    dayjs().subtract(1, 'day').endOf('day').toISOString(),
                ];
            case 'last_3d':
                return [
                    dayjs().subtract(3, 'day').startOf('day').toISOString(),
                    dayjs().subtract(1, 'day').endOf('day').toISOString(),
                ];
            case 'last_7d':
                return [
                    dayjs().subtract(7, 'day').startOf('day').toISOString(),
                    dayjs().subtract(1, 'day').endOf('day').toISOString(),
                ];
            case 'this_week':
                return [dayjs().startOf('week').toISOString(), dayjs().endOf('week').toISOString()];
            case 'last_week':
                return [
                    dayjs().subtract(1, 'week').startOf('week').toISOString(),
                    dayjs().subtract(1, 'week').endOf('week').toISOString(),
                ];
            case 'this_month':
                return [
                    dayjs().startOf('month').toISOString(),
                    dayjs().endOf('month').toISOString(),
                ];
            case 'last_month':
                return [
                    dayjs().subtract(1, 'month').startOf('month').toISOString(),
                    dayjs().subtract(1, 'month').endOf('month').toISOString(),
                ];
            case 'custom':
                return [startTime?.toISOString(), endTime?.toISOString()];
            default:
                return [startTime?.toISOString(), endTime?.toISOString()];
        }
    };
    useEffect(() => {
        if (onChange) {
            onChange(getDateRange());
        }
    }, [startTime, endTime, timeTypeValue]);

    return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
            {displayType === 'onlyDate' ? null : (
                <Select
                    style={{ width: 150, marginRight: timeTypeValue === 'custom' ? 10 : 0 }}
                    defaultValue={selectType}
                    options={domRadioOptions}
                    onChange={(value: ITimeType) => {
                        setTimeTypeValue(value);
                    }}
                />
            )}

            {timeTypeValue === 'custom' && range ? (
                <RangePicker
                    picker={pickType}
                    value={[startTime, endTime]}
                    allowEmpty={allowEmpty}
                    onChange={(e) => {
                        setStartTime(dayjs(e?.[0]).startOf(pickType));
                        setEndTime(dayjs(e?.[1]).endOf(pickType));
                        if (!e?.[0]) {
                            setStartTime(null);
                        }
                        if (!e?.[1]) {
                            setEndTime(null);
                        }
                    }}
                />
            ) : null}
            {timeTypeValue === 'custom' && !range ? (
                <DatePicker
                    picker={pickType}
                    value={rangeTime}
                    onChange={(e) => {
                        setRangeTime(dayjs(e).startOf(pickType));
                        setStartTime(dayjs(e).startOf(pickType));
                        setEndTime(dayjs(e).endOf(pickType));
                    }}
                />
            ) : null}
        </div>
    );
}
