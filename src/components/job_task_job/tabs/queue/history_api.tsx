import dayjs from 'dayjs';
import { useState } from 'react';
import { getIntl, getLocale } from '@umijs/max';
import { getJobCpuHistory, getJobGpuHistory } from '@/services/api/admin_job_task';
import { splitNum } from '@/utils/time_split_num';

const intl = getIntl(getLocale());

export function calcChartDataMaxValue(data: any) {
    let max = 0;
    for (let i = 0; i < data.x.length; i++) {
        const _max =
            data.run[i] +
            data.resourceLimit[i] +
            data.personalLimit[i] +
            data.licenseLimit[i] +
            data.errorLimit[i] +
            data.otherLimit[i] +
            data.exclusiveLimit[i];
        max = max > _max ? max : _max;
        max = max > data.total[i] ? max : data.total[i];
    }
    return {
        max,
        ...data,
    };
}

export const useHistoryApi = () => {
    const [cpuTimeInterval, setCpuTimeInterval] = useState(256);
    const [loading, setLoading] = useState<boolean>(false);
    const [data, setData] = useState<
        (API.IApiResGetJobCpuHistory | API.IApiResGetJobGpuHistory) & {
            total: number[];
            run: number[];
            xAxisData: string[];
            max: number;
        }
    >();
    const [title, setTitle] = useState<{
        run: string;
        y: string;
    }>({
        run: `${intl.formatMessage({ id: 'pages.monitorsJobs.runCores' })}`,
        y: `${intl.formatMessage({ id: 'pages.monitorsJobs.nuclear' })}`,
    });
    const [coreInfo, setCoreInfo] = useState<{
        totalTime: number;
        totalTimeLimit: number;
        totalTimeUsage: number;
    }>();
    const [limitTitle, setLimitTitle] = useState<string>(
        `${intl.formatMessage({ id: 'pages.monitorsJobs.resourceUpperLimit' })}`,
    );

    const getJobHistory = async (params: API.IApiReqGetJobCpuHistory & { type: 'CPU' | 'GPU' }) => {
        if (params.zones && params.zones?.length !== 0) {
            setLimitTitle(
                `${intl.formatMessage({ id: 'pages.monitorsJobs.resourceUpperLimit' })}(${intl.formatMessage({ id: 'pages.jobTag.history.basis' })}${intl.formatMessage({ id: 'pages.job.zone' })})`,
            );
        } else if (params.platforms && params.platforms?.length !== 0) {
            setLimitTitle(
                `${intl.formatMessage({ id: 'pages.monitorsJobs.resourceUpperLimit' })}(${intl.formatMessage({ id: 'pages.jobTag.history.basis' })}${intl.formatMessage({ id: 'pages.job.platform' })})`,
            );
        } else if (params.queues && params.queues?.length !== 0) {
            setLimitTitle(
                `${intl.formatMessage({ id: 'pages.monitorsJobs.resourceUpperLimit' })}(${intl.formatMessage({ id: 'pages.jobTag.history.basis' })}${intl.formatMessage({ id: 'menu.resource.queue' })})`,
            );
        } else {
            setLimitTitle(`${intl.formatMessage({ id: 'pages.monitorsJobs.resourceUpperLimit' })}`);
        }
        setLoading(true);
        if (params.type === 'CPU') {
            setTitle({
                run: `${intl.formatMessage({ id: 'pages.monitorsJobs.runCores' })}`,
                y: `${intl.formatMessage({ id: 'pages.monitorsJobs.nuclear' })}`,
            });
            const res = await getJobCpuHistory(params);
            setCoreInfo({
                totalTime: res.jobCpuTimeStatistics.totalCpuTime,
                totalTimeLimit: res.jobCpuTimeStatistics.totalCpuTimeLimit,
                totalTimeUsage: res.jobCpuTimeStatistics.totalCpuTimeUsage,
            });
            const _data = calcChartDataMaxValue({
                ...res,
                xAxisData: res.x.map((item) => dayjs(item).format('YYYY-MM-DD HH:mm')),
                total: res.totalCore,
                run: res.runCore,
            });
            const step = splitNum(_data.max / 256);
            const lcpuTimeInterval = step * 256;
            setCpuTimeInterval(lcpuTimeInterval);
            setData(_data);
        } else {
            setTitle({
                run: `${intl.formatMessage({ id: 'pages.monitorsJobs.runGPUs' })}`,
                y: `${intl.formatMessage({ id: 'pages.monitorsJobs.gpu' })}`,
            });
            const res = await getJobGpuHistory(params);
            setCoreInfo({
                totalTime: res.jobGpuTimeStatistics.totalGpuTime,
                totalTimeLimit: res.jobGpuTimeStatistics.totalGpuTimeLimit,
                totalTimeUsage: res.jobGpuTimeStatistics.totalGpuTimeUsage,
            });
            const _data = calcChartDataMaxValue({
                ...res,
                xAxisData: res.x.map((item) => dayjs(item).format('YYYY-MM-DD HH:mm')),
                total: res.totalGpu,
                run: res.runGpu,
            });
            const step = splitNum(_data.max / 256);
            const lcpuTimeInterval = step * 256;
            setCpuTimeInterval(lcpuTimeInterval);
            setData(_data);
        }
        setLoading(false);
    };

    return {
        getJobHistory,
        data,
        loading,
        title,
        limitTitle,
        coreInfo,
        cpuTimeInterval,
    };
};
