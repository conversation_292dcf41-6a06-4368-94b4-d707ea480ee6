import GroupSelector from '@/components/admin_group_manage/group_selector';
import TimeSelector from '@/components/time_selector2';
import { type ProFormInstance, type ProFormColumnsType } from '@ant-design/pro-components';
import { getIntl, getLocale } from '@umijs/max';
import { Divider, Space } from 'antd';
import { type MutableRefObject } from 'react';
import { useCommonStore } from '@/components/job_task_job/common_store';
import dayjs from 'dayjs';

const intl = getIntl(getLocale());

export const getSearchColumns = (
    formRef: MutableRefObject<ProFormInstance | undefined>,
): ProFormColumnsType[] => {
    const {
        allZoneCode,
        getZoneCode,
        allPlatforms,
        getPlatforms,
        getQueues,
        allQueues,
        allApps,
        getApps,
        allJobUsers,
        getJobUsers,
        allUids,
        getUids,
    } = useCommonStore();

    return [
        {
            title: intl.formatMessage({ id: 'pages.monitorsJobs.statisticalRange' }),
            dataIndex: 'timeRange',
            renderFormItem: () => <TimeSelector />,
            transform: (timeRange) => {
                return {
                    intervalStartTimeStart: timeRange[0],
                    intervalStartTimeEnd: timeRange[1],
                };
            },
        },
        {
            title: '统计时刻',
            dataIndex: 'dailyMinutes',
            valueType: 'dateTime',
            transform: (dailyMinutes) => {
                return {
                    // 转换为相对于每天0点0分点偏移量，使用dayjs
                    dailyMinutes: dayjs(dailyMinutes).diff(
                        dayjs(dailyMinutes).startOf('day'),
                        'minute',
                    ),
                };
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.job.zone' }),
            dataIndex: 'zones',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('zones', allZoneCode);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getZoneCode,
        },
        {
            title: intl.formatMessage({ id: 'pages.resourceQueue.terrace' }),
            dataIndex: 'platforms',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('platforms', allPlatforms);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getPlatforms,
        },
        {
            title: intl.formatMessage({ id: 'menu.resource.queue' }),
            dataIndex: 'queues',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('queues', allQueues);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getQueues,
        },
        {
            title: intl.formatMessage({ id: 'menu.app.icon' }),
            dataIndex: 'softwares',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('softwares', allApps);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getApps,
        },
        {
            title: intl.formatMessage({ id: 'pages.organization.user.group' }),
            dataIndex: 'gids',
            renderFormItem: () => <GroupSelector />,
        },
        {
            title: intl.formatMessage({ id: 'menu.organization.user' }),
            dataIndex: 'uids',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('uids', allUids);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getUids,
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.supercomputerAccount' }),
            dataIndex: 'jobUsers',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('jobUsers', allJobUsers);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getJobUsers,
        },
        {
            title: intl.formatMessage({ id: 'menu.jobTag.type' }),
            dataIndex: 'type',
            valueType: 'select',
            initialValue: globalConfig.JOB_TASK_JOB_CONFIG.TYPE,
            fieldProps: {
                allowClear: false,
            },
            valueEnum: {
                CPU: 'CPU',
                GPU: 'GPU',
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.appIcon.intervalTime' }),
            dataIndex: 'jobTimeUnit',
            initialValue: globalConfig.JOB_TASK_JOB_CONFIG.JOB_TIME_UNIT,
            fieldProps: {
                allowClear: false,
            },
            valueEnum: new Map([
                ['HOURS', intl.formatMessage({ id: 'component.tips.hours' })],
                ['MINUTES', intl.formatMessage({ id: 'component.tips.minutes' })],
            ]),
        },
    ];
};
