import { useIntl } from '@umijs/max';
import SearchEchartPage from '@/components/search_echart_page';
import { type ProFormInstance } from '@ant-design/pro-components';
import { useEffect, useRef } from 'react';
import { getSearchColumns } from './get_columns';
import { useHistoryApi } from './history_api';
import dayjs from 'dayjs';
import { thousandthDivision } from '@/utils/utils';

export const Queue = () => {
    const intl = useIntl();
    const formRef = useRef<ProFormInstance>();

    const { getJobHistory, data, limitTitle, loading, title, coreInfo, cpuTimeInterval } =
        useHistoryApi();
    const fetchData = async () => {
        const searchParams = formRef.current?.getFieldFormatValue?.();
        return await getJobHistory(searchParams);
    };

    useEffect(() => {
        formRef.current?.submit();
    }, []);

    return (
        <>
            <SearchEchartPage
                style={{
                    paddingBottom: '0px',
                }}
                status={{
                    loading,
                    chartsIsNull: !data,
                }}
                searchProps={{
                    formRef,
                    columns: getSearchColumns(formRef),
                    onFinish: fetchData,
                }}
                echartProps={{
                    option: {
                        title: {
                            textStyle: {
                                fontStyle: 'normal',
                                fontWeight: 'normal',
                                color: '#000',
                                fontSize: 14,
                            },
                        },
                        legend: {
                            type: 'scroll',
                            selected: {
                                [intl.formatMessage({ id: 'pages.monitorsJobs.runJob' })]: false,
                            },
                        },
                        grid: {
                            top: 70,
                            left: 30,
                            right: 50,
                            bottom: 40,
                            containLabel: true,
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: (data) => {
                                if (!Array.isArray(data)) {
                                    return '';
                                }
                                let res = `${intl.formatMessage({
                                    id: 'pages.monitorsJobs.date',
                                })}: ${dayjs(Number(data[0].name)).format('YYYY-MM-DD HH:mm')}<br/>`;
                                data.forEach((item: any) => {
                                    let marker = `<span style="color:${item.color};">&nbsp&nbsp<div style="width:10px;height:10px;border-radius:50%;display:inline-block">${item.marker}</div>&nbsp&nbsp</span>`;
                                    if (item.seriesType === 'line') {
                                        marker = `<span style="color:${item.color};">-<div style="border:1px solid ${item.color};background:#fff;width:10px;height:10px;border-radius:50%;display:inline-block"></div>-</span>`;
                                    }
                                    res += `<x-p style="display:flex;justify-content:space-between;">
                                <span>${marker}${item.seriesName}</span>
                                <span>${item.data ? thousandthDivision(item.data, 0) : 0}</span>
                                </x-p>`;
                                });
                                return res;
                            },
                            axisPointer: {
                                snap: true,
                                type: 'shadow',
                            },
                        },
                        xAxis: {
                            type: 'category',
                            data: data?.x,
                            axisLabel: {
                                rotate: -90,
                                // 更改坐标轴文字颜色
                                fontSize: 14, // 更改坐标轴文字大小
                                color: '#000',
                                fontFamily: 'Helvetica Neue',
                                formatter: (value: unknown) => {
                                    return dayjs(Number(value)).format('YYYY-MM-DD HH:mm');
                                },
                            },
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: `(${title.y})`,
                                max: () => {
                                    const val = data?.max || 0;
                                    const interval = Math.ceil(val / cpuTimeInterval);
                                    if (val === 0) {
                                        return 1024;
                                    }
                                    return interval * cpuTimeInterval;
                                },
                                interval: cpuTimeInterval,
                            },
                            {
                                type: 'value',
                                name: `(${intl.formatMessage({ id: 'pages.billing.jobNum' })})`,
                                minInterval: 1,
                                alignTicks: true,
                            },
                        ],
                        series: [
                            {
                                name: title.run,
                                data: data?.run,
                                type: 'bar',
                                stack: 'other',
                                itemStyle: {
                                    color: '#377e22',
                                },
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.billing.resourceQueuing' }),
                                data: data?.resourceLimit,
                                type: 'bar',
                                stack: 'other',
                                itemStyle: {
                                    color: '#b02418',
                                },
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.billing.quotaQueuing' }),
                                data: data?.personalLimit,
                                type: 'bar',
                                stack: 'other',
                                itemStyle: {
                                    color: '#f6e653',
                                },
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.billing.licenseQueue' }),
                                data: data?.licenseLimit,
                                type: 'bar',
                                stack: 'other',
                                itemStyle: {
                                    color: '#cedc58',
                                },
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.billing.exceptionQueuing' }),
                                data: data?.errorLimit,
                                stack: 'other',
                                type: 'bar',
                                itemStyle: {
                                    color: '#ea3323',
                                },
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.billing.otherQueues' }),
                                data: data?.otherLimit,
                                stack: 'other',
                                type: 'bar',
                                itemStyle: {
                                    color: '#a5aeb4',
                                },
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.billing.exclusiveQueues' }),
                                data: data?.exclusiveLimit,
                                stack: 'other',
                                type: 'bar',
                                itemStyle: {
                                    color: '#7f8142',
                                },
                            },
                            {
                                name: limitTitle,
                                data: data?.total,
                                type: 'line',
                                smooth: 0,
                                itemStyle: {
                                    color: '#3b8127',
                                },
                            },
                            {
                                name: intl.formatMessage({
                                    id: 'pages.monitorsUser.numberOfJobsSubmitted',
                                }),
                                data: data?.submitJob,
                                type: 'line',
                                itemStyle: {
                                    color: '#d7955d',
                                },
                                smooth: 0,
                                yAxisIndex: 1,
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.monitorsJobs.runJob' }),
                                data: data?.runJob,
                                type: 'line',
                                smooth: 0,
                                itemStyle: {
                                    color: '#b0f87c',
                                },
                                yAxisIndex: 1,
                            },
                            {
                                name: intl.formatMessage({ id: 'pages.monitorsJobs.pendingJob' }),
                                data: data?.queueJob,
                                type: 'line',
                                smooth: 0,
                                itemStyle: {
                                    color: '#567be5',
                                },
                                yAxisIndex: 1,
                            },
                        ],
                    },
                }}
            />
            <p
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    marginTop: '-15px',
                }}
            >
                {formRef.current?.getFieldValue('type') === 'CPU'
                    ? `${intl.formatMessage({ id: 'pages.billing.usedCpuTime' })}：${thousandthDivision(Number(coreInfo?.totalTime) / 1000 / 60 / 60, 0)} ${intl.formatMessage({ id: 'pages.billing.totalCpuTime' })}：${thousandthDivision(Number(coreInfo?.totalTimeLimit) / 1000 / 60 / 60, 0)} ${intl.formatMessage({ id: 'pages.billing.utilizationRate' })}: ${(Number(coreInfo?.totalTimeUsage) * 100).toFixed(2)}%`
                    : `已用${intl.formatMessage({ id: 'pages.billing.gpuTime' })}：${thousandthDivision(Number(coreInfo?.totalTime) / 1000 / 60 / 60, 0)} ${intl.formatMessage({ id: 'pages.billing.gpuTime' })}总量：${thousandthDivision(Number(coreInfo?.totalTimeLimit) / 1000 / 60 / 60, 0)} ${intl.formatMessage({ id: 'pages.billing.utilizationRate' })}: ${(Number(coreInfo?.totalTimeUsage) * 100).toFixed(2)}%`}
            </p>
        </>
    );
};
