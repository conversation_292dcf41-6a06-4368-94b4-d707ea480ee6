import { request } from '@umijs/max';

export const queueReasonApi = {
    page: (params?: API.QueueReason.Request) => {
        return request<API.IApiResPagedDataModel<API.QueueReason.Response>>(
            'admin/job/pending/reason/list',
            { params },
        );
    },
    create: (data: API.QueueReason.Create.Request) => {
        return request<API.QueueReason.Response>('admin/job/pending/reason', {
            method: 'POST',
            data,
        });
    },
    update: (data: API.QueueReason.Update.Request) => {
        return request<API.QueueReason.Response>('admin/job/pending/reason', {
            method: 'PUT',
            data,
        });
    },
    delete: (params: API.QueueReason.Delete.Request) => {
        return request<API.QueueReason.Response>('admin/job/pending/reason', {
            method: 'DELETE',
            params,
        });
    },
    typeList: () => {
        return request<API.QueueReason.TypeList.Response>('admin/job/pending/reason/type/list', {
            method: 'GET',
        });
    },
    getSysEnum: () => {
        return request<string[]>('admin/scheduling/system/enum', {
            method: 'GET',
        });
    },
};
