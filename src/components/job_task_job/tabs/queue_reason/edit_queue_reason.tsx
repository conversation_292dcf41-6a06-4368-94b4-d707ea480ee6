import { useIntl } from '@umijs/max';
import { ActionType, BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import { formItemLayout } from '@/utils/form_layout';
import { useQueueReasonStore } from './queue_reason_store';

export interface EditQueueReasonProps {
    actions: {
        changeOpen?: (row: API.QueueReason.Response) => void;
    };
    tableRef: React.MutableRefObject<ActionType | undefined>;
}

export const EditQueueReason = ({ actions, tableRef }: EditQueueReasonProps) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const { editQueueReason, getReasonTypeList, getSysEnum } = useQueueReasonStore();
    const formRef = useRef<ProFormInstance>(null);
    Object.assign(actions, {
        changeOpen: async (row: API.QueueReason.Response) => {
            await setOpen(true);
            await formRef.current?.setFieldsValue(row);
        },
    });

    const onFinish = async () => {
        const params = await formRef.current?.validateFields();
        await editQueueReason(params);
        await tableRef.current?.reloadAndRest?.();
        setOpen(false);
    };

    return (
        <BetaSchemaForm
            {...formItemLayout}
            formRef={formRef}
            title={intl.formatMessage({ id: 'component.operate.edit' })}
            open={open}
            layout="horizontal"
            layoutType="ModalForm"
            onFinish={onFinish}
            columns={[
                {
                    formItemProps: {
                        hidden: true,
                    },
                    dataIndex: 'id',
                },
                {
                    title: intl.formatMessage({ id: 'pages.resourceCluster.schedulingSystem' }),
                    dataIndex: 'schedulingSystem',
                    request: getSysEnum,
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.queueReason' }),
                    dataIndex: 'reasonDesc',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.queueReason.reasonType' }),
                    request: getReasonTypeList,
                    dataIndex: 'reasonType',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.queueReason.reasonKey' }),
                    dataIndex: 'reasonKey',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
            ]}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                },
            }}
        />
    );
};
