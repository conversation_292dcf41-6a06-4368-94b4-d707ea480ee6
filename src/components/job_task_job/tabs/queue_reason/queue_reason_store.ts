import { queueReasonApi } from './queue_reason_api';

export const useQueueReasonStore = () => {
    const getPage = async (params?: API.QueueReason.Request) => {
        const res = await queueReasonApi.page(params);
        return res;
    };

    const deleteQueueReason = async (id: number) => {
        const res = await queueReasonApi.delete({ id });
        return res;
    };

    const addQueueReason = async (params: API.QueueReason.Create.Request) => {
        const res = await queueReasonApi.create(params);
        return res;
    };

    const editQueueReason = async (params: API.QueueReason.Update.Request) => {
        const res = await queueReasonApi.update(params);
        return res;
    };

    const getSysEnum = async () => {
        const res = await queueReasonApi.getSysEnum();
        return res.map((item) => ({
            label: item,
            value: item,
        }));
    };

    const getReasonTypeList = async () => {
        const res = await queueReasonApi.typeList();
        return res.reasonTypes.map((item) => ({
            label: item,
            value: item,
        }));
    };

    return {
        getPage,
        deleteQueueReason,
        addQueueReason,
        editQueueReason,
        getReasonTypeList,
        getSysEnum,
    };
};
