import { useIntl } from '@umijs/max';
import { ActionType, BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import { formItemLayout } from '@/utils/form_layout';
import { useQueueReasonStore } from './queue_reason_store';

export interface AddQueueReasonProps {
    actions: {
        changeOpen?: () => void;
    };
    tableRef: React.MutableRefObject<ActionType | undefined>;
}

export const AddQueueReason = ({ actions, tableRef }: AddQueueReasonProps) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const { addQueueReason, getReasonTypeList, getSysEnum } = useQueueReasonStore();
    const formRef = useRef<ProFormInstance>(null);
    Object.assign(actions, {
        changeOpen: () => {
            setOpen(true);
        },
    });

    const onFinish = async () => {
        const params = await formRef.current?.validateFields();
        await addQueueReason(params);
        await tableRef.current?.reloadAndRest?.();
        setOpen(false);
    };

    return (
        <BetaSchemaForm
            {...formItemLayout}
            formRef={formRef}
            title={intl.formatMessage({ id: 'component.operate.create' })}
            open={open}
            layout="horizontal"
            layoutType="ModalForm"
            onFinish={onFinish}
            columns={[
                {
                    title: intl.formatMessage({ id: 'pages.resourceCluster.schedulingSystem' }),
                    dataIndex: 'schedulingSystem',
                    request: getSysEnum,
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.queueReason' }),
                    dataIndex: 'reasonDesc',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.queueReason.reasonType' }),
                    dataIndex: 'reasonType',
                    request: getReasonTypeList,
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.queueReason.reasonKey' }),
                    dataIndex: 'reasonKey',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
            ]}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                },
            }}
        />
    );
};
