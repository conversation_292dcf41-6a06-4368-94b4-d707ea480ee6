import SearchTablePage from '@/components/search_table_page';
import { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { useQueueReasonStore } from './queue_reason_store';
import { useRef } from 'react';
import { useIntl } from '@umijs/max';
import { Button, Modal } from 'antd';
import { AddQueueReason, AddQueueReasonProps } from './add_queue_reason';
import { EditQueueReason, EditQueueReasonProps } from './edit_queue_reason';

export interface ActionsType {
    addRef: AddQueueReasonProps['actions'];
    editRef: EditQueueReasonProps['actions'];
}

export const colorMap: Record<string, string> = {
    resourceLimit: '#b02418',
    personalLimit: '#f6e653',
    licenseLimit: '#cedc58',
    errorLimit: '#ea3323',
    otherLimit: '#a5aeb4',
    exclusiveLimit: '#7f8142',
};

export const QueueReason = () => {
    const intl = useIntl();
    const searchFormRef = useRef<ProFormInstance>();
    const tableRef = useRef<ActionType>();
    const { getPage, deleteQueueReason } = useQueueReasonStore();
    const actionsRef = useRef<ActionsType>({
        addRef: {},
        editRef: {},
    });

    return (
        <>
            <SearchTablePage
                rightMenu={true}
                searchProps={{
                    formRef: searchFormRef,
                    columns: [
                        {
                            title: intl.formatMessage({ id: 'pages.job.queueReason' }),
                            dataIndex: 'reasonDescSearch',
                        },
                    ],
                    onFinish: async (values) => {
                        tableRef.current?.reloadAndRest?.();
                    },
                }}
                tableProps={{
                    pagination: {
                        pageSize: 10,
                    },
                    actionRef: tableRef,
                    toolBarRender: () => [
                        <Button
                            type="primary"
                            key="primary"
                            onClick={() => {
                                actionsRef.current.addRef.changeOpen?.();
                            }}
                        >
                            {intl.formatMessage({ id: 'component.operate.create' })}
                        </Button>,
                    ],
                    cellActions: (record) => [
                        {
                            label: intl.formatMessage({ id: 'component.operate.delete' }),
                            key: 'delete',
                            onClick: (row) => {
                                const { id } = row;
                                Modal.confirm({
                                    title: intl.formatMessage({
                                        id: 'component.areYouSureYouWantToDeleteIt',
                                    }),
                                    onOk: async () => {
                                        await deleteQueueReason(id);
                                        await tableRef.current?.reload?.();
                                    },
                                });
                            },
                        },
                        {
                            label: intl.formatMessage({ id: 'component.operate.edit' }),
                            key: 'edit',
                            onClick: (row) => {
                                actionsRef.current.editRef.changeOpen?.(
                                    row as API.QueueReason.Response,
                                );
                            },
                        },
                    ],
                    request: async ({ current, pageSize }) => {
                        const params = await searchFormRef.current?.validateFields();
                        const { data, total } = await getPage({
                            ...params,
                            page: current,
                            size: pageSize,
                        });
                        return {
                            data,
                            total,
                            success: true,
                        };
                    },
                    columns: [
                        {
                            title: '图例',
                            dataIndex: 'icon',
                            render: (text, record) => {
                                const color = colorMap[record.reasonType] || '#ccc'; // 默认灰色
                                return (
                                    <div
                                        style={{
                                            width: 40,
                                            height: 20,
                                            borderRadius: 4,
                                            background: color,
                                            border: '1px solid #eee',
                                            display: 'inline-block',
                                        }}
                                    />
                                );
                            },
                        },
                        {
                            title: intl.formatMessage({
                                id: 'pages.resourceCluster.schedulingSystem',
                            }),
                            dataIndex: 'schedulingSystem',
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.job.queueReason' }),
                            dataIndex: 'reasonDesc',
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.job.queueReason.reasonType' }),
                            dataIndex: 'reasonType',
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.job.queueReason.reasonKey' }),
                            dataIndex: 'reasonKey',
                        },
                    ],
                }}
            />
            <AddQueueReason actions={actionsRef.current.addRef} tableRef={tableRef} />
            <EditQueueReason actions={actionsRef.current.editRef} tableRef={tableRef} />
        </>
    );
};
