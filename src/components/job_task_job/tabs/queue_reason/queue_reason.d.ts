namespace API.QueueReason {
    interface Request extends IApiReqPageParams {}
    interface Response {
        id: number;
        createdDate: Date;
        reasonDesc: string;
        reasonKey: string;
        reasonType: string;
        schedulingSystem: string;
        updatedDate: Date;
    }
}

namespace API.QueueReason.Create {
    interface Request {
        reasonDesc: string;
        reasonKey: string;
        reasonType: string;
    }
}

namespace API.QueueReason.Update {
    interface Request {
        id: number;
        reasonDesc?: string;
        reasonKey?: string;
        reasonType?: string;
    }
}

namespace API.QueueReason.Delete {
    interface Request {
        id: number;
    }
}

namespace API.QueueReason.TypeList {
    interface Response {
        reasonTypes: string[];
    }
}
