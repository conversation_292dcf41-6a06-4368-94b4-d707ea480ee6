import { BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { actionsType } from './job_status_page';
import { formItemLayout } from '@/utils/form_layout';
import { jobStatusApi } from './job_status_api';

/**
 * 切换优先级
 */
export const Priority = ({ actions }: { actions: React.MutableRefObject<actionsType> }) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const formRef = useRef<ProFormInstance>();
    Object.assign(actions.current.priorityActions, {
        openModal: async (row: API.IApiResGetJobStatusPage) => {
            await setOpen(true);
            await formRef.current?.setFieldsValue({
                rawJobId: row.rawJobId,
                higher: row.higher,
            });
        },
    });

    const onSubmit = async () => {
        const params = formRef.current?.getFieldsFormatValue?.();
        await jobStatusApi.changePriority(params);
        await actions.current.tableActions.current?.reload();
        return await setOpen(false);
    };

    return (
        <BetaSchemaForm
            {...formItemLayout}
            title={intl.formatMessage({ id: 'pages.job.changePriorityReason' })}
            open={open}
            layoutType="ModalForm"
            layout="horizontal"
            formRef={formRef}
            onFinish={onSubmit}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                    formRef.current?.resetFields();
                },
            }}
            columns={[
                {
                    title: intl.formatMessage({ id: 'pages.log.jobId' }),
                    dataIndex: 'rawJobId',
                    readonly: true,
                },
                {
                    title: intl.formatMessage({ id: 'pages.job.priority' }),
                    dataIndex: 'higher',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                    fieldProps: {
                        style: {
                            width: '100%',
                        },
                    },
                    valueType: 'select',
                    valueEnum: new Map([
                        [1, 1],
                        [2, 2],
                        [3, 3],
                        [4, 4],
                        [5, 5],
                        [6, 6],
                        [7, 7],
                        [8, 8],
                        [9, 9],
                        [20, 20],
                        [30, 30],
                    ]),
                },
            ]}
        />
    );
};
