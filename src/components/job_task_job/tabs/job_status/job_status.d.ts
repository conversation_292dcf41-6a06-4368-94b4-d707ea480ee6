namespace API {
    namespace SuspendJob {
        interface Request {
            jobId: string;
        }
    }
    namespace ResumeJob {
        interface Request {
            jobId: string;
        }
    }
    namespace QuickCalculate {
        interface Request {
            jobId: string;
            queue: string;
        }
    }
    namespace ChangePriority {
        interface Request {
            jobId: string;
            higher: number;
        }
    }
    namespace GetQueue {
        interface Request {
            jobId: string;
        }
        interface Response {
            queue: string;
            queueInfo: {
                queue: string;
            };
        }
    }
}
