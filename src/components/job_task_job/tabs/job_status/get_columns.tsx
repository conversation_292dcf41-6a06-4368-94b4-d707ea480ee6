import { createJobStatusValueEnum } from '@/valueEnum/job_status';
import { ProColumns, ProFormColumnsType, ProFormInstance } from '@ant-design/pro-components';
import { getIntl, getLocale } from '@umijs/max';
import dayjs from 'dayjs';
import { createJobSyncStatusValueEnum } from '@/valueEnum/job_sync_status';
import GroupSelector from '@/components/admin_group_manage/group_selector';
import { Divider, Popover, Space } from 'antd';
import { MutableRefObject } from 'react';
import { useCommonStore } from '@/components/job_task_job/common_store';
import { thousandthDivision } from '@/utils/utils';
import { timeFormat } from '@/utils/time_format';

const intl = getIntl(getLocale());

export const getSearchColumns = (
    formRef: MutableRefObject<ProFormInstance | undefined>,
): ProFormColumnsType<any>[] => {
    const {
        allZoneCode,
        getZoneCode,
        allPlatforms,
        getPlatforms,
        getQueues,
        allQueues,
        allApps,
        getApps,
        allJobUsers,
        getJobUsers,
        allUids,
        getUids,
    } = useCommonStore();

    return [
        {
            title: intl.formatMessage({ id: 'pages.job.zone' }),
            dataIndex: 'zones',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('zones', allZoneCode);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getZoneCode,
        },
        {
            title: intl.formatMessage({ id: 'pages.log.jobId' }),
            dataIndex: 'rawJobIdSearch',
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.supercomputerAccount' }),
            dataIndex: 'jobUsers',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('jobUsers', allJobUsers);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getJobUsers,
        },
        {
            title: intl.formatMessage({ id: 'menu.resource.queue' }),
            dataIndex: 'queues',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('queues', allQueues);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getQueues,
        },
        {
            title: intl.formatMessage({ id: 'pages.resourceQueue.terrace' }),
            dataIndex: 'platforms',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('platforms', allPlatforms);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getPlatforms,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.jobStatus' }),
            dataIndex: 'jobStatus',
            valueType: 'select',
            width: 150,
            fieldProps: {
                mode: 'multiple',
                maxTagCount: 0,
            },
            initialValue: ['PENDING', 'RUN', 'WAITING'],
            valueEnum: createJobStatusValueEnum(),
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.queueReason' }),
            dataIndex: 'pendReasonKeys',
            width: 150,
            fieldProps: {
                mode: 'multiple',
                maxTagCount: 0,
            },
            valueType: 'select',
            valueEnum: {
                'resourceLimit,queueLimit': {
                    text: intl.formatMessage({ id: 'pages.billing.resourceQueuing' }),
                },
                'personalLimit,jobOver': {
                    text: intl.formatMessage({ id: 'pages.billing.quotaQueuing' }),
                },
                'licenseLimit,licenseOver': {
                    text: intl.formatMessage({ id: 'pages.billing.licenseQueue' }),
                },
                errorLimit: {
                    text: intl.formatMessage({ id: 'pages.billing.exceptionQueuing' }),
                },
                otherLimit: {
                    text: intl.formatMessage({ id: 'pages.billing.otherQueues' }),
                },
            },
        },
        {
            title: intl.formatMessage({ id: 'menu.app.icon' }),
            dataIndex: 'softwares',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('softwares', allApps);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getApps,
        },
        {
            title: intl.formatMessage({ id: 'pages.monitorsSession.department' }),
            dataIndex: 'gids',
            renderFormItem: () => <GroupSelector />,
        },
        {
            title: intl.formatMessage({ id: 'menu.organization.user' }),
            dataIndex: 'uids',
            fieldProps: {
                style: {
                    width: 200,
                },
                mode: 'multiple',
                maxTagCount: 0,
                dropdownRender: (menu: any) => {
                    return (
                        <>
                            <Space
                                style={{
                                    padding: '0 0 0 10px',
                                    width: 300,
                                    cursor: 'pointer',
                                }}
                                onClick={async () => {
                                    formRef.current?.setFieldValue('uids', allUids);
                                }}
                            >
                                {intl.formatMessage({
                                    id: 'component.select.all',
                                })}
                            </Space>
                            <Divider style={{ margin: '8px 0' }} />
                            {menu}
                        </>
                    );
                },
            },
            request: getUids,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.showSyncStatus' }),
            dataIndex: 'showSyncStatus',
            valueType: 'select',
            width: 150,
            fieldProps: {
                mode: 'multiple',
                maxTagCount: 0,
            },
            valueEnum: createJobSyncStatusValueEnum,
        },
    ];
};

export const getTableColumns = (
    zoneNameValueEnum?: {
        label: string;
        value: string;
    }[],
): ProColumns<API.IApiResGetJobStatusPage>[] => {
    return [
        {
            title: intl.formatMessage({ id: 'pages.job.jobId' }),
            dataIndex: 'jobId',
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.log.jobId' }),
            dataIndex: 'rawJobId',
            sorter: true,
            width: 'max-content',
            align: 'right',
            fixed: 'left',
        },
        {
            title: intl.formatMessage({ id: 'pages.job.zone' }),
            dataIndex: 'zone',
            sorter: true,
            renderText: (zone: string) => {
                const zoneName = zoneNameValueEnum?.find((item) => item.value === zone);
                return zoneName ? zoneName.label : zone;
            },
        },
        {
            title: '实际分区',
            dataIndex: 'zoneCode',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.supercomputerAccount' }),
            dataIndex: 'jobUser',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'menu.organization.user' }),
            dataIndex: 'uid',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.resourceQueueResource.queueName' }),
            dataIndex: 'queue',
            sorter: true,
        },
        {
            title: '平台名称',
            dataIndex: 'platform',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.jobName' }),
            dataIndex: 'jobName',
            sorter: true,
            render(dom) {
                return (
                    <span
                        style={{
                            display: 'block',
                            width: '20em',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                        }}
                    >
                        {dom}
                    </span>
                );
            },
        },
        {
            title: '节点数量',
            dataIndex: 'nodesLength',
            sorter: true,
            align: 'right',
            renderText: (_, row) => (row.nodes === '' ? '-' : row?.nodes?.split('+').length),
        },
        {
            title: '节点列表',
            dataIndex: 'nodes',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.slots' }),
            dataIndex: 'slots',
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.order.Ngpus' }),
            dataIndex: 'ngpus',
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.queuingTime' }),
            dataIndex: 'pendingTime',
            renderText: (pendingTime: number) => timeFormat(pendingTime),
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.job.walltime' }),
            dataIndex: 'walltime',
            renderText: (walltime) => timeFormat(walltime, 'seconds'),
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.job.higher' }),
            dataIndex: 'higher',
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.job.jobStatus' }),
            dataIndex: 'jobStatus',
            valueEnum: createJobStatusValueEnum(),
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.queueReason' }),
            dataIndex: 'pendReason',
            render: (_, row) => {
                return <Popover content={row.pendReasonDesc}>{row.pendReason}</Popover>;
            },
        },
        {
            title: intl.formatMessage({ id: 'pages.job.appCode' }),
            dataIndex: 'appCode',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.softwareVersion' }),
            dataIndex: 'version',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.appVersion.appSubmitType' }),
            dataIndex: 'appSubmitType',
            sorter: true,
            renderText: (appSubmitType: string) =>
                appSubmitType === 'VIEW'
                    ? intl.formatMessage({ id: 'pages.job.type.viewJob' })
                    : appSubmitType === 'BATCH'
                      ? intl.formatMessage({ id: 'pages.job.type.batchJob' })
                      : intl.formatMessage({ id: 'pages.job.type.cmdJob' }),
        },
        {
            title: '批量提交',
            dataIndex: 'isBatch',
            renderText: (isBatch: boolean) => (isBatch ? '是' : '否'),
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.billing.queueType' }),
            dataIndex: 'queueType',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.calculationAccuracy' }),
            dataIndex: 'calculationAccuracy',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.createdDate' }),
            dataIndex: 'createdDate',
            valueType: 'dateTime',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.submitTime' }),
            dataIndex: 'submitTime',
            valueType: 'dateTime',
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'pages.job.showSyncStatus' }),
            dataIndex: 'showSyncStatus',
            valueEnum: createJobSyncStatusValueEnum(),
            sorter: true,
        },
        {
            title: '上同排队时长',
            dataIndex: 'uploadPendingTime',
            valueType: 'dateTime',
        },

        {
            title: '上同开始时间',
            dataIndex: 'uploadStartDate',
            valueType: 'dateTime',
        },

        {
            title: '上同结束时间',
            dataIndex: 'uploadEndDate',
            valueType: 'dateTime',
        },
        {
            title: intl.formatMessage({ id: 'pages.jobTag.uploadTime' }),
            dataIndex: 'uploadTime',
            renderText: (uploadTime: number) =>
                uploadTime && dayjs.duration(uploadTime, 'millisecond').format('HH:mm:ss'),
            sorter: true,
            align: 'right',
        },
        {
            title: '下同排队时长',
            dataIndex: 'downloadPendingTime',
            valueType: 'dateTime',
        },

        {
            title: '下同开始时间',
            dataIndex: 'downloadStartDate',
            valueType: 'dateTime',
        },
        {
            title: '下同结束时间',
            dataIndex: 'downloadEndDate',
            valueType: 'dateTime',
        },
        {
            title: intl.formatMessage({ id: 'pages.jobTag.downloadTime' }),
            dataIndex: 'downloadTime',
            renderText: (downloadTime: number) =>
                downloadTime && dayjs.duration(downloadTime, 'millisecond').format('HH:mm:ss'),
            sorter: true,
            align: 'right',
        },
        {
            title: intl.formatMessage({ id: 'pages.jobTag.totalInputFileSize' }),
            dataIndex: 'totalInputFileSize',
            align: 'right',
            renderText: (totalInputFileSize) =>
                thousandthDivision(totalInputFileSize / 1024 / 1024),
        },
        {
            title: intl.formatMessage({ id: 'pages.jobTag.totalOutputFileSize' }),
            dataIndex: 'totalOutputFileSize',
            align: 'right',
            renderText: (totalOutputFileSize) =>
                thousandthDivision(totalOutputFileSize / 1024 / 1024),
        },
        {
            title: intl.formatMessage({ id: 'pages.jobTag.totalSyncFileSize' }),
            dataIndex: 'totalSyncFileSize',
            align: 'right',
            renderText: (totalSyncFileSize) => thousandthDivision(totalSyncFileSize / 1024 / 1024),
        },

        {
            title: '失败原因',
            dataIndex: 'msg',
        },
        {
            title: '定时时间',
            dataIndex: 'executionTime',
        },
        {
            title: '许可来源',
            dataIndex: 'TODO', // 后端无法获取
        },
        {
            title: '项目名称',
            dataIndex: 'projectName',
        },
    ];
};
