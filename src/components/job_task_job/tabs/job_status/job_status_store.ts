import { useState } from 'react';
import { jobStatusApi } from './job_status_api';
import { downLoadBlob } from '@/components/billing_center/helper';

export const useJobStatus = () => {
    const [loading, setLoading] = useState<boolean>(false);
    const [cpuSum, setCpuSum] =
        useState<
            Record<'RUN' | 'PENDING' | 'WAITING' | 'EXECUTION' | 'STAGEOUT', API.IApiResGetCpuSum>
        >();
    const getPage = async (params: API.IApiReqGetJobStatusPage) => {
        const sum = await jobStatusApi.getCpuSum(params);
        const { data, total } = await jobStatusApi.getJobStatusPage(params);
        setCpuSum(sum.jobCountSummaryMap);
        return { data, total };
    };

    const exportJobStatus = async (params: API.IApiReqGetJobStatusPage) => {
        setLoading(true);
        const res = await jobStatusApi.exportJobStatus(params).finally(() => setLoading(false));
        return await downLoadBlob(res.headers['content-disposition'], res.data);
    };
    const suspendJob = async (params: API.SuspendJob.Request) => {
        return await jobStatusApi.suspendJob(params);
    };
    const resumeJob = async (params: API.ResumeJob.Request) => {
        return await jobStatusApi.resumeJob(params);
    };

    const getQueue = async (params: API.GetQueue.Request) => {
        const res = await jobStatusApi.getQueue(params);
        return res.map((item) => {
            return {
                label: item.queueInfo.queue,
                value: item.queue,
            };
        });
    };
    const quickCalculate = async (params: API.QuickCalculate.Request) => {
        return await jobStatusApi.quickCalculate(params);
    };
    return {
        getPage,
        exportJobStatus,
        loading,
        cpuSum,
        suspendJob,
        resumeJob,
        getQueue,
        quickCalculate,
    };
};
