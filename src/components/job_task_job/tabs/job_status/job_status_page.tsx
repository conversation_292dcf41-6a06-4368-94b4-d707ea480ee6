import SearchTablePage from '@/components/search_table_page';
import { ActionType, ColumnsState, ProFormInstance } from '@ant-design/pro-components';
import { useEffect, useRef, useState } from 'react';
import { getSearchColumns, getTableColumns } from './get_columns';
import { useIntl } from '@umijs/max';
import { thousandthDivision } from '@/utils/utils';
import { Button, Collapse, Drawer, message, Modal } from 'antd';
import { putAdminJobCancel } from '@/services/api';
import { useJobStatus } from './job_status_store';
import { QuickCalculate } from './quickCalculate';
import { Priority } from './priority';
import JobDetailModal from '../job_manage/job_detail_modal';
import { IJobActions } from '../job_manage/types';
import MonitorChartsModal from '../job_manage/monitor_charts_modal';
import { createZoneNameValueEnum } from '@/valueEnum/zone_name';
import { useHistoryJobStore } from '../history_job/history_job_store';

export interface IQuickCalculate {
    openModal?: (row: API.IApiResGetJobStatusPage) => void;
}

export interface actionsType {
    tableActions: React.MutableRefObject<ActionType | undefined>;
    quickCalculateActions: IQuickCalculate;
    priorityActions: IQuickCalculate;
    detail: IJobActions;
    resourceMonitor: IJobActions;
}

const intiColumnsStateMap = {
    jobId: {
        show: false,
    },
    zoneCode: {
        show: false,
    },
    platform: {
        show: false,
    },
    nodes: {
        show: false,
    },
    version: {
        show: false,
    },
    appSubmitType: {
        show: false,
    },
    isBatch: {
        show: false,
    },
    queueType: {
        show: false,
    },
    calculationAccuracy: {
        show: false,
    },
    createdDate: {
        show: false,
    },
    submitTime: {
        show: false,
    },
    showSyncStatus: {
        show: false,
    },
    uploadPendingTime: {
        show: false,
    },
    uploadStartDate: {
        show: false,
    },
    uploadEndDate: {
        show: false,
    },
    uploadTime: {
        show: false,
    },
    downloadPendingTime: {
        show: false,
    },
    downloadStartDate: {
        show: false,
    },
    downloadEndDate: {
        show: false,
    },
    downloadTime: {
        show: false,
    },
    totalInputFileSize: {
        show: false,
    },
    totalOutputFileSize: {
        show: false,
    },
    totalSyncFileSize: {
        show: false,
    },
    msg: {
        show: false,
    },
    executionTime: {
        show: false,
    },
    TODO: {
        show: false,
    },
};

const JobStatusPage = () => {
    const intl = useIntl();
    const { getPage, cpuSum, exportJobStatus, loading, suspendJob, resumeJob } = useJobStatus();
    const { getLogFile } = useHistoryJobStore();
    const [columnsStateMap, setColumnsStateMap] =
        useState<Record<string, ColumnsState>>(intiColumnsStateMap);
    const sumKeys = ['RUN', 'PENDING', 'WAITING', 'EXECUTION', 'STAGEOUT'] as const;
    const [zoneNameValueEnum, setZoneNameValueEnum] = useState([]);
    const [open, setOpen] = useState(false);
    const [value, setValue] = useState<string>('');
    const [logName, setLogName] = useState<string>('');

    const tableActions = useRef<ActionType>();
    const searchRef = useRef<ProFormInstance>();
    const actionsRef = useRef<actionsType>({
        tableActions: tableActions,
        quickCalculateActions: {},
        priorityActions: {},
        detail: {},
        resourceMonitor: {},
    });

    useEffect(() => {
        createZoneNameValueEnum().then((r) => {
            setZoneNameValueEnum(r);
        });
    }, []);

    return (
        <>
            <SearchTablePage<API.IApiResGetJobStatusPage, API.IApiReqGetJobStatusPage>
                style={{
                    border: true,
                }}
                rightMenu
                searchProps={{
                    formRef: searchRef,
                    columns: getSearchColumns(searchRef),
                    onFinish: async () => {
                        return tableActions.current?.reloadAndRest?.();
                    },
                    submitter: {
                        render: (_, dom) => {
                            return [
                                ...dom,
                                <Button
                                    loading={loading}
                                    type="primary"
                                    onClick={async () => {
                                        const searchParams =
                                            await searchRef.current?.getFieldFormatValue?.();
                                        await exportJobStatus({ ...searchParams });
                                    }}
                                >
                                    {intl.formatMessage({ id: 'component.table.export' })}
                                </Button>,
                            ];
                        },
                    },
                }}
                tableProps={{
                    columnsState: {
                        value: columnsStateMap,
                        onChange: setColumnsStateMap,
                    },
                    headerTitle: (
                        <div
                            style={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                gap: '8px',
                                fontSize: '14px',
                                fontWeight: 'normal',
                            }}
                        >
                            {sumKeys.map((key) => {
                                if (!cpuSum) return null;
                                const item = cpuSum[key];
                                return (
                                    <div
                                        style={{ paddingRight: '15px', cursor: 'pointer' }}
                                        onClick={async () => {
                                            await searchRef.current?.setFieldValue(
                                                'jobStatus',
                                                key,
                                            );
                                            await searchRef.current?.submit();
                                        }}
                                    >
                                        <span>
                                            {intl.formatMessage({
                                                id: `pages.jobStatus.${item.jobStatus}`,
                                            })}
                                            ｜
                                            {intl.formatMessage({ id: 'pages.jobTag.coreNumber' })}
                                        </span>
                                        :
                                        <span>
                                            <strong
                                                style={{
                                                    color: `${key === 'RUN' ? 'green' : 'red'}`,
                                                    fontWeight: 'bold',
                                                }}
                                            >
                                                {thousandthDivision(item.jobNum, 0)}
                                            </strong>
                                            ｜
                                            <strong
                                                style={{
                                                    color: `${key === 'RUN' ? 'green' : 'red'}`,
                                                    fontWeight: 'bold',
                                                }}
                                            >
                                                {thousandthDivision(item.cpuNum, 0)}
                                            </strong>
                                        </span>
                                    </div>
                                );
                            })}
                        </div>
                    ),
                    actionRef: tableActions,
                    columns: getTableColumns(zoneNameValueEnum),
                    headerActions: () => [
                        {
                            key: '取消作业',
                            label: intl.formatMessage({ id: 'pages.job.cancel' }),
                            onClick: (row) => {
                                Modal.confirm({
                                    content: intl.formatMessage({
                                        id: 'pages.job.areYouSureYouWantCancel',
                                    }),
                                    autoFocusButton: 'cancel',
                                    okButtonProps: { danger: true },
                                    okText: intl.formatMessage({ id: 'component.modal.ok' }),
                                    cancelText: intl.formatMessage({
                                        id: 'component.modal.cancel',
                                    }),
                                    onOk: async () => {
                                        await putAdminJobCancel(row.jobId);
                                        tableActions.current?.reload?.();
                                    },
                                });
                            },
                        },
                    ],
                    cellActions: (record) => [
                        {
                            key: '详情',
                            label: intl.formatMessage({ id: 'component.modal.details' }),
                            onClick: (row) => {
                                actionsRef.current.detail?.openModal?.(row);
                            },
                        },
                        {
                            key: '取消作业',
                            label: intl.formatMessage({ id: 'pages.job.cancel' }),
                            disabled: ({ props }) => {
                                return !['PENDING', 'RUN'].includes(props.jobStatus || ''); // 云端作业本地作业均可取消（消息来源hmj）
                            },
                            onClick: (row) => {
                                Modal.confirm({
                                    content: intl.formatMessage({
                                        id: 'pages.job.areYouSureYouWantCancel',
                                    }),
                                    autoFocusButton: 'cancel',
                                    okButtonProps: { danger: true },
                                    okText: intl.formatMessage({ id: 'component.modal.ok' }),
                                    cancelText: intl.formatMessage({
                                        id: 'component.modal.cancel',
                                    }),
                                    onOk: async () => {
                                        await putAdminJobCancel(row.jobId);
                                        tableActions.current?.reload?.();
                                    },
                                });
                            },
                        },
                        {
                            key: '挂起作业',
                            disabled: ({ props }) => {
                                return (
                                    props.cloud ||
                                    !['PENDING', 'RUN'].includes(props.jobStatus || '') ||
                                    props.jobType === 'CMD'
                                );
                            },
                            label: intl.formatMessage({ id: 'pages.job.hangUp' }),
                            onClick: (row) => {
                                Modal.confirm({
                                    content: '您确定要挂起作业吗？',
                                    onOk: async () => {
                                        if (row.jobId) {
                                            await suspendJob({ jobId: row.jobId });
                                            tableActions.current?.reload?.();
                                        }
                                    },
                                });
                            },
                        },
                        {
                            key: '释放作业',
                            disabled: ({ props }) => {
                                return (
                                    props.cloud || !['SUSPENDED'].includes(props.jobStatus || '')
                                );
                            },
                            label: intl.formatMessage({ id: 'pages.job.release' }),
                            onClick: (row) => {
                                if (row.jobId) {
                                    resumeJob({ jobId: row.jobId });
                                    tableActions.current?.reload?.();
                                }
                            },
                        },
                        {
                            key: '查看作业日志',
                            label: intl.formatMessage({ id: 'pages.job.viewJobLog' }),
                            disabled: ({ props }) =>
                                props.appCode === 'APP_CMD' ||
                                !['RUN', 'SUSPENDED'].includes(props.jobStatus),
                            onClick: async (row) => {
                                if (row.localLogPath === null) {
                                    message.warning('作业没有日志文件');
                                    return;
                                }
                                const res = await getLogFile(row.jobId, row.localLogPath as string);
                                setValue(res);
                                const logName = row.localLogPath?.split('/')?.pop();
                                setLogName(logName as string);
                                setOpen(true);
                            },
                        },
                        {
                            key: '调整作业优先级',
                            label: intl.formatMessage({ id: 'pages.job.changePriorityReason' }),
                            disabled: ({ props }) =>
                                props.cloud ||
                                props.jobStatus !== 'PENDING' ||
                                props.appCode === 'APP_CMD',
                            onClick: (row) => {
                                actionsRef.current.priorityActions.openModal?.(row);
                            },
                        },
                        {
                            key: '查看结果文件(3DViewer)',
                            label: intl.formatMessage({ id: 'pages.job.view3dViewer' }),
                            disabled: true,
                        },
                        {
                            key: '查看运行特征',
                            label: intl.formatMessage({ id: 'pages.job.viewRunCharacteristics' }),
                            onClick: (row) => {
                                actionsRef.current.resourceMonitor?.openModal?.(row);
                            },
                        },
                        {
                            key: '快算',
                            label: '手动快算',
                            disabled: ({ props }) => props.cloud || props.jobStatus !== 'PENDING',
                            onClick: (row) => {
                                actionsRef.current.quickCalculateActions.openModal?.(row);
                            },
                        },
                    ],
                    request: async (params, sort) => {
                        const { pageSize: size, current: page } = params;
                        const orderBy = Object.keys(sort)[0];
                        const order = sort[orderBy] === 'ascend' ? 'ASC' : 'DESC';
                        const searchParams = searchRef.current?.getFieldFormatValue?.();
                        const { data, total } = await getPage({
                            size,
                            page,
                            orderBy,
                            order,
                            ...searchParams,
                        });
                        return {
                            data,
                            total,
                        };
                    },
                }}
            />
            <Drawer
                title="日志"
                width={1200}
                open={open}
                onClose={() => {
                    setOpen(false);
                }}
            >
                <Collapse
                    accordion
                    activeKey={logName}
                    items={[
                        {
                            key: logName,
                            label: logName,
                            children: (
                                <pre
                                    style={{
                                        height: '550px',
                                        backgroundColor: '#000',
                                        color: '#fff',
                                        fontWeight: 'bolder',
                                        border: 'none',
                                        overflow: 'auto',
                                        padding: '10px',
                                        margin: '0',
                                        fontFamily: 'monospace, monospace',
                                        fontSize: '1em',
                                    }}
                                >
                                    {value}
                                </pre>
                            ),
                        },
                    ]}
                />
            </Drawer>
            <QuickCalculate actions={actionsRef} />
            <Priority actions={actionsRef} />
            <JobDetailModal action={actionsRef.current.detail} />
            <MonitorChartsModal action={actionsRef.current.resourceMonitor} />
        </>
    );
};

export default JobStatusPage;
