import {
    getCpuSum,
    getJobStatusPage,
    getJobStatusRunExport,
} from '@/services/api/admin_job_status';
import { request } from '@umijs/max';

export const jobStatusApi = {
    getJobStatusPage: async (params: API.IApiReqGetJobStatusPage) => await getJobStatusPage(params),
    getCpuSum: async (params: API.IApiReqGetJobStatusPage) => await getCpuSum(params),
    exportJobStatus: async (params: API.IApiReqGetJobStatusPage) =>
        await getJobStatusRunExport(params),
    suspendJob: async (params: API.SuspendJob.Request) => {
        return await request(`admin/job/${params.jobId}/suspend`, {
            method: 'PUT',
            params,
        });
    },
    resumeJob: async (params: API.ResumeJob.Request) => {
        return await request(`admin/job/${params.jobId}/resume`, {
            method: 'PUT',
            params,
        });
    },
    getQueue: async (params: API.GetQueue.Request) => {
        return await request<API.GetQueue.Response[]>(`admin/job/${params.jobId}/change/queue`, {
            method: 'GET',
            params,
        });
    },
    quickCalculate: async (params: API.QuickCalculate.Request) => {
        return await request(`admin/job/${params.jobId}/change/queue`, {
            method: 'PUT',
            params,
        });
    },
    changePriority: async (params: API.ChangePriority.Request) => {
        return await request(`admin/job/${params.jobId}/higher`, {
            method: 'PUT',
            params,
        });
    },
};
