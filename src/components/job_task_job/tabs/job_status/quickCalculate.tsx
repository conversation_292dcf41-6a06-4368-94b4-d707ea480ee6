import { BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { actionsType } from './job_status_page';
import { formItemLayout } from '@/utils/form_layout';
import { useJobStatus } from './job_status_store';
/**
 * 快速计算
 */
export const QuickCalculate = ({ actions }: { actions: React.MutableRefObject<actionsType> }) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [info, setInfo] = useState<API.IApiResGetJobStatusPage>();
    const formRef = useRef<ProFormInstance>();
    const { getQueue, quickCalculate } = useJobStatus();
    Object.assign(actions.current.quickCalculateActions, {
        openModal: async (row: API.IApiResGetJobStatusPage) => {
            await setInfo(row);
            await setOpen(true);
            await formRef.current?.setFieldsValue({
                jobId: row.jobId,
                queue: row.queue,
            });
        },
    });

    const onSubmit = async () => {
        const params = formRef.current?.getFieldsFormatValue?.();
        await quickCalculate(params);
        setOpen(false);
        actions.current.tableActions?.current?.reload();
    };

    return (
        <BetaSchemaForm
            {...formItemLayout}
            title={intl.formatMessage({ id: 'pages.job.quickCalculate' })}
            open={open}
            layoutType="ModalForm"
            layout="horizontal"
            formRef={formRef}
            onFinish={onSubmit}
            clearOnDestroy
            modalProps={{
                onCancel: () => {
                    formRef.current?.resetFields();
                    setOpen(false);
                },
                destroyOnClose: true,
            }}
            columns={[
                {
                    title: intl.formatMessage({ id: 'pages.log.jobId' }),
                    dataIndex: 'jobId',
                    readonly: true,
                },
                {
                    title: intl.formatMessage({ id: 'menu.resource.queue' }),
                    dataIndex: 'queue',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                    valueType: 'select',
                    request: async () => {
                        const res = await getQueue({
                            jobId: info?.jobId || '',
                        });
                        return res;
                    },
                },
            ]}
        />
    );
};
