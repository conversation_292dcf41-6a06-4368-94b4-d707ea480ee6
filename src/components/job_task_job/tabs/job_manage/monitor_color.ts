/* cpu usage */
export const CPU_USAGE = 'rgb(0, 128, 0)';

/* Gflops */
export const GFLOPS = 'rgb(0, 255, 0)';

/* swap usage */
export const SWAP_USAGE = 'rgb(255, 0, 0)';

/* memory usage */
export const MEMORY_USAGE = 'rgb(128, 128, 0)';

/* memory read and write */
export const MEMORY_RW = 'rgb(192, 192, 0)';

/* diskRead */
export const DISK_READ = 'rgb(255, 128, 0)';

/* diskWrite */
export const DISK_WRITE = 'rgb(0, 128, 255)';

/* netReceive */
export const NET_RECEIVE = 'rgb(0, 255, 255)';

/* netSend */
export const NET_SEND = 'rgb(255, 0, 255)';

/* IB Receive */
export const IB_RECEIVE = 'rgb(128, 223, 223)';

/* IB Send */
export const IB_SEND = 'rgb(223, 128, 223)';
