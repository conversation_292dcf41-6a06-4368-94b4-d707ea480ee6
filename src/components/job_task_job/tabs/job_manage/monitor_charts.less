.jobOutputDetailsMonitorChartsModal {
    > :nth-child(2) {
        width: 100%;
        height: 100%;
        > .ant-modal-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;

            > .ant-modal-body {
                flex-grow: 1;
                overflow: hidden;
            }
        }
    }
}

.jobOutputDetailsMonitorCharts {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;

    > .ant-spin-nested-loading,
    > .ant-spin-nested-loading > .ant-spin-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .controller {
        padding: 5px 0;
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        flex-shrink: 0;
        margin-bottom: 10px;
    }

    .controller-right {
        text-align: right;
    }
    .controller-right > * {
        margin-left: 10px;
    }

    .controller-right-float {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
    }
    .controller-right-float > * {
        margin-left: 10px;
    }

    .jobMonitorChart {
        flex-grow: 1;
        flex-shrink: 0;
        flex-basis: 220px;
    }
}

.jobOutputDetailsMonitorTables {
    margin-bottom: 10px;
    &.ant-tabs {
        max-height: 30%;

        .ant-tabs-content,
        .ant-tabs-tabpane {
            height: 100%;
        }

        .ant-tabs-tabpane {
            overflow: auto;
        }
    }
    &.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
        padding: 5px 8px;
    }
    .ant-table table {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        border-spacing: 0;
        border-collapse: separate;
        width: 100%;
    }
    .ant-table-wrapper .ant-table-thead > tr > th,
    .ant-table-wrapper .ant-table-thead > tr > td {
        font-weight: 500;
        font-size: 12px;
    }
    .ant-table-wrapper .ant-table.ant-table-small .ant-table-cell,
    .ant-table-wrapper .ant-table.ant-table-small .ant-table-thead > tr > th {
        padding: 2.5px 4px;
    }
    .ant-table-wrapper .ant-table.ant-table-small {
        font-size: 12px;
        font-family:
            Helvetica Neue,
            Helvetica,
            Arial,
            sans-serif;
    }
    .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell {
        background: none;
    }
    .ant-table-wrapper .ant-table-tbody .ant-table-row-hover > td,
    .ant-table-wrapper .ant-table-tbody :hover > td {
        background: none;
    }
    .ant-table-wrapper
        .ant-table-tbody
        > tr:nth-child(odd).ant-table-row.ant-table-row-selected
        > .ant-table-cell,
    .ant-table-wrapper .ant-table-tbody > tr:nth-child(odd).ant-table-row-hover > td,
    .ant-table-wrapper .ant-table-tbody > tr:nth-child(odd):hover > td,
    .ant-table-wrapper .ant-table-tbody > tr:nth-child(odd) td {
        background: #f5f5f5;
    }
}

.jobOutputDetailsMonitorChartsContextmenuContainer {
    position: absolute;
    left: 0;
    top: 0;
}
