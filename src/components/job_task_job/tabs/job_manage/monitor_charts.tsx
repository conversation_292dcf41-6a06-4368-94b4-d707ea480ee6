import { useIntl } from '@umijs/max';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useFullscreen, useMemoizedFn, useRequest } from 'ahooks';
import { Spin, Tooltip } from 'antd';
import {
    FullscreenExitOutlined,
    FullscreenOutlined,
    MinusSquareOutlined,
    PlusSquareOutlined,
} from '@ant-design/icons';
import moment from 'dayjs';

import './monitor_charts.less';

import Echarts from '@/components/para_echarts';
import { getAdminParamonJobMonitor } from '@/services/api';
import { isJobEnd } from './utils';
import { indicatorConfigRecord } from './monitor_indicator_config_records';
import MonitorTables from './monitor_tables';

import type { IMonitorTableCurrentData, IMonitorTableGroupData } from './monitor_tables';
import type { HighlightPayload } from 'echarts';

type IData = {
    echartsOption: echarts.EChartsOption;
    tableGroupData: IMonitorTableGroupData;
};

interface IIndicatorData {
    name: string;
    color: string;
    yAxisIndex: 0 | 1;
    dataMap: Record<string, API.IApiResAdminParamonMetricModel['values']>;
}

const stepOptions = [
    {
        label: '3 秒',
        value: 3,
    },
    {
        label: '1 分钟',
        value: 60,
    },
    {
        label: '3 分钟',
        value: 180,
    },
    {
        label: '5 分钟',
        value: 300,
    },
    {
        label: '15 分钟',
        value: 900,
    },
    {
        label: '30 分钟',
        value: 1800,
    },
    {
        label: '1 小时',
        value: 3600,
    },
];

function axisLabelFormatter(value) {
    return moment(value).format('HH:mm');
}

function getEmptyEchartsOption(): echarts.EChartsOption {
    return {
        aria: {
            enabled: false,
        },
        legend: {
            // @ts-ignore
            animation: false,
            type: 'scroll',
            show: true,
            bottom: 55,
            itemStyle: {
                opacity: 0,
            },
        },
        grid: {
            left: 40,
            right: 40,
            top: 10,
            bottom: 100,
        },
        xAxis: [
            {
                type: 'time',
            },
            {
                type: 'time',
                position: 'bottom',
                axisLine: { show: true, onZero: false },
            },
        ],
        yAxis: [0, 1].map(() => ({
            min: -100,
            max: 100,
            axisLine: { show: true },
            axisTick: { show: true },
            axisLabel: {
                show: true,
                formatter: (value) => Math.abs(value).toFixed(0),
            },
        })),
        series: Object.values(indicatorConfigRecord).map(({ title, color }) => ({
            name: title,
            data: [],
            type: 'line',
            showSymbol: false,
            xAxisIndex: 1,
            yAxisIndex: 0,
            color,
        })),
        dataZoom: [
            {
                type: 'slider',
                xAxisIndex: [0, 1],
                start: 0,
                end: 100,
            },
        ],
    };
}

function getEchartsOption(
    nodes: string[],
    tempDatas: IIndicatorData[],
    chartHeight: number,
    yAxisMax: number,
    preOption?: echarts.EChartsOption,
): echarts.EChartsOption {
    const grid: echarts.GridComponentOption[] = [];
    const xAxis: echarts.XAXisComponentOption[] = [];
    const yAxis: echarts.YAXisComponentOption[] = [];
    const series: echarts.LineSeriesOption[] = [];

    const option: echarts.EChartsOption = {
        aria: {
            enabled: false,
        },
        legend: {
            // @ts-ignore
            animation: false,
            type: 'scroll',
            show: true,
            bottom: 55,
            itemStyle: {
                opacity: 0,
            },
        },
        grid,
        xAxis,
        yAxis,
        series,
        tooltip: {
            trigger: 'axis',
            valueFormatter: (value) => {
                if (typeof value === 'number') {
                    return Math.abs(value).toFixed(2);
                }
                return '';
            },
        },
        dataZoom: [
            {
                type: 'slider',
                xAxisIndex: [],
                start: 0,
                end: 100,
            },
        ],
    };

    const itemPercent = 1 / nodes.length;
    const top = 10;
    const bottom = 100;
    const padding = 25;
    const gridHeight = chartHeight - top - bottom;
    const itemHeight = Math.ceil(gridHeight * itemPercent);
    const yAxisInterval1 = itemHeight <= 80 ? 50 : 25;
    const yAxisInterval2 = itemHeight <= 80 ? yAxisMax / 2 : yAxisMax / 4;

    nodes.forEach((node, i) => {
        grid.push({
            left: 50,
            right: 40,
            top: i * itemHeight + top,
            height: itemHeight - padding,
        });

        xAxis.push({
            gridIndex: i,
            type: 'time',
            axisTick: { show: false },
            axisLabel: { show: false },
        });
        if (i === nodes.length - 1) {
            xAxis.push({
                gridIndex: i,
                type: 'time',
                position: 'bottom',
                axisLine: { show: true, onZero: false },
                axisTick: { show: true },
                axisLabel: { show: true, formatter: axisLabelFormatter },
            });
        }

        yAxis.push({
            name: node,
            nameLocation: 'middle',
            nameGap: 30,
            gridIndex: i,
            min: -100,
            max: 100,
            interval: yAxisInterval1,
            axisLine: { show: true },
            axisTick: { show: true },
            axisLabel: {
                show: true,
                formatter: (value) => Math.abs(value).toFixed(0),
            },
        });
        yAxis.push({
            gridIndex: i,
            min: -yAxisMax,
            max: yAxisMax,
            interval: yAxisInterval2,
            axisLine: { show: true },
            axisTick: { show: true },
            axisLabel: {
                show: true,
                formatter: (value) => Math.abs(value).toFixed(0),
            },
        });

        const xAxisIndex = i === nodes.length - 1 ? i + 1 : i;
        tempDatas.forEach((tempData) => {
            if (!tempData) return;

            series.push({
                name: tempData.name,
                data: tempData.dataMap[node] || [],
                type: 'line',
                showSymbol: false,
                xAxisIndex,
                yAxisIndex: 2 * i + tempData.yAxisIndex,
                color: tempData.color,
            });
        });
    });

    xAxis.forEach((_, i) => {
        option.dataZoom[0].xAxisIndex.push(i);
    });

    if (preOption) {
        if (preOption.legend) {
            option.legend = preOption.legend;
        }
        if (preOption.dataZoom && preOption.dataZoom[0]) {
            option.dataZoom[0].start = preOption.dataZoom[0].start;
            option.dataZoom[0].end = preOption.dataZoom[0].end;
        }
    }

    console.debug('MonitorCharts @getEchartsOption', option);
    return option;
}

function getTableGroupData(nodes: string[], tempMap: Record<string, IIndicatorData>) {
    const groupData = {
        average: [],
        peak: [],
        total: [],
    };

    const indicatorKeys = Object.keys(indicatorConfigRecord);

    const allAvgData = {
        nodename: 'Avg.',
    };
    nodes.forEach((node) => {
        const avgData = {
            nodename: node,
        };
        groupData.average.push(avgData);

        const peakData = {
            nodename: node,
        };
        groupData.peak.push(peakData);

        const totalData = {
            nodename: node,
        };
        groupData.total.push(totalData);

        indicatorKeys.forEach((key) => {
            const tempData = tempMap[key];
            if (!tempData) return;

            let total = 0;
            let peak = 0;
            const nodeIndicatorDatas = tempData.dataMap[node] || [];
            nodeIndicatorDatas.forEach((item) => {
                const itemValue = Math.abs(parseFloat(item[1]));
                total += itemValue;
                peak = Math.max(peak, itemValue);
            });

            const avgVal = total / nodeIndicatorDatas.length;
            avgData[key] = avgVal.toFixed(2);
            peakData[key] = peak.toFixed(2);
            totalData[key] = total.toFixed(2);
            allAvgData[key] = (allAvgData[key] || 0) + avgVal;
        });
    });
    if (nodes.length > 1) {
        indicatorKeys.forEach((key) => {
            if (!allAvgData[key]) return;
            allAvgData[key] = (allAvgData[key] / nodes.length).toFixed(2);
        });
        groupData.average.push(allAvgData);
    }
    console.debug('MonitorCharts @getTableGroupData', groupData);
    return groupData;
}

function getTableCurrentData(
    nodes: string[],
    tempMap: Record<string, IIndicatorData>,
    dataIndex: number,
) {
    const currentData = [];

    const indicatorKeys = Object.keys(indicatorConfigRecord);

    let time = '';

    nodes.forEach((node) => {
        const nodeData = {
            nodename: node,
        };
        currentData.push(nodeData);

        indicatorKeys.forEach((key) => {
            const tempData = tempMap[key];
            if (!tempData) return;

            const nodeIndicatorDatas = tempData.dataMap[node] || [];
            const item = nodeIndicatorDatas[dataIndex];
            if (item) {
                time = moment(item[0]).format('YYYY-MM-DD HH:mm:ss');
                const itemValue = Math.abs(parseFloat(item[1]));
                nodeData[key] = itemValue.toFixed(2);
            }
        });
    });

    console.debug('MonitorCharts @getTableCurrentData', currentData);
    return {
        data: currentData,
        time,
    };
}

export default function MonitorCharts({ job }: { job: API.IApiResJobTaskModel }) {
    const intl = useIntl();
    const [step /* setStep */] = useState(stepOptions[0].value);
    const rootElRef = useRef<HTMLDivElement>();
    const contextmenuContainerElRef = useRef<HTMLDivElement>();
    const abortControllerRef = useRef<AbortController>();
    const echartRef = useRef<echarts.ECharts>();
    const echartSizeRef = useRef<{ width: number; height: number }>();
    const nodesRef = useRef<string[]>();
    const nodesSelectedRef = useRef<string[]>();
    const [selectedNodes, setSelectedNodes] = useState([]);
    const tempMapRef = useRef<Record<string, IIndicatorData>>();
    const tempDatasRef = useRef<IIndicatorData[]>();
    const yAxisMaxRef = useRef<number>(100);
    const emptyData = useMemo<IData>(
        () => ({
            echartsOption: getEmptyEchartsOption(),
            tableGroupData: {
                average: [],
                peak: [],
                total: [],
            },
        }),
        [],
    );
    const [data, setData] = useState<IData>(emptyData);
    const [currentData, setCurrentData] = useState<IMonitorTableCurrentData>();
    const firstLoadingRef = useRef<boolean>(true);
    const firstSetDataRef = useRef<boolean>(true);
    const [showTable, setShowTable] = useState(true);

    const cacheRef = useRef<Record<string, Promise<API.IApiResAdminParamonMetricModel[]>>>({});
    const getViewData = useMemoizedFn(async () => {
        if (!job.startTime && !job.walltime) {
            firstLoadingRef.current = false;
            return;
        }
        /**
         * 每次请求的最大时间跨度的毫秒值。
         * 由于Prometheus每次只能请求大概10000条数据，step的时间单位为s, maxTime最大值公式如下：
         * maxTime = 9999 * step * 1000;
         * 大概是8个小时
         */
        const maxTime = 3000 * step * 1000;
        /* 运行中的作业没有endTime，所以取systemTime, systemTime为新增加的属性，没有则用walltime计算 */
        const endTime = job.endTime || job.systemTime || job.startTime + job.walltime * 1000;
        if (endTime <= job.startTime) {
            firstLoadingRef.current = false;
            return;
        }

        const requestNum = Math.ceil((endTime - job.startTime) / maxTime);
        // 以maxTime为分页条件，分页请求数据
        const requestTasks = new Array(requestNum).fill(0).map((_, i) => {
            const start = job.startTime + i * maxTime + (i === 0 ? 0 : step * 1000);
            const end = Math.min(start + maxTime, endTime);
            const cacheKey = `${job.jobId}-${start}-${end}`;
            if (cacheRef.current[cacheKey]) {
                console.debug('MonitorCharts @getAdminParamonJobMonitor use cache', cacheKey);
                return cacheRef.current[cacheKey];
            }
            cacheRef.current[cacheKey] = getAdminParamonJobMonitor(
                {
                    jobId: job.jobId,
                    step,
                    start: new Date(start).toISOString(),
                    end: new Date(end).toISOString(),
                },
                {
                    skipErrorHandler: true,
                    signal: abortControllerRef.current.signal,
                },
            );
            return cacheRef.current[cacheKey];
        });
        const responses = await Promise.all(requestTasks);
        firstLoadingRef.current = false;

        if (responses[0].length < 1) {
            return;
        }

        const nodesSet: Set<string> = new Set();
        const tempMap: Record<string, IIndicatorData> = {};
        let yAxisMax = 100;

        responses.forEach((response) => {
            response.forEach((item) => {
                const nodename = item.metric.instance;
                if (!nodesSet.has(nodename)) {
                    nodesSet.add(nodename);
                }

                const config = indicatorConfigRecord[item.metric.type];
                if (!config) return;

                // 处理数值单位，并返回最大值
                const reduceCallback =
                    // @ts-ignore
                    item.processed === true
                        ? function getMax(curMax: number, item: [number, string]) {
                              // @ts-ignore
                              return Math.max(curMax, item[1]);
                          }
                        : config.valueProcessor;
                let max = Math.ceil(item.values.reduce(reduceCallback, 0));
                Math.ceil(
                    item.values.reduce(function getMax(curMax: number, item: [number, string]) {
                        // @ts-ignore
                        return Math.max(curMax, item[1]);
                    }, 0),
                );

                if (config.yAxisStep) {
                    // 优化Y轴最大值
                    max = max + config.yAxisStep - (max % config.yAxisStep);
                }
                yAxisMax = Math.max(max, yAxisMax);

                if (
                    config.xAxisNagative &&
                    // @ts-ignore
                    item.processed !== true
                ) {
                    item.values.forEach((item) => {
                        // @ts-ignore
                        item[1] = -item[1];
                    });
                }

                let tempMapItem = tempMap[item.metric.type];
                if (!tempMapItem) {
                    tempMapItem = {
                        name: config.title,
                        color: config.color,
                        yAxisIndex: config.yAxisIndex,
                        dataMap: {},
                    };
                    tempMap[item.metric.type] = tempMapItem;
                }

                if (!tempMapItem.dataMap[nodename]) {
                    tempMapItem.dataMap[nodename] = item.values;
                } else {
                    tempMapItem.dataMap[nodename] = tempMapItem.dataMap[nodename].concat(
                        item.values,
                    );
                }
                // @ts-ignore
                item.processed = true;
            });
        });

        // pbs头节点，slurm目前不支持
        let headNode = job.nodes.split('+')[0];
        if (nodesSet.has(headNode)) {
            nodesSet.delete(headNode);
        } else {
            // 头节点计算有误，如slurm
            headNode = null;
        }
        const nodes = Array.from(nodesSet);
        nodes.sort();
        if (headNode) {
            nodes.unshift(headNode);
        }
        const tempDatas = Object.keys(indicatorConfigRecord).map((key) => {
            return (
                tempMap[key] || {
                    name: indicatorConfigRecord[key].title,
                    color: indicatorConfigRecord[key].color,
                    yAxisIndex: indicatorConfigRecord[key].yAxisIndex,
                    dataMap: {},
                }
            );
        });
        tempMapRef.current = tempMap;
        tempDatasRef.current = tempDatas;
        nodesRef.current = nodes;
        yAxisMaxRef.current = yAxisMax;
        if (!nodesSelectedRef.current) {
            nodesSelectedRef.current = nodes;
        }

        let preOption;
        if (echartRef.current) {
            preOption = echartRef.current.getOption();
        }

        setData({
            echartsOption:
                nodesSelectedRef.current.length == 0
                    ? emptyData.echartsOption
                    : getEchartsOption(
                          nodesSelectedRef.current,
                          tempDatas,
                          echartSizeRef.current.height,
                          yAxisMax,
                          preOption,
                      ),
            tableGroupData: getTableGroupData(nodes, tempMap),
        });
        if (firstSetDataRef.current) {
            setSelectedNodes(nodesSelectedRef.current);
            setCurrentData(getTableCurrentData(nodes, tempMap, -1));
            firstSetDataRef.current = false;
        }
    });

    const { run, cancel } = useRequest(getViewData, {
        manual: true,
        pollingInterval: isJobEnd(job.jobStatus) ? 0 : step * 1000,
    });

    useEffect(() => {
        const controller = abortControllerRef.current;
        if (!controller || controller.signal.aborted) {
            abortControllerRef.current = new AbortController();
        }
        run();

        return () => {
            cancel();
            abortControllerRef.current.abort();
        };
    }, [step]);

    const [isFullScreen, { toggleFullscreen }] = useFullscreen(rootElRef);

    function handleChartInit(chart: echarts.ECharts) {
        echartRef.current = chart;
        let dataIndex = -1;
        chart.on('highlight', (evt: HighlightPayload) => {
            if (evt.batch && evt.batch[0] && typeof evt.batch[0].dataIndex === 'number') {
                dataIndex = evt.batch[0].dataIndex;
            } else {
                dataIndex = -1;
            }
        });
        chart.getDom().addEventListener('click', () => {
            console.debug('MonitorCharts @handleChartInit click', dataIndex);
            if (dataIndex === -1) return;

            setCurrentData(getTableCurrentData(nodesRef.current, tempMapRef.current, dataIndex));
        });
    }

    function handleChartResize(width: number, height: number) {
        if (!echartRef.current) return;
        if (!echartSizeRef.current) {
            echartSizeRef.current = { width, height };
            return;
        }

        echartSizeRef.current.width = width;
        if (height === echartSizeRef.current.height) return;
        echartSizeRef.current.height = height;

        if (!nodesSelectedRef.current || !tempDatasRef.current) return;

        let preOption;
        if (echartRef.current) {
            preOption = echartRef.current.getOption();
        }
        echartRef.current.setOption(
            getEchartsOption(
                nodesSelectedRef.current,
                tempDatasRef.current,
                echartSizeRef.current.height,
                yAxisMaxRef.current,
                preOption,
            ),
            true,
        );
    }

    function handleSelectedNodeChange(selectedNodes: string[]) {
        const sortedSelectedNodes = nodesRef.current.filter((nodename) =>
            selectedNodes.includes(nodename),
        );
        setSelectedNodes(sortedSelectedNodes);
        nodesSelectedRef.current = sortedSelectedNodes;

        if (!echartRef.current) return;
        if (!echartSizeRef.current) return;

        if (sortedSelectedNodes.length === 0) {
            setData((data) => {
                return {
                    ...data,
                    echartsOption: emptyData.echartsOption,
                };
            });
        } else {
            setData((data) => ({
                ...data,
                echartsOption: getEchartsOption(
                    sortedSelectedNodes,
                    tempDatasRef.current,
                    echartSizeRef.current.height,
                    yAxisMaxRef.current,
                ),
            }));
        }
    }

    return (
        <div
            className="jobOutputDetailsMonitorCharts"
            ref={rootElRef}
            style={{
                padding: isFullScreen ? 20 : 0,
            }}
        >
            <Spin size="large" spinning={firstLoadingRef.current}>
                {/* <div className="controller">
                    <div className="controller-left">
                        <span>
                            {intl.formatMessage({ id: 'pages.jobResourceMonitor.chart_ctl_nodes' })}
                            ：
                        </span>
                        {data.nodes.map((nodename) => (
                            <Checkbox
                                key={nodename}
                                defaultChecked={true}
                                data-value={nodename}
                                onChange={(evt: CheckboxChangeEvent) => {
                                    const nodename = evt.target['data-value'];
                                    const checked = evt.target.checked;
                            
                                    const set = new Set(nodesSelectedRef.current);
                                    if (checked) {
                                        set.add(nodename);
                                    } else {
                                        set.delete(nodename);
                                    }

                                    handleSelectedNodeChange(Array.from(set));
                                }}
                            >
                                <span>{nodename}</span>
                            </Checkbox>
                        ))}
                        {noData && intl.formatMessage({ id: 'pages.monitorsJobs.noData' })}
                    </div>
                </div> */}
                <div className={`${showTable ? 'controller-right-float' : 'controller-right'}`}>
                    <Tooltip
                        title={intl.formatMessage({
                            id: 'pages.jobResourceMonitor.chart_ctl_table',
                        })}
                    >
                        <a onClick={() => setShowTable(!showTable)}>
                            {showTable ? <MinusSquareOutlined /> : <PlusSquareOutlined />}
                        </a>
                    </Tooltip>
                    <Tooltip
                        title={intl.formatMessage({
                            id: 'component.fullscreen',
                        })}
                    >
                        <a onClick={toggleFullscreen}>
                            {isFullScreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                        </a>
                    </Tooltip>
                    {/* <span>{intl.formatMessage({ id: 'pages.jobResourceMonitor.chart_ctl_steps'})}：</span>
                    <Select
                        style={{ width: '110px' }}
                        value={step}
                        onChange={(item) => setStep(item)}
                        options={stepOptions}
                    /> */}
                </div>
                {showTable && (
                    <MonitorTables
                        contextmenuContainerElRef={contextmenuContainerElRef}
                        data={data.tableGroupData}
                        currentData={currentData}
                        selectedNodes={selectedNodes}
                        onSelectedNodesChange={handleSelectedNodeChange}
                    />
                )}
                <Echarts
                    className="jobMonitorChart"
                    option={data.echartsOption}
                    onChartInit={handleChartInit}
                    onResize={handleChartResize}
                />
            </Spin>
            <div
                ref={contextmenuContainerElRef}
                className="jobOutputDetailsMonitorChartsContextmenuContainer"
            ></div>
        </div>
    );
}
