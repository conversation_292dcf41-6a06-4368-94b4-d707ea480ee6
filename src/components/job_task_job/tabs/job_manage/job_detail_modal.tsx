import { useEffect, useState } from 'react';

import { ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Modal } from 'antd';
import dayjs from 'dayjs';

import { cpuTimeFormatter, flowFormatter } from '@/utils/indicator_formatter';
import { thousandthDivision } from '@/utils/utils';
import { createBillingTypeValueEnum } from '@/valueEnum/billing_type';
import { createJobStatusValueEnum } from '@/valueEnum/job_status';
import { createJobSyncStatusValueEnum } from '@/valueEnum/job_sync_status';

import type { IJobActions } from './types';
import { useZonesCache } from '@/components/resource_zone_manage/use_zone_cache';
import { createJobSecurityLevel } from '@/valueEnum/job_security_level';

const { Item } = ProDescriptions;

function totalSpeedTransformer(speed: number): string {
    let showTotalSpeed = flowFormatter(speed);
    showTotalSpeed = showTotalSpeed == 'N/A' ? showTotalSpeed : showTotalSpeed + '/s';

    return showTotalSpeed;
}

export default function JobDetailModal({ action }: { action: IJobActions }) {
    const intl = useIntl();
    const [visible, setVisible] = useState<boolean>(false);
    const [jobData, setJobData] = useState<API.IApiResJobTaskModel>();
    const [ZoneMap, setZoneMap] = useState<Map<string, string>>();
    const [data] = useZonesCache();

    useEffect(() => {
        const _ZoneMap = new Map<string, string>();
        data.forEach((v) => {
            _ZoneMap.set(v.zoneCode, v.zoneName);
        });
        setZoneMap(_ZoneMap);
    }, [data]);

    if (action) {
        Object.assign(action, {
            toggleModal: setVisible,
            openModal: (data: API.IApiResJobTaskModel) => {
                setJobData(data);
                setVisible(true);
            },
        });
    }

    return (
        <Modal
            visible={visible}
            title={intl.formatMessage({ id: 'pages.job.jobDetails' })}
            width="70%"
            onCancel={() => {
                setVisible(false);
            }}
            onOk={() => {
                setVisible(false);
            }}
        >
            <ProDescriptions bordered size="small">
                <Item label={intl.formatMessage({ id: 'pages.job.jobId' })}>
                    {jobData?.jobId ? jobData?.jobId : '-'}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.jobNumber' })}>
                    {jobData?.rawJobId ? jobData?.rawJobId : '-'}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.billing.resourceZoon' })}>
                    {jobData?.zone ? ZoneMap?.get(jobData?.zone) : '-'}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.jobName' })}>
                    {jobData?.jobName}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.batchId' })}>
                    {jobData?.batchId}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.batchName' })}>
                    {jobData?.batchName}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.batchNo' })}>
                    {jobData?.batchNo}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.uid' })}>{jobData?.uid}</Item>
                <Item label={intl.formatMessage({ id: 'pages.appScript.queue' })}>
                    {jobData?.queue}
                </Item>
                <Item
                    label={intl.formatMessage({ id: 'pages.job.jobStatus' })}
                    valueEnum={createJobStatusValueEnum()}
                >
                    {jobData?.jobStatus}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.jobType' })}>
                    {jobData?.appSubmitType === 'VIEW'
                        ? intl.formatMessage({ id: 'pages.job.type.viewJob' })
                        : jobData?.isBatch
                          ? intl.formatMessage({ id: 'pages.job.type.batchJob' })
                          : jobData?.jobType === 'GUI'
                            ? intl.formatMessage({ id: 'pages.job.type.guiJob' })
                            : intl.formatMessage({ id: 'pages.job.type.cmdJob' })}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.appCode' })}>
                    {jobData?.appCode}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.billing.softwareVersion' })}>
                    {jobData?.version}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.totalOutputFileCount' })}>
                    {jobData?.totalOutputFileCount}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.softwareModule' })}>
                    {jobData?.softwareModule}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.calculationAccuracy' })}>
                    {jobData?.calculationAccuracy}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.mpi' })}>{jobData?.mpi}</Item>
                <Item label={intl.formatMessage({ id: 'pages.machineHours.project' })}>
                    {jobData?.projectName}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.createdDate' })}>
                    {dayjs(jobData?.createdDate).format('YYYY-MM-DD HH:mm:ss')}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.uploadStartDate' })}>
                    {dayjs(jobData?.uploadStartDate).format('YYYY-MM-DD HH:mm:ss')}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.submitTime' })}>
                    {dayjs(jobData?.submitTime).format('YYYY-MM-DD HH:mm:ss')}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.executionTime' })}>
                    {jobData?.executionTime
                        ? dayjs(jobData?.executionTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-'}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.startTime' })}>
                    {dayjs(jobData?.startTime).format('YYYY-MM-DD HH:mm:ss')}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.endTime' })}>
                    {dayjs(jobData?.endTime).format('YYYY-MM-DD HH:mm:ss')}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.slots' })}>{jobData?.slots}</Item>
                <Item label={intl.formatMessage({ id: 'pages.job.walltime' })}>
                    {jobData?.walltime ? cpuTimeFormatter(jobData?.walltime) : ''}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.billing.jobCheckTime' })}>
                    {((jobData?.cpuTime || 0) / (60 * 60)).toFixed(2)}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.billing.queuingTime' })}>
                    {jobData?.pendingTime
                        ? cpuTimeFormatter(Math.floor(jobData?.pendingTime / 1000))
                        : ''}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.pendingOrder' })}>
                    {jobData?.pendingOrder}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.pendingSlots' })}>
                    {jobData?.pendingSlots}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.nodesName' })}>
                    {jobData?.nodes}
                </Item>
                <Item
                    label={intl.formatMessage({ id: 'pages.job.billingType' })}
                    valueEnum={createBillingTypeValueEnum()}
                >
                    {jobData?.billingType}
                </Item>
                <Item
                    label={intl.formatMessage({ id: 'pages.job.showSyncStatus' })}
                    valueEnum={createJobSyncStatusValueEnum()}
                >
                    {jobData?.showSyncStatus}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.totalSpeed' })}>
                    {jobData?.totalSpeed}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.percentage' })}>
                    {jobData?.percentage}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.uploadTime' })}>
                    {jobData?.uploadTime
                        ? cpuTimeFormatter(Math.floor(jobData?.uploadTime / 1000))
                        : ''}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.downloadTime' })}>
                    {jobData?.downloadTime
                        ? cpuTimeFormatter(Math.floor(jobData?.downloadTime / 1000))
                        : ''}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.averageUploadSpeed' })}>
                    {totalSpeedTransformer(jobData?.averageUploadSpeed || 0)}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.averageDownlondSpeed' })}>
                    {totalSpeedTransformer(jobData?.averageDownlondSpeed || 0)}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.jobTag.totalInputFileSize' })}>
                    {thousandthDivision((jobData?.totalInputFileSize || 0) / 1024 / 1024)}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.jobTag.totalOutputFileSize' })}>
                    {thousandthDivision((jobData?.totalOutputFileSize || 0) / 1024 / 1024)}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.jobTag.totalSyncFileSize' })}>
                    {thousandthDivision((jobData?.totalSyncFileSize || 0) / 1024 / 1024)}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.uploadPendingTime' })}>
                    {jobData?.uploadPendingTime
                        ? cpuTimeFormatter(Math.floor(jobData?.uploadPendingTime / 1000))
                        : ''}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.downlondPendingTime' })}>
                    {jobData?.downlondPendingTime
                        ? cpuTimeFormatter(Math.floor(jobData?.downlondPendingTime / 1000))
                        : ''}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.localPath' })}>
                    {typeof jobData?.localPath === 'string'
                        ? jobData?.localPath.split(':').pop()
                        : '-'}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.outPutPathTmp' })}>
                    {typeof jobData?.outPutPathTmp === 'string'
                        ? jobData?.outPutPathTmp.split(':').pop()
                        : '-'}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.billing.queueReason' })}>
                    {jobData?.pendReason}
                </Item>
                <Item
                    label={intl.formatMessage({ id: 'pages.log.jobSecurityLevel' })}
                    valueEnum={createJobSecurityLevel(intl)}
                >
                    {jobData?.jobSecurityLevel}
                </Item>
                <Item
                    label={intl.formatMessage({ id: 'pages.appealManage.appealStatus' })}
                    valueEnum={{
                        REQUESTED: {
                            text: intl.formatMessage({
                                id: 'pages.appealManage.appealStatus.requested',
                            }),
                        },
                        NO_APPEAL: {
                            text: intl.formatMessage({
                                id: 'pages.appealManage.appealStatus.noAppeal',
                            }),
                        },
                        IN_COMPLAINT: {
                            text: intl.formatMessage({
                                id: 'pages.appealManage.appealStatus.inComplaint',
                            }),
                        },
                        APPEALED: {
                            text: intl.formatMessage({
                                id: 'pages.appealManage.appealStatus.appealed',
                            }),
                        },
                        NOT_APPROVED: {
                            text: intl.formatMessage({
                                id: 'pages.appealManage.appealStatus.notApproved',
                            }),
                        },
                        VOUCHER_RETURNED: {
                            text: intl.formatMessage({
                                id: 'pages.appealManage.appealStatus.voucherReturned',
                            }),
                        },
                    }}
                >
                    {jobData?.jobAppealStatusType}
                </Item>
                <Item label={intl.formatMessage({ id: 'component.remarks' })}>
                    {jobData?.jobDesc}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.exitCode' })}>
                    {jobData?.exitCode}
                </Item>
                <Item label={intl.formatMessage({ id: 'pages.job.reason' })}>
                    {jobData?.reason}
                </Item>
            </ProDescriptions>
        </Modal>
    );
}
