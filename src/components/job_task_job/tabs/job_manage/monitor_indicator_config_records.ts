import {
    CPU_USAGE,
    GFLOPS,
    MEMORY_USAGE,
    MEMORY_RW,
    IB_RECEIVE,
    IB_SEND,
    NET_RECEIVE,
    NET_SEND,
    DISK_READ,
    DISK_WRITE,
} from './monitor_color';

type IIndicatorKey = Exclude<
    API.IApiResAdminParamonMetricModel['metric']['type'],
    'memthr' | 'memthw'
>;

interface IIndicatorConfig {
    title: string;
    /** 数据线条颜色 */
    color: string;
    /** 是否显示在负数轴 */
    xAxisNagative?: boolean;
    /** Y轴递增的步数 */
    yAxisStep?: number;
    /** Y轴索引，0为左边%轴，1为右边数值轴 */
    yAxisIndex: 0 | 1;
    /** tabl中列的宽度 */
    columnWidth: number;
    /** 处理数据，转换单位，并返回最大值 */
    valueProcessor: (curMax: number, item: [number, string]) => number;
}

function mbValuesProcess(curMax: number, item: [number, string]) {
    // 秒数到时间值转为毫秒
    item[0] *= 1000;
    // 单位由Byte转换为MB
    // @ts-ignore
    item[1] = parseInt(item[1]) / 1048576;
    // @ts-ignore
    return Math.max(curMax, item[1]);
}

function gbValuesProcess(curMax: number, item: [number, string]) {
    // 秒数到时间值转为毫秒
    item[0] *= 1000;
    // 单位由Byte转换为GB
    // @ts-ignore
    item[1] = parseInt(item[1]) / 1073741824;
    // @ts-ignore
    return Math.max(curMax, item[1]);
}

function usageValueProcess(_curMax: number, item: [number, string]) {
    // 秒数到时间值转为毫秒
    item[0] *= 1000;
    // @ts-ignore
    item[1] = parseFloat(item[1]);
    return 100;
}

export const indicatorConfigRecord: Record<IIndicatorKey, IIndicatorConfig> = {
    cpuutil: {
        title: 'CPU(all)%',
        color: CPU_USAGE,
        yAxisIndex: 0,
        columnWidth: 100,
        valueProcessor: usageValueProcess,
    },
    gflops: {
        title: 'Gflops',
        color: GFLOPS,
        yAxisStep: 10,
        yAxisIndex: 1,
        columnWidth: 100,
        valueProcessor: gbValuesProcess,
    },
    memutil: {
        title: 'Memory%',
        color: MEMORY_USAGE,
        yAxisIndex: 0,
        columnWidth: 100,
        valueProcessor: usageValueProcess,
    },
    memthrw: {
        title: 'MemRW (GB/s)',
        color: MEMORY_RW,
        yAxisStep: 50,
        yAxisIndex: 1,
        columnWidth: 150,
        valueProcessor: gbValuesProcess,
    },
    ibrecei: {
        title: 'IB Recv (MB/s)',
        color: IB_RECEIVE,
        xAxisNagative: true,
        yAxisStep: 100,
        yAxisIndex: 1,
        columnWidth: 120,
        valueProcessor: mbValuesProcess,
    },
    ibtrans: {
        title: 'IB Send (MB/s)',
        color: IB_SEND,
        yAxisStep: 100,
        yAxisIndex: 1,
        columnWidth: 120,
        valueProcessor: mbValuesProcess,
    },
    ethrecei: {
        title: 'Net Recv (MB/s)',
        color: NET_RECEIVE,
        xAxisNagative: true,
        yAxisStep: 50,
        yAxisIndex: 1,
        columnWidth: 120,
        valueProcessor: mbValuesProcess,
    },
    ethtrans: {
        title: 'Net Send (MB/s)',
        color: NET_SEND,
        yAxisStep: 50,
        yAxisIndex: 1,
        columnWidth: 120,
        valueProcessor: mbValuesProcess,
    },
    diskrw: {
        title: 'Disk Write (MB/s)',
        color: DISK_WRITE,
        xAxisNagative: true,
        yAxisStep: 50,
        yAxisIndex: 1,
        columnWidth: 120,
        valueProcessor: mbValuesProcess,
    },
    diskro: {
        title: 'Disk Read (MB/s)',
        color: DISK_READ,
        yAxisStep: 50,
        yAxisIndex: 1,
        columnWidth: 120,
        valueProcessor: mbValuesProcess,
    },
};
