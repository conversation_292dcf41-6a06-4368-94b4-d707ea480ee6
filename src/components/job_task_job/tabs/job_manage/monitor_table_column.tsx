import { getIntl, getLocale } from '@umijs/max';
import { indicatorConfigRecord } from './monitor_indicator_config_records';

import type { ColumnType } from 'antd/es/table';

const intl = getIntl(getLocale());

export default function getColumns(filterColKeys?: string[]) {
    const columns: ColumnType<any>[] = [
        {
            // 节点
            title: intl.formatMessage({ id: 'pages.job.nodesName' }),
            align: 'left',
            dataIndex: 'nodename',
            key: 'nodename',
            width: '90px',
        },
    ];
    Object.keys(indicatorConfigRecord).forEach((key: keyof typeof indicatorConfigRecord) => {
        const config = indicatorConfigRecord[key];
        columns.push({
            title: config.title,
            align: 'right',
            dataIndex: key,
            key: key,
            width: `${config.columnWidth || 100}px`,
            render: (text) => <span style={{ color: config.color }}>{text}</span>,
        });
    });
    if (filterColKeys) {
        return columns.filter((column) => !filterColKeys.includes(column.key as string));
    }
    return columns;
}
