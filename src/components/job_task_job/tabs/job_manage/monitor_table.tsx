import { Table, TableProps } from 'antd';
import { useMemo, useState } from 'react';
import getColumns from './monitor_table_column';
import useAntdTableHeaderContextMenu from '@/components/contextmenu_table_header/useAntdTableHeaderContextMenu';

import type { ColumnType } from 'antd/es/table';

type ITableData = Record<API.IApiResAdminParamonMetricModel['metric']['type'], number> & {
    nodename: string;
};

export default function MonitorTable({
    contextmenuContainerElRef,
    filterColKeys,
    dataSource,
    selectedNodes,
    onSelectedNodesChange,
    ...rest
}: {
    contextmenuContainerElRef: React.MutableRefObject<HTMLDivElement>;
    filterColKeys?: string[];
    dataSource: ITableData[];
    selectedNodes: string[];
    onSelectedNodesChange: (nodes: string[]) => void;
} & Omit<TableProps<ITableData>, 'columns' | 'dataSource' | 'components'>) {
    const rowSelection: TableProps<ITableData>['rowSelection'] = {
        type: 'checkbox',
        selectedRowKeys: selectedNodes,
        onChange: (selectedRowKeys: string[]) => {
            onSelectedNodesChange(selectedRowKeys);
        },
        getCheckboxProps: (record: ITableData) => ({
            disabled: record.nodename === 'Avg.',
            name: record.nodename,
        }),
    };
    const [columns, setColumns] = useState<ColumnType<any>[]>(() => getColumns(filterColKeys));
    const contextMenuHandle = useAntdTableHeaderContextMenu({
        columns,
        setColumns,
        enableOrderColumn: true,
    });

    const components = useMemo(() => {
        return {
            header: {
                row: (props: any) => {
                    return (
                        <tr
                            {...props}
                            onContextMenu={(evt) => {
                                evt.preventDefault();
                                evt.stopPropagation();
                                contextMenuHandle(evt, contextmenuContainerElRef.current);
                            }}
                        />
                    );
                },
            },
        };
    }, []);

    return (
        <Table
            size="small"
            tableLayout="fixed"
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            rowKey="nodename"
            rowSelection={rowSelection}
            components={components}
            {...rest}
        />
    );
}
