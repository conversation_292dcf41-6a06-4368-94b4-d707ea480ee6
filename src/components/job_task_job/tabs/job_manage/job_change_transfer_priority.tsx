import { useState } from 'react';
import { useIntl } from '@umijs/max';
import { Form, Modal, Select } from 'antd';
import { putAdminJobTransferPriority } from '@/services/api';

import type { IJobActions } from './types';

export default function JobChangePriority({ action }: { action?: IJobActions }) {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    if (action && !action.toggleModal) {
        Object.assign(action, {
            toggleModal: setOpen,
            openModal: function openModal(job: API.IApiResJobTaskModel) {
                setOpen(true);
                form.setFieldsValue({
                    jobId: job.jobId,
                    transferPriority:
                        job.status === 'UPLOAD_PEND'
                            ? job.uploadTransferPriority
                            : job.downloadTransferPriority,
                });
            },
        });
    }

    if (!open) {
        return null;
    }

    return (
        <Modal
            open={open}
            title={intl.formatMessage({ id: 'pages.job.changeTransferPriority' })}
            onCancel={() => {
                setOpen(false);
            }}
            onOk={() => {
                const { jobId, transferPriority } = form.getFieldsValue();
                putAdminJobTransferPriority(jobId, transferPriority).finally(() => {
                    form.resetFields();
                    setOpen(false);
                });
            }}
        >
            <Form form={form} name="control-hooks">
                <Form.Item
                    hidden
                    name="jobId"
                    label="jobId"
                    rules={[
                        {
                            required: true,
                            message: intl.formatMessage({ id: 'pages.job.lackOfJobId' }),
                        },
                    ]}
                >
                    <input />
                </Form.Item>
                <Form.Item
                    name="transferPriority"
                    label={intl.formatMessage({ id: 'pages.job.jobTransferPriority' })}
                    rules={[{ required: true }]}
                >
                    <Select>
                        <Select.Option value="NORMAL">
                            {intl.formatMessage({
                                id: 'pages.job.jobManualTransferPriority.normal',
                            })}
                        </Select.Option>
                        <Select.Option value="URGENT">
                            {intl.formatMessage({
                                id: 'pages.job.jobManualTransferPriority.urgent',
                            })}
                        </Select.Option>
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );
}
