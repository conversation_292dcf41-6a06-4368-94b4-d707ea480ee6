import { useState } from 'react';
import { useIntl } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Modal } from 'antd';

import MonitorCharts from './monitor_charts';

import { getJob } from '@/services/api';
import { isJobEnd } from './utils';

import type { IJobActions } from './types';

export default function MonitorChartsModal({ action }: { action: IJobActions }) {
    const intl = useIntl();
    const [visible, setVisible] = useState<boolean>(false);
    const [jobData, setJobData] = useState<API.IApiResJobTaskModel>();
    async function requestJobDetail(): Promise<API.IApiResJobTaskModel> {
        const job = await getJob(jobData.jobId);
        setJobData(job);
        return job;
    }

    const { data } = useRequest(requestJobDetail, {
        ready: visible,
        pollingInterval: jobData && isJobEnd(jobData.jobStatus) ? 0 : 3000,
    });

    if (action) {
        Object.assign(action, {
            toggleModal: setVisible,
            openModal: (data: API.IApiResJobTaskModel) => {
                setJobData(data);
                setVisible(true);
            },
        });
    }

    if (!visible) return null;

    return (
        <Modal
            open={visible}
            title={intl.formatMessage({ id: 'pages.jobResourceMonitor.resource_monitor' })}
            className="jobOutputDetailsMonitorChartsModal"
            width="90%"
            height="80%"
            footer={null}
            maskClosable={false}
            onCancel={() => setVisible(false)}
        >
            {data && <MonitorCharts job={data} />}
        </Modal>
    );
}
