import { useIntl } from '@umijs/max';
import { Table, Tabs } from 'antd';
import { useMemo } from 'react';
import MonitorTable from './monitor_table';

type ITableData = Record<API.IApiResAdminParamonMetricModel['metric']['type'], number> & {
    nodename: string;
};

export type IMonitorTableGroupData = {
    average: ITableData[];
    peak: ITableData[];
    total: ITableData[];
};

export type IMonitorTableCurrentData = {
    data: ITableData[];
    time?: string;
};

export default function MonitorTables({
    contextmenuContainerElRef,
    data,
    currentData,
    selectedNodes,
    onSelectedNodesChange,
}: {
    contextmenuContainerElRef: React.MutableRefObject<HTMLDivElement>;
    data: IMonitorTableGroupData;
    currentData?: IMonitorTableCurrentData;
    selectedNodes: string[];
    onSelectedNodesChange: (nodes: string[]) => void;
}) {
    const intl = useIntl();
    const tabItems = useMemo(() => {
        return [
            {
                label: intl.formatMessage({ id: 'pages.jobResourceMonitor.table_average' }),
                key: 'average',
                children: (
                    <MonitorTable
                        contextmenuContainerElRef={contextmenuContainerElRef}
                        dataSource={data.average}
                        selectedNodes={selectedNodes}
                        onSelectedNodesChange={onSelectedNodesChange}
                    />
                ),
            },
            {
                label: intl.formatMessage({ id: 'pages.jobResourceMonitor.table_peak' }),
                key: 'peak',
                children: (
                    <MonitorTable
                        contextmenuContainerElRef={contextmenuContainerElRef}
                        dataSource={data.peak}
                        selectedNodes={selectedNodes}
                        onSelectedNodesChange={onSelectedNodesChange}
                    />
                ),
            },
            {
                label: intl.formatMessage({ id: 'pages.jobResourceMonitor.table_total' }),
                key: 'total',
                children: (
                    <MonitorTable
                        contextmenuContainerElRef={contextmenuContainerElRef}
                        filterColKeys={['cpuutil', 'memutil']}
                        dataSource={data.total}
                        selectedNodes={selectedNodes}
                        onSelectedNodesChange={onSelectedNodesChange}
                    />
                ),
            },
            {
                label: intl.formatMessage({ id: 'pages.jobResourceMonitor.table_current' }),
                key: 'current',
                children: (
                    <MonitorTable
                        contextmenuContainerElRef={contextmenuContainerElRef}
                        dataSource={currentData?.data}
                        selectedNodes={selectedNodes}
                        onSelectedNodesChange={onSelectedNodesChange}
                        summary={() =>
                            currentData?.time ? (
                                <Table.Summary>
                                    <Table.Summary.Row>
                                        <Table.Summary.Cell index={0}></Table.Summary.Cell>
                                        <Table.Summary.Cell index={1} colSpan={11}>
                                            {intl.formatMessage({
                                                id: 'pages.jobResourceMonitor.table_current_time',
                                            })}
                                            ：{currentData?.time}
                                        </Table.Summary.Cell>
                                    </Table.Summary.Row>
                                </Table.Summary>
                            ) : null
                        }
                    />
                ),
            },
        ];
    }, [data, selectedNodes, currentData]);

    return (
        <Tabs className="jobOutputDetailsMonitorTables" type="card" size="small" items={tabItems} />
    );
}
