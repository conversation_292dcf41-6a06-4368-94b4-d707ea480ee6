import {
    deleteAssignProjectResources,
    getProjectResourcesAssigned,
} from '@/services/api/admin_resource_queue_resource';
import SearchTablePage from '../search_table_page';
import { Button, Modal } from 'antd';
import AssignProjectModal from './assign_project_modal';
import { useRef } from 'react';
import type { IAssignProjectModalActions } from './types';
import { useIntl } from '@umijs/max';
import { ActionType } from '@ant-design/pro-components';

const AssignProjectTable: React.FC<{ queue?: API.IApiResQueueModel }> = ({ queue }) => {
    const intl = useIntl();
    const tableActionsRef = useRef<ActionType>();
    const modalActionsRef = useRef<IAssignProjectModalActions>({
        assignToProject: {},
        tableActionsRef,
    });

    if (!queue) {
        return null;
    }

    return (
        <>
            <SearchTablePage
                searchProps={false}
                tableProps={{
                    actionRef: tableActionsRef,
                    pagination: false,
                    headerTitle: intl.formatMessage({ id: 'pages.resourceQueue.assignProject' }),
                    toolBarRender: () => {
                        return [
                            <Button
                                type="primary"
                                onClick={() =>
                                    modalActionsRef.current.assignToProject.openModal?.(queue)
                                }
                            >
                                {intl.formatMessage({ id: 'pages.resourceQueue.assignProject' })}
                            </Button>,
                        ];
                    },
                    request: async () => {
                        const data = await getProjectResourcesAssigned({
                            queueResourceId: queue.id,
                        });
                        return {
                            data,
                        };
                    },
                    columns: [
                        {
                            title: intl.formatMessage({ id: 'pages.machineHours.project' }),
                            dataIndex: 'projectName',
                        },
                        // {
                        //     title: intl.formatMessage({ id: 'component.state' }),
                        //     dataIndex: 'status',
                        //     render: (text, record) => {
                        //         const color = record.status ? 'processing' : 'error';
                        //         const message = record.status
                        //             ? intl.formatMessage({ id: 'component.assigned' })
                        //             : intl.formatMessage({ id: 'component.disabled' });
                        //         return <Tag color={color}>{message}</Tag>;
                        //     },
                        // },
                        {
                            title: intl.formatMessage({ id: 'component.operate' }),
                            valueType: 'option',
                            render: (_, record) => {
                                return [
                                    <Button
                                        type="link"
                                        onClick={async () => {
                                            Modal.confirm({
                                                title: intl.formatMessage({
                                                    id: 'component.areYouSureYouWantToDeleteIt',
                                                }),
                                                onOk: async () => {
                                                    await deleteAssignProjectResources({
                                                        projectResourceIds: [record.id],
                                                    });
                                                    return await tableActionsRef.current?.reload();
                                                },
                                            });
                                        }}
                                    >
                                        {intl.formatMessage({ id: 'component.operate.delete' })}
                                    </Button>,
                                ];
                            },
                        },
                    ],
                }}
            />
            <AssignProjectModal actionsRef={modalActionsRef} />
        </>
    );
};

export default AssignProjectTable;
