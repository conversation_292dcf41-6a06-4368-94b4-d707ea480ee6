import { BetaSchemaForm, type ProFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import { IAssignProjectModalActions } from './types';
import { createAsyncProjectAssignable } from '@/utils/columns_enum';
import { useIntl } from '@umijs/max';
import { postAssignProjectResources } from '@/services/api/admin_resource_queue_resource';

const AssignProjectModal: React.FC<{ actionsRef: React.RefObject<IAssignProjectModalActions> }> = ({
    actionsRef,
}) => {
    if (!actionsRef.current) {
        return null;
    }
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [info, setInfo] = useState<API.IApiResQueueModel>();
    const formRef = useRef<ProFormInstance>();

    Object.assign(actionsRef.current, {
        assignToProject: {
            openModal: (data: API.IApiResQueueModel) => {
                setInfo(data);
                setOpen(true);
            },
        },
    });

    return (
        <BetaSchemaForm
            title={intl.formatMessage({ id: 'pages.resourceQueue.assignProject' })}
            open={open}
            layoutType="ModalForm"
            layout="horizontal"
            formRef={formRef}
            onFinish={async (values) => {
                const params = {
                    ...values,
                    queueResourceIds: [info?.id],
                };
                await postAssignProjectResources(params);
                await setOpen(false);
                return actionsRef.current?.tableActionsRef.current?.reloadAndRest?.();
            }}
            modalProps={{
                onCancel: () => {
                    setOpen(false);
                },
            }}
            columns={[
                {
                    title: intl.formatMessage({ id: 'pages.machineHours.project' }),
                    dataIndex: 'projectIds',
                    fieldProps: {
                        mode: 'multiple',
                        maxTagCount: 0,
                    },
                    request: async () =>
                        createAsyncProjectAssignable({ queueResourceId: info!.id }),
                },
            ]}
        />
    );
};

export default AssignProjectModal;
