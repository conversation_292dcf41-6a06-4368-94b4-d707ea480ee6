import { <PERSON><PERSON>, Drawer, Modal } from 'antd';
import { useIntl } from '@umijs/max';
import { useEffect, useRef, useState } from 'react';

import { IQueueSccUserActions } from './types';
import SearchTablePage from '../search_table_page';
import {
    deleteAdminQueueInfoSccUserQueueMaxCoreUpdate,
    getAdminQueueInfoSccUserQueueList,
    putAdminQueueInfoSccUserQueueMaxCoreUpdate,
} from '@/services/api/admin_queue_info';
import { ActionType, BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { getAdminSshAccountList } from '@/services/api';

export const QueueSccUserDrawer: React.FC<{ actions: IQueueSccUserActions }> = ({ actions }) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const [editSccUserOpen, setEditSccUserOpen] = useState(false);
    const [queue, setQueue] = useState<API.IApiQueueInfoModel | null>(null);
    const formRef = useRef<ProFormInstance>();
    const tableActionsRef = useRef<ActionType>();

    useEffect(() => {
        if (actions) {
            actions.openDrawer = async (row) => {
                setQueue(row);
                await setOpen(true);
            };
        }
    }, []);

    return (
        <>
            <Drawer
                destroyOnClose
                title={intl.formatMessage({ id: 'pages.billing.clusterCpusLimit' })}
                onClose={onClose}
                open={open}
                width={1200}
                zIndex={700}
            >
                <SearchTablePage
                    rightMenu
                    searchProps={false}
                    tableProps={{
                        toolBarRender: () => {
                            return [
                                <Button type="primary" onClick={() => setEditSccUserOpen(true)}>
                                    用户核心上限配置
                                </Button>,
                            ];
                        },
                        columns: [
                            {
                                title: intl.formatMessage({
                                    id: 'pages.billing.supercomputerAccount',
                                }),
                                dataIndex: 'scUser',
                            },
                            {
                                title: intl.formatMessage({ id: 'pages.billing.clusterCpusLimit' }),
                                dataIndex: 'maxUserRunResNcpus',
                            },
                            {
                                title: intl.formatMessage({ id: 'pages.billing.clusterGpusLimit' }),
                                dataIndex: 'maxUserRunResNgpus',
                            },
                        ],
                        request: async () => {
                            const data = await getAdminQueueInfoSccUserQueueList({
                                id: queue.id,
                            });
                            return {
                                data,
                                total: data.length,
                            };
                        },
                        actionRef: tableActionsRef,
                        cellActions: () => [
                            {
                                label: intl.formatMessage({ id: 'component.button.update' }),
                                key: 'update',
                                onClick: async (record) => {
                                    await setEditSccUserOpen(true);
                                    await formRef.current?.setFieldsValue({
                                        scUser: record.scUser,
                                        cpuMaxCoreLimit: record.maxUserRunResNcpus,
                                        gpuMaxCoreLimit: record.maxUserRunResNgpus,
                                    });
                                },
                            },
                            {
                                label: intl.formatMessage({ id: 'component.operate.delete' }),
                                key: 'delete',
                                onClick: (row) => {
                                    Modal.confirm({
                                        title: intl.formatMessage({
                                            id: 'component.areYouSureYouWantToDeleteIt',
                                        }),
                                        onOk: async () => {
                                            await deleteAdminQueueInfoSccUserQueueMaxCoreUpdate({
                                                id: queue.id,
                                                scUser: row.scUser,
                                            });
                                        },
                                    });
                                },
                            },
                        ],
                    }}
                />
            </Drawer>
            <BetaSchemaForm
                style={{
                    zIndex: 99999,
                }}
                layout="horizontal"
                layoutType="ModalForm"
                title={'用户核心上限配置'}
                open={editSccUserOpen}
                formRef={formRef}
                onFinish={onFinish}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'pages.billing.supercomputerAccount' }),
                        dataIndex: 'scUser',
                        valueType: 'select',
                        fieldProps: {
                            showSearch: true,
                        },
                        request: async () => {
                            const { data } = await getAdminSshAccountList({
                                size: 999999,
                            });
                            // 过滤 clusterLoginName 重复的数据
                            const uniqueData = Array.from(
                                new Map(data.map((item) => [item.clusterLoginName, item])).values(),
                            );
                            return uniqueData.map((item) => ({
                                label: item.clusterLoginName,
                                value: item.clusterLoginName,
                            }));
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.clusterCpusLimit' }),
                        dataIndex: 'cpuMaxCoreLimit',
                        width: '100%',
                        valueType: 'digit',
                    },
                    {
                        title: intl.formatMessage({ id: 'pages.billing.clusterGpusLimit' }),
                        dataIndex: 'gpuMaxCoreLimit',
                        width: '100%',
                        valueType: 'digit',
                    },
                ]}
                modalProps={{
                    destroyOnClose: true,
                    onCancel: () => {
                        reset();
                    },
                }}
            />
        </>
    );

    async function onFinish() {
        const values = await formRef.current?.validateFields();
        if (values) {
            const { scUser, cpuMaxCoreLimit, gpuMaxCoreLimit } = values;
            await putAdminQueueInfoSccUserQueueMaxCoreUpdate({
                id: queue.id,
                scUser,
                cpuMaxCoreLimit,
                gpuMaxCoreLimit,
            });
            reset();
            await tableActionsRef.current.reload();
        }
    }

    async function reset() {
        setEditSccUserOpen(false);
        await formRef.current?.resetFields();
    }

    function onClose() {
        setOpen(false);
    }
};
