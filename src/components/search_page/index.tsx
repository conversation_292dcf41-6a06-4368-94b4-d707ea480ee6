import React from 'react';

import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { BetaSchemaForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';

import styles from './index.less';

import type {
    FormInstance,
    ProFormColumnsType,
    ProFormInstance,
    SubmitterProps,
} from '@ant-design/pro-components';

function SearchPage<ParamsType extends Record<string, any>, ValueType = 'text'>({
    searchProps,
    contentRender,
    style,
}: {
    searchProps:
        | {
              formRef: React.MutableRefObject<ProFormInstance<ParamsType> | undefined>;
              columns: ProFormColumnsType<ParamsType, ValueType>[];
              onFinish: (values: ParamsType) => Promise<void>;
              onReset?: React.FormEventHandler<HTMLFormElement>;
              submitter?: SubmitterProps<{ form?: FormInstance<ParamsType> | undefined }>;
          }
        | false;
    contentRender: React.ReactNode;
    style?: {
        border?: true;
    };
}) {
    const intl = useIntl();

    return (
        <div
            className={`${style?.border && styles.search_table_page_border} ${
                styles.search_table_page
            }`}
        >
            {searchProps && (
                <div className="search_box">
                    <BetaSchemaForm
                        layout="inline"
                        layoutType="Form"
                        {...searchProps}
                        submitter={{
                            searchConfig: {
                                resetText: intl.formatMessage({ id: 'pages.monitorsJobs.reset' }),
                                submitText: intl.formatMessage({
                                    id: 'pages.billing.makeEnquiries',
                                }),
                            },
                            submitButtonProps: {
                                icon: <SearchOutlined />,
                            },
                            resetButtonProps: {
                                icon: <ReloadOutlined />,
                            },
                            ...searchProps.submitter,
                        }}
                    />
                </div>
            )}

            <div className="content_box">{contentRender}</div>
        </div>
    );
}

export default SearchPage;
