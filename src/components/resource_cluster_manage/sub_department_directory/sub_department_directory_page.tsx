import { <PERSON><PERSON>, Drawer } from 'antd';
import { useRef, useState } from 'react';
import { useIntl } from '@umijs/max';
import SearchTablePage from '@/components/search_table_page';
import { ActionType } from '@ant-design/pro-components';
import { subDepartmentDirectoryStore } from './sub_department_directory_store';
import {
    AddSubDepartmentDirectory,
    AddSubDepartmentDirectoryProps,
} from './add_sub_department_directory';

export interface SubDepartmentDirectoryPageProps {
    actions: {
        toggleModal?: (row: API.IApiResGetAdminZoneClusterDiskModel) => void;
    };
}

export const SubDepartmentDirectoryPage: React.FC<SubDepartmentDirectoryPageProps> = ({
    actions,
}) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const tableRef = useRef<ActionType>();
    const addSubDepartmentDirectoryRef = useRef<AddSubDepartmentDirectoryProps['actions']>({});
    const [cluster, setCluster] = useState<API.IApiResGetAdminZoneClusterDiskModel>();

    const { getList, deleteSubDepartmentDirectory } = subDepartmentDirectoryStore();

    Object.assign(actions, {
        toggleModal: (row: API.IApiResGetAdminZoneClusterDiskModel) => {
            setOpen(true);
            setCluster(row);
        },
    });

    return (
        <Drawer
            open={open}
            width="80%"
            title={intl.formatMessage({ id: 'component.modal.details' })}
            onClose={() => {
                setOpen(false);
            }}
        >
            <SearchTablePage
                rightMenu={true}
                tableProps={{
                    actionRef: tableRef,
                    cellActions: (record) => [
                        {
                            key: 'edit',
                            label: intl.formatMessage({ id: 'component.operate.edit' }),
                            onClick: (record) => {
                                if (!cluster?.id) return;
                                addSubDepartmentDirectoryRef.current?.setOpen?.(
                                    {
                                        ...record,
                                        zoneClusterDiskId: cluster.id,
                                        gid: record.gid,
                                    },
                                    'edit',
                                );
                            },
                        },
                        {
                            key: 'delete',
                            label: intl.formatMessage({ id: 'component.operate.delete' }),
                            onClick: async (record) => {
                                await deleteSubDepartmentDirectory({
                                    id: record.id,
                                });
                                tableRef.current?.reload();
                            },
                        },
                    ],
                    columns: [
                        {
                            title: intl.formatMessage({ id: 'pages.billing.departmentName' }),
                            dataIndex: 'groupName',
                        },
                        {
                            title: intl.formatMessage({ id: 'pages.billing.departmentRoot' }),
                            dataIndex: 'path',
                        },
                        {
                            title: intl.formatMessage({
                                id: 'pages.resourceCluster.departmentDirectory',
                            }),
                            dataIndex: 'departmentDirName',
                        },
                    ],
                    request: async () => {
                        if (!cluster?.id) {
                            return {
                                data: [],
                                success: true,
                            };
                        }
                        const res = await getList({
                            zoneClusterDiskId: cluster?.id,
                        });

                        return {
                            data: res,
                            success: true,
                        };
                    },
                    toolBarRender: () => {
                        return [
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (!cluster?.id) return;
                                    addSubDepartmentDirectoryRef.current?.setOpen?.(
                                        {
                                            departmentDirName: '',
                                            gid: 0,
                                            zoneClusterDiskId: cluster.id,
                                        },
                                        'add',
                                    );
                                }}
                            >
                                {intl.formatMessage({ id: 'component.operate.create' })}
                            </Button>,
                        ];
                    },
                    pagination: false,
                }}
                searchProps={false}
            />
            <AddSubDepartmentDirectory
                tableActions={tableRef}
                actions={addSubDepartmentDirectoryRef.current}
            />
        </Drawer>
    );
};
