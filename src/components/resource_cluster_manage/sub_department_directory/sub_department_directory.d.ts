namespace API.SubDepartmentDirectory {
    interface Request {
        zoneClusterDiskId: number;
    }
    interface Response {
        cluster: string;
        createdDate: Date;
        departmentDirName: string;
        gid: number;
        id: number;
        name: string;
        order: number;
        path: string;
        type: Type;
        updatedDate: Date;
        zone: string;
        zoneClusterDiskId: number;
    }
    export enum Type {
        BackupDir = 'BACKUP_DIR',
        DepartmentDir = 'DEPARTMENT_DIR',
        GlobalDir = 'GLOBAL_DIR',
        ShareDir = 'SHARE_DIR',
        TmpDir = 'TMP_DIR',
        WorkDir = 'WORK_DIR',
    }
}

namespace API.SubDepartmentDirectory.Update {
    interface Request {
        departmentDirName: string;
        gid: number;
        zoneClusterDiskId: number;
    }
    interface Response {
        createdDate: string;
        departmentDirName: string;
        gid: number;
        id: number;
        updatedDate: string;
        zoneClusterDiskId: number;
    }
}

namespace API.SubDepartmentDirectory.Delete {
    interface Request {
        id: number;
    }
}
