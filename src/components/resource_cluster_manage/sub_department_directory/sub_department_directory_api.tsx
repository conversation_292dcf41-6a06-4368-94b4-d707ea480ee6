import { request } from '@umijs/max';

export const subDepartmentDirectoryApi = {
    list: (params?: API.SubDepartmentDirectory.Request) => {
        return request<API.SubDepartmentDirectory.Response[]>(
            'admin/group/zone/cluster/Disk/list',
            { params },
        );
    },
    update: (data: API.SubDepartmentDirectory.Update.Request) => {
        return request<API.SubDepartmentDirectory.Response>('admin/group/zone/cluster/Disk', {
            method: 'POST',
            data,
        });
    },
    delete: (params: API.SubDepartmentDirectory.Delete.Request) => {
        return request<API.SubDepartmentDirectory.Response>('admin/group/zone/cluster/Disk', {
            method: 'DELETE',
            params,
        });
    },
    getAllGroup: () => {
        return request<API.SubDepartmentDirectory.Response[]>(
            'admin/group/zone/cluster/bind/assignable',
            {
                method: 'GET',
            },
        );
    },
};
