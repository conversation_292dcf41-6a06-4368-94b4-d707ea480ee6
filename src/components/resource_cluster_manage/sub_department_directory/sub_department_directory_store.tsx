import { subDepartmentDirectoryApi } from './sub_department_directory_api';

export const subDepartmentDirectoryStore = () => {
    const getList = async (params?: API.SubDepartmentDirectory.Request) => {
        const res = await subDepartmentDirectoryApi.list(params);
        return res;
    };
    const add = async (data: API.SubDepartmentDirectory.Update.Request) => {
        const res = await subDepartmentDirectoryApi.update(data);
        return res;
    };
    const edit = async (data: API.SubDepartmentDirectory.Update.Request) => {
        const res = await subDepartmentDirectoryApi.update(data);
        return res;
    };
    const deleteSubDepartmentDirectory = async (
        params: API.SubDepartmentDirectory.Delete.Request,
    ) => {
        return await subDepartmentDirectoryApi.delete(params);
    };

    return {
        getList,
        add,
        edit,
        deleteSubDepartmentDirectory,
    };
};
