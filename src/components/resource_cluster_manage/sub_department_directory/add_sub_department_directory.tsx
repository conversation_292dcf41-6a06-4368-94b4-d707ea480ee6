import { ActionType, BetaSchemaForm, ProFormInstance } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { subDepartmentDirectoryStore } from './sub_department_directory_store';
import GroupSelector from '@/components/admin_group_manage/group_selector';
import { useRef, useState } from 'react';
import { formItemLayout } from '@/utils/form_layout';

export interface AddSubDepartmentDirectoryProps {
    actions: {
        setOpen?: (row: API.SubDepartmentDirectory.Update.Request, type: 'add' | 'edit') => void;
    };
    tableActions: React.MutableRefObject<ActionType | undefined>;
}

export const AddSubDepartmentDirectory: React.FC<AddSubDepartmentDirectoryProps> = ({
    actions,
    tableActions,
}) => {
    const intl = useIntl();
    const [open, setOpen] = useState(false);
    const { add } = subDepartmentDirectoryStore();
    const [zoneClusterDiskId, setZoneClusterDiskId] = useState<number>();
    const [create, setCreate] = useState<'add' | 'edit'>('add');
    const formRef = useRef<ProFormInstance>();

    Object.assign(actions, {
        setOpen: async (row: API.SubDepartmentDirectory.Response, type: 'add' | 'edit') => {
            await setOpen(true);
            await setZoneClusterDiskId(row.zoneClusterDiskId);
            await setCreate(type);
            await formRef.current?.setFieldsValue(row);
        },
    });

    return (
        <BetaSchemaForm
            {...formItemLayout}
            title={
                create === 'edit'
                    ? intl.formatMessage({ id: 'component.operate.edit' })
                    : intl.formatMessage({ id: 'component.operate.create' })
            }
            open={open}
            layout="horizontal"
            layoutType="ModalForm"
            formRef={formRef}
            columns={[
                {
                    title: intl.formatMessage({ id: 'pages.billing.departmentName' }),
                    dataIndex: 'groupName',
                    readonly: true,
                    formItemProps: {
                        hidden: create === 'add',
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.billing.departmentRoot' }),
                    dataIndex: 'path',
                    readonly: true,
                    formItemProps: {
                        hidden: create === 'add',
                    },
                },
                {
                    title: intl.formatMessage({ id: 'pages.billing.departmentName' }),
                    dataIndex: 'gid',
                    formItemProps: {
                        hidden: create === 'edit',
                    },
                    renderFormItem: () => (
                        <GroupSelector
                            options={{
                                multiple: false,
                                style: {
                                    width: '100%',
                                },
                            }}
                        />
                    ),
                },
                {
                    title: intl.formatMessage({ id: 'pages.resourceCluster.departmentDirectory' }),
                    dataIndex: 'departmentDirName',
                    formItemProps: {
                        rules: [{ required: true }],
                    },
                },
            ]}
            modalProps={{
                onCancel: () => {
                    reset();
                    setOpen(false);
                },
            }}
            onFinish={async () => {
                const params = formRef.current?.getFieldsFormatValue?.();
                await add({ ...params, zoneClusterDiskId });
                reset();
                await setOpen(false);
                await tableActions.current?.reload();
            }}
        />
    );

    async function reset() {
        formRef.current.resetFields();
        setCreate('add');
    }
};
